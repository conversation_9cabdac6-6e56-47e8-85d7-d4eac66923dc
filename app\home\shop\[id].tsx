import BackIcon from "../../../assets/OrderDetails/backIcon.svg";
import BtnPlus from "../../../assets/OrderDetails/BtnPlus.svg";
import Checkbox from "expo-checkbox";
import CloseIcon from "../../../assets/icons/Close.svg";
import GStarIcon from "../../../assets/OrderDetails/star.svg";
import HeartIcon from "../../../assets/OrderDetails/hertIcon.svg";
import HeartIcon2 from "@/assets/SellerMapIcon/heart.svg";
import HeavyVehicle from "../../../assets/Heavy Vehicle Delivery.svg";
import Icon from "../../../assets/OrderDetails/-Icon.svg";
import JsonPrint from "@/core/print_call/json_print";
import LinearGradientComponent from "../../../component/LinearGradientComponent copy";
import LoadingScreen from "../../../component/LoadingScreen/LoadingScreen";
import Plus from "../../../assets/OrderDetails/Plus.svg";
import QuickDelivery from "../../../assets/Quick Delivery.svg";
import RBSheet from "react-native-raw-bottom-sheet";
import React, { forwardRef, useEffect, useRef, useState } from "react";
import Replacement from "../../../assets/Replacement.svg";
import RsIcon from "../../../assets/images/CardImages/RsIcon.svg";
import StarIcon from "@/assets/images/CardImages/stareIcon.svg";
import UploadIcon from "../../../assets/OrderDetails/uploadIcon.svg";
import VegIcon from "../../../assets/OrderDetails/Veg.svg";
import WarrantyGuaranty from "../../../assets/WarrantyGuaranty.svg";
import useCartHook from "@/hooks/useCartHook/useCartHook";
import useCartLoadActions from "@/hooks/useLoadCartDataHook/useCartLoadActions";
import useDefaultAddressHook from "@/hooks/DefaultAddress/useDefaultAddressHook";
import useGetApiDatawithParam from "../../../hooks/useGetApiDatawithParam";
import useSetApiData from "../../../hooks/useSetApiData";
import useTenStackHook2 from "@/hooks/TenStackHook2/TenStackHook2";
import useTenStackMutateD from "@/hooks/useTenStackMutateD/useTenStackMutate";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { router, useLocalSearchParams } from "expo-router";
import { useForm } from "react-hook-form";
import { CustomFiled4 } from "../../../component/CustomFiled3";
import { usePanCurrentLocation } from "../../../store";
import { FavouritesItems } from "../../../store/MenuState";
import { axiosInstance } from "@/api/axios";
import { productDetailsModel } from "@/data/model/product_model/product_details_model";
import { ProductReopsitoryImpl } from "@/data/repository/product_reopsitory_impl";
import { ApiImpl } from "@/data/source";
import { ProductUsecase } from "@/domain/usecase/product_usecase";

import {
  NewVariantComponent,
  UnpackageProductComponent,
} from "../../../component/VariantComponent";

/* eslint-disable react/display-name */

import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Image,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import {
  applyGrayscale,
  GetTagFormListobject,
} from "../../../utils/shopListPage";

const OrderDetails = () => {
  const data = useLocalSearchParams();
  const CurrentLocation = useDefaultAddressHook();
  const scrollViewRef = useRef(null);
  const {
    data: SellerData,
    isLoading,
    triggerreFresh,
  } = useGetApiDatawithParam({
    endpoint: "shop_details",
    param: {
      shop_id: data.id,
      latitude: CurrentLocation.latitude,
      longitude: CurrentLocation.longitude,
    },
  });
  return (
    <>
      {isLoading ? (
        <>
          <LoadingScreen />
        </>
      ) : (
        <>
          {/* <ScrollView ref={scrollViewRef}>
            <View className="items-center justify-center relative mt-6">
              <TouchableOpacity
                className="absolute left-[3%]"
                onPress={() => {
                  router.back();
                }}
              >
                <BackIcon />
              </TouchableOpacity>
              <View>
                <Text className="font-[600] text-[20px] leading-[30px]">
                  {SellerData?.shop_data?.shop_details?.shop_name}
                </Text>
              </View>
            </View>

            <View className="items-center justify-center flex-row mt-2">
              <View className="items-center justify-center flex-row space-x-3">
                {SellerData?.shop_data?.product_category?.map((category) => {
                  return (
                    <>
                      <View>
                        <Text>{category}</Text>
                      </View>
                      <View className="w-[8px] h-[8px] bg-[#E9EAE9] rounded-full" />
                    </>
                  );
                })}
              </View>
            </View>
            <View className="flex-row items-center justify-center mt-4 space-x-1">
              <View className="flex-row items-center bg-[#00660A] px-2 py-1 space-x-1 rounded-[4px]">
                <View>
                  <Text className="text-[#fff]">
                    {SellerData?.shop_data?.avg_rating}
                  </Text>
                </View>
                <StarIcon />
              </View>
              <View>
                <Text>{SellerData?.shop_data?.total_count}</Text>
              </View>
            </View>
            <View className="items-center justify-center flex-row mt-2">
              <View className="items-center justify-center flex-row space-x-4">
                <View className="w-[8px] h-[8px] bg-[#E9EAE9] rounded-full" />
                <View>
                  <Text>{SellerData?.shop_data?.delivery_time} minutes</Text>
                </View>
                <View className="w-[8px] h-[8px] bg-[#E9EAE9] rounded-full" />
                <View>
                  <Text>{SellerData?.shop_data?.distance} km</Text>
                </View>
              </View>
            </View>

            <View className="px-6 flex-row items-center justify-between mt-4">
              <TouchableOpacity
                onPress={() => {
                  router.push({
                    pathname: "home/sellerdetails/profile",
                    params: {
                      banner_image: SellerData?.shop_data?.shop_details
                        ?.banner_image
                        ? SellerData?.shop_data?.shop_details?.banner_image
                        : SellerData?.shop_data?.shop_details?.logo,
                      sellerImg: SellerData?.shop_data?.user_details?.image,
                      sellerName: SellerData?.shop_data?.user_details?.name,
                      phone: SellerData?.shop_data?.user_details?.phone,
                      shopName: SellerData?.shop_data?.shop_details?.shop_name,
                      type: data.from,
                      address: SellerData?.shop_data?.shop_details?.address,
                    },
                  });
                }}
                className="flex-row items-center space-x-2"
              >
                <Image
                  source={{
                    uri: SellerData?.shop_data?.user_details?.image,
                  }}
                  className={"w-[50px] h-[50px] rounded-full"}
                />
                <Text className="text-[#00660A] text-[12px] font-[500] leading-[18px]">
                  {SellerData?.shop_data?.user_details?.name}
                </Text>
              </TouchableOpacity>

              <View className="w-[8px] h-[8px] bg-[#E9EAE9] rounded-full" />
              <View className="items-center">
                <View className="flex-row items-center space-x-2 border-[1px] h-[30px] px-2 border-[#E9EAE9] rounded-[4px] bg-[#00660A]">
                  <Text className="font-[500] text-[12px] leading-[18px] font-Pop text-[#fff]">
                    {SellerData?.shop_data?.avg_rating}
                  </Text>
                  <Text className="font-[500] text-[12px] leading-[18px] font-Pop text-[#fff]">
                    Reviews
                  </Text>
                  <StarIcon />
                </View>
                <TouchableOpacity
                  onPress={() => {
                    router.push({
                      pathname: "home/sellerdetails/rating",
                      params: {
                        id: data.id,
                        avg_rating: SellerData?.shop_data?.avg_rating,
                        total_count: SellerData?.shop_data?.total_count,
                        from: data.from,
                        total_review: JSON.stringify({
                          data: SellerData?.shop_data?.total_review,
                        }),
                      },
                    });
                  }}
                >
                  <Text className="font-[400] text-[14px] leading-[21px] text-[#00660A]">
                    View Reviews
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            <View className="h-[0.6px] w-full bg-[#E9EAE9] mt-4" />

            <View className="px-4">
              <View className="my-4">
                <Text className="font-[600] text-[16px] leading-[24px]">
                  Recommended
                </Text>
                <FlatList
                  data={(SellerData as any)?.shop_data?.productList}
                  keyExtractor={({ id }) => id}
                  renderItem={({
                    item: {
                      id,
                      name,
                      description,
                      price,
                      image,
                      prd_avg_rating,
                      prd_total_count,
                    },
                    index,
                  }) => (
                    <View key={id}>
                      <ItemComponent
                        key={id}
                        id={id}
                        shop_id={data?.id}
                        name={name}
                        description={description}
                        price={price}
                        image={image}
                        prd_avg_rating={prd_avg_rating}
                        prd_total_count={prd_total_count}
                      />
                    </View>
                  )}
                />
              </View>
            </View>
          </ScrollView> */}
        </>
      )}
    </>
  );
};

export const ItemComponent = (props: any) => {
  const { control, watch } = useForm();
  const refRBSheet = useRef();
  const refProductRBSheet = useRef();
  const refProductTShirtRBSheet = useRef();
  const [itemCount, setitemCount] = useState(0);
  const [data, setdata] = useState([{ ...props.data, id: props.id }]);

  const { SetFunction: addProductTofav } = useSetApiData({
    endpoint: "add_remove_product_wishlist",
  });
  const [isFav, setisFav] = useState(null);
  const [VariantId, setVariant] = useState([]);
  const [OptionsId, setOptions] = useState([]);

  const {
    data: ProductData,
    isLoading,
    triggerreFresh,
  } = useGetApiDatawithParam({
    endpoint: "product/product_details",
    param: {
      product_id: props?.id,
    },
  });
  const { isLoading: isLoadingCart, SetFunction } = useSetApiData({
    endpoint: "product/add_to_cart",
  });
  const { isLoading: isLoadingCart2, SetFunction: addunpackageProduct } =
    useSetApiData({
      endpoint: "product/add_update_unpackage_product_cart",
    });

  const [total, setTotal] = useState(0);

  const [isOutOfStock, setOutOfStock] = useState(null);

  const Tags = [
    {
      Tag: "Veg",
      image: VegIcon,
    },
    {
      Tag: "Best Seller",
      image: VegIcon,
    },
    {
      Tag: "Featured Product",
      image: VegIcon,
    },
  ];

  useEffect(() => {
    const tagData = GetTagFormListobject(Tags);
    setOutOfStock((tagData as any).includes("Out of Stock"));
  }, [isLoading]);
  return (
    <>
      <TouchableOpacity
        disabled={true}
        activeOpacity={0.5}
        onPress={() => {
          (refProductTShirtRBSheet as any).current.open();
        }}
        className="mb-4 mt-4"
      >
        <View className="flex-row">
          <View className="mt-2 space-y-1 flex-1 ">
            <View>
              <Text className="text-[18px]">{props?.name}</Text>
            </View>

            <View className=""></View>
            <View className="flex-row items-center">
              <RsIcon fill={"#000"} />
              <Text>{props?.price}</Text>
            </View>
            <View className="flex-row items-center space-x-2">
              <GStarIcon
                fill={props?.avg_rating > 0 ? "#DBB900" : "#fff"}
                height={20}
                width={20}
              />
              <GStarIcon
                fill={props?.avg_rating > 1 ? "#DBB900" : "#fff"}
                height={20}
                width={20}
              />
              <GStarIcon
                fill={props?.avg_rating > 2 ? "#DBB900" : "#fff"}
                height={20}
                width={20}
              />
              <GStarIcon
                fill={props?.avg_rating > 3 ? "#DBB900" : "#fff"}
                height={20}
                width={20}
              />
              <GStarIcon
                fill={props?.avg_rating > 4 ? "#DBB900" : "#fff"}
                height={20}
                width={20}
              />
              <Text>{props?.prd_avg_rating} ratings</Text>
            </View>
            <View className="">
              <Text className="flex-shrink">{props?.description}</Text>
            </View>
          </View>
          <View className="items-center">
            <View className="relative">
              <TouchableOpacity
                className=""
                style={{
                  width: 100,
                  height: 100,
                }}
                onPress={() => {
                  (refProductRBSheet as any).current.open();
                }}
              >
                <Image
                  source={{
                    uri: props?.image,
                  }}
                  style={{
                    width: 100,
                    height: 100,
                    zIndex: 1,
                  }}
                />
              </TouchableOpacity>
              <TouchableOpacity
                disabled={isOutOfStock}
                onPress={() => {
                  refRBSheet?.current?.open();
                }}
                className="absolute w-full flex-row items-center space-x-3 justify-center bg-[#A4F4AC] bottom-[-10%] px-2 h-[26px] border-[1px] border-[#00660A] rounded-[4px]"
              >
                <View>
                  <Text>Add</Text>
                </View>

                <View>
                  <Plus />
                </View>
              </TouchableOpacity>
            </View>
            <View className="mt-5">
              <Text>Customisable</Text>
            </View>
          </View>
        </View>
        <TouchableOpacity
          onPress={() => {
            if (isFav === false) {
              addProductTofav({
                product_id: props?.id,
              });
              setisFav(true);
            } else if (isFav === true) {
              addProductTofav({
                product_id: props?.id,
              });
              setisFav(false);
            }
          }}
          className="mt-2"
        >
          <HeartIcon2 fill={isFav ? "#00660A" : "#ccc"} />
        </TouchableOpacity>
      </TouchableOpacity>

      {isLoading ? (
        <></>
      ) : (
        <>
          {/* Product Order Bottom Sheet */}
          <RBSheet
            ref={refRBSheet}
            customStyles={{
              container: {
                borderRadius: 20,
                height: 540,
              },
              draggableIcon: {
                backgroundColor: "#000",
              },
            }}
            customModalProps={{
              statusBarTranslucent: true,
            }}
            customAvoidingViewProps={{
              enabled: false,
            }}
          >
            <ScrollView>
              <View className="flex-1 mb-4">
                <View
                  className="px-5 py-5 flex-row justify-between"
                  style={{
                    shadowColor: "#314533",
                    shadowOffset: { width: 0, height: 4 },
                    shadowOpacity: 0.12,
                    shadowRadius: 12,
                    elevation: 6,
                    backgroundColor: "#fff",
                  }}
                >
                  <View className="flex-row items-center space-x-4">
                    <Image
                      source={{
                        uri: ProductData?.product_data?.product_details?.image,
                      }}
                      style={{ width: 60, height: 60 }}
                    />
                    <Text className="font-[600] text-[20px] leading-[30px] text-[#001A03]">
                      {ProductData?.product_data?.product_details?.name}
                    </Text>
                  </View>
                </View>
                <View className="px-4 mt-3">
                  <View>
                    <Text>Quantity</Text>
                  </View>
                  <View className="mt-2 flex-row items-center space-x-2">
                    <Text>Required</Text>
                    <View className="w-[8px] h-[8px] bg-[#E9EAE9] rounded-full" />
                    <Text>Select any 1 option</Text>
                  </View>
                </View>
                <View className="w-full h-[1px] bg-[#e2e4e2] my-4" />

                {/* Variant Components Area */}
                <View className="px-4">
                  {Boolean(
                    (ProductData as any)?.product_data?.product_details
                      ?.is_unpackage_prd,
                  ) ? (
                    <>
                      <UnpackageProductComponent
                        id={
                          (ProductData as any)?.product_data?.product_details
                            ?.id
                        }
                        image={
                          (ProductData as any)?.product_data?.product_details
                            ?.image
                        }
                        units={
                          (ProductData as any)?.product_data?.product_details
                            ?.unit
                        }
                        setTotal={setTotal}
                        amount={
                          (ProductData as any)?.product_data?.product_details
                            ?.price
                        }
                        key={
                          (ProductData as any)?.product_data?.product_details
                            ?.id
                        }
                        RefSheet={refProductRBSheet}
                        control={control}
                      />
                    </>
                  ) : (
                    <>
                      {(
                        ProductData as any
                      )?.product_data?.product_variants?.map(
                        ({ id, price, unit, image }: any) => {
                          return (
                            <>
                              <NewVariantComponent
                                id={id}
                                image={image}
                                units={unit}
                                setTotal={setTotal}
                                amount={price}
                                isActive={VariantId == id ? true : false}
                                setVariant={setVariant}
                                key={id}
                                RefSheet={refProductRBSheet}
                                control={control}
                                is_unpackage_prd={Boolean(
                                  (ProductData as any)?.product_data
                                    ?.product_details?.is_unpackage_prd,
                                )}
                              />
                            </>
                          );
                        },
                      )}
                    </>
                  )}
                </View>

                <View className="px-4">
                  {Boolean(
                    (ProductData as any)?.product_data?.product_details
                      ?.is_unpackage_prd,
                  ) ? (
                    <></>
                  ) : (
                    <>
                      <View>
                        <Text className="font-[600] text-[14px] leading-[21px]">
                          Add On
                        </Text>
                      </View>
                      <View>
                        <Text className="mt-2 font-[500] text-[14px] leading-[21px] text-[#627164]">
                          Select up to 1 options
                        </Text>
                      </View>
                      {(ProductData as any)?.product_data?.product_options?.map(
                        ({ id, name, price, unit, image }) => {
                          return (
                            <>
                              <NewAddOnComponent
                                id={id}
                                text={name}
                                amount={price}
                                setOptions={setOptions}
                                val={OptionsId}
                                image={image}
                                setTotal={setTotal}
                                RefSheet={refProductRBSheet}
                                control={control}
                                is_unpackage_prd={Boolean(
                                  ProductData?.product_data?.product_details
                                    ?.is_unpackage_prd,
                                )}
                              />
                            </>
                          );
                        },
                      )}
                    </>
                  )}
                </View>

                <View className="w-full h-[1px] bg-[#e2e4e2] mt-6" />
              </View>
            </ScrollView>
            <View className="flex-row space-x-6 items-center justify-center mt-4">
              <TouchableOpacity
                onPress={async () => {
                  await SetFunction({
                    product_id: props?.id,
                    variant_id: VariantId,
                    option_id: OptionsId,
                    quantity: itemCount,
                  }).then((val) => {
                    if ((val as any)?.status === 0) {
                      alert((val as any)?.msg);
                    }
                  });
                  if (
                    (ProductData as any)?.product_data?.product_details
                      ?.is_unpackage_prd
                  ) {
                    await addunpackageProduct({
                      product_id: props?.id,
                      unpackage_prd_qty: watch(`number${props?.id}`)
                        ? watch(`number${props?.id}`)
                        : 0,
                      unpackage_prd_unit: watch(`unit${props?.id}`)
                        ? watch(`unit${props?.id}`)
                        : "undefined", //kg,lt,grm,ml
                      quantity: watch(`number${props?.id}`)
                        ? watch(`number${props?.id}`)
                        : 0,
                    }).then((val) => {
                      if ((val as any)?.status == 0) {
                        alert((val as any)?.msg);
                      }
                    });
                  }
                  setOptions([]);
                  setVariant([]);
                  (refRBSheet as any).current.close();
                }}
                className=" flex-row items-center h-[46px] flex-1 justify-center rounded-[4px] bg-[#00660A] mx-6 my-4"
              >
                {isLoadingCart ? (
                  <>
                    <ActivityIndicator />
                  </>
                ) : (
                  <>
                    <Text className="text-[#fff] font-[400] text-[20px] leading-[30]">
                      Add Item -
                    </Text>
                    <RsIcon fill={"#fff"} />
                    <Text className="text-[#fff] font-[400] text-[20px] leading-[30]">
                      {total}
                    </Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </RBSheet>

          <ProductDetails
            key={1}
            ref={refProductRBSheet}
            product_id={props?.id}
            shop_id={props?.shop_id}
          />
          <ShirtBottomSheet ref={refProductTShirtRBSheet} />
        </>
      )}
    </>
  );
};
export const ProductDetails = forwardRef(
  ({ product_id, shop_id }: any, ref) => {
    const { data, isLoading, isFetching } = useTenStackHook2<
      { product_id: string },
      { product_data: productDetailsModel }
    >({
      key: product_id,
      canSave: true,
      endpoint: "customer/product/product_details",
      params: {
        product_id: product_id,
      },
    });

    const { mutate, isPending } = useTenStackMutateD({
      endpoint: "customer/product/add_remove_product_wishlist",
      invalidateQueriesKey: ["prd_wishlist", product_id, "productDetails"],
    });
    const [isFav, setisFav] = useState<boolean>(
      data?.product_data?.is_whistlist === 1,
    );
    useEffect(() => {
      setisFav(data?.product_data?.is_whistlist === 1);
    }, [isFetching]);

    return (
      <RBSheet
        ref={ref}
        customStyles={{
          container: {
            borderRadius: 20,
            height: 700,
          },
          draggableIcon: {
            backgroundColor: "#000",
          },
        }}
        useNativeDriver={true}
        customModalProps={{
          statusBarTranslucent: true,
        }}
        customAvoidingViewProps={{
          enabled: false,
        }}
      >
        {isLoading ? (
          <></>
        ) : (
          <ScrollView>
            <View className="flex-1 relative">
              <TouchableOpacity
                onPress={() => ref.current.close()}
                className="absolute right-3 top-3 z-20"
              >
                <CloseIcon width={20} height={20} />
              </TouchableOpacity>
              <View className="">
                {data?.product_data?.product_details?.image && (
                  <Image
                    className="w-full h-[300px]"
                    source={{ uri: data?.product_data?.product_details?.image }}
                  />
                )}
              </View>
              <View className="my-4 px-4">
                <View>
                  <View>
                    <View className="flex-row justify-between items-center">
                      <View className="">
                        <Text className="font-[600] text-[20px] leading-[30px]">
                          {data?.product_data?.product_details?.name}
                        </Text>
                      </View>
                    </View>
                    <View className="flex-row items-center space-x-4">
                      <View className="flex-row items-center">
                        <RsIcon fill={"#000"} />
                        <Text className="text-[19px]">
                          {data?.product_data?.product_details?.price}

                          {data?.product_data?.product_details
                            ?.is_unpackage_prd ? (
                            <>
                              {"/"}
                              <Text>
                                {data?.product_data?.product_details.unit}
                              </Text>
                            </>
                          ) : (
                            <></>
                          )}
                        </Text>
                      </View>
                    </View>
                  </View>
                  <View className="">
                    <TouchableOpacity
                      onPress={() => {
                        mutate(
                          {
                            product_id: product_id,
                          },
                          {
                            onSuccess: () => {
                              setisFav((state) => !state);
                            },
                          },
                        );
                      }}
                      className="my-2"
                      style={{}}
                    >
                      <HeartIcon2 fill={isFav ? "#00660A" : "#ccc"} />
                    </TouchableOpacity>
                  </View>
                </View>
                <View className="m-2 flex-row justify-between items-start flex-1 flex-wrap">
                  {data?.product_data?.product_details?.delivery_mode === 1 ? (
                    <>
                      <OptionsAndConditions
                        icon={<WarrantyGuaranty width={30} height={30} />}
                        text1={"Normal"}
                        text2={"Delivery"}
                        key={2}
                        index={2}
                      />
                    </>
                  ) : (
                    <>
                      {data?.product_data?.product_details?.delivery_mode ===
                      2 ? (
                        <OptionsAndConditions
                          icon={<HeavyVehicle width={30} height={30} />}
                          text1={"Heavy Vehicle"}
                          text2={"Delivery"}
                          key={1}
                          index={1}
                        />
                      ) : (
                        <OptionsAndConditions
                          icon={<WarrantyGuaranty width={30} height={30} />}
                          text1={"Quick"}
                          text2={"Delivery"}
                          key={3}
                          index={3}
                        />
                      )}
                    </>
                  )}
                  {data?.product_data?.product_details?.prd_cover_by === 2 ? (
                    <>
                      <OptionsAndConditions
                        icon={<Replacement width={30} height={30} />}
                        text1={
                          data?.product_data?.product_details?.prd_cover_num ===
                          0
                            ? `${
                                data?.product_data?.product_details
                                  ?.prd_cover_duration
                              } Month`
                            : `${
                                data?.product_data?.product_details
                                  ?.prd_cover_duration
                              } Year`
                        }
                        text2={`warranty`}
                        key={2}
                        index={2}
                      />
                    </>
                  ) : null}
                  {data?.product_data?.product_details?.prd_cover_by === 3 ? (
                    <>
                      <OptionsAndConditions
                        icon={<Replacement width={30} height={30} />}
                        text1={
                          data?.product_data?.product_details?.prd_cover_num ===
                          0
                            ? `${
                                data?.product_data?.product_details
                                  ?.prd_cover_duration
                              } Month`
                            : `${
                                data?.product_data?.product_details
                                  ?.prd_cover_duration
                              } Year`
                        }
                        text2={`guaranty`}
                        key={2}
                        index={2}
                      />
                    </>
                  ) : null}
                </View>
                <View className="">
                  {!data?.product_data?.product_details.is_unpackage_prd && (
                    <Text>
                      {data?.product_data?.product_details.qty}
                      {data?.product_data?.product_details.unit}
                    </Text>
                  )}
                </View>
                <View className="flex-row items-center space-x-2 mt-2">
                  <GStarIcon
                    fill={
                      data?.product_data?.avg_rating >= 1 ? "#DBB900" : "#fff"
                    }
                    height={20}
                    width={20}
                  />
                  <GStarIcon
                    fill={
                      data?.product_data?.avg_rating >= 2 ? "#DBB900" : "#fff"
                    }
                    height={20}
                    width={20}
                  />
                  <GStarIcon
                    fill={
                      data?.product_data?.avg_rating >= 3 ? "#DBB900" : "#fff"
                    }
                    height={20}
                    width={20}
                  />
                  <GStarIcon
                    fill={
                      data?.product_data?.avg_rating >= 4 ? "#DBB900" : "#fff"
                    }
                    height={20}
                    width={20}
                  />
                  <GStarIcon
                    fill={
                      data?.product_data?.avg_rating >= 5 ? "#DBB900" : "#fff"
                    }
                    height={20}
                    width={20}
                  />
                  <Text>{data?.product_data?.total_count} ratings</Text>
                </View>
                <View className="my-4">
                  <Text className="text-[14px] font-[400] leading-[21px]">
                    {data?.product_data?.product_details?.description}
                  </Text>
                </View>
                {data?.product_data?.product_nutrition && (
                  <>
                    <View className="">
                      <Text className="mt-2 font-[500] text-[14px]">
                        Product Nutrients List
                      </Text>
                      <Text className="text-[14px] font-[400] leading-[21px]">
                        {data?.product_data?.product_nutrition ? (
                          <>
                            {Object?.keys(
                              data?.product_data?.product_nutrition,
                            ).map((key, index) => {
                              if (index === 0) {
                                return;
                              }
                              return `${key} : ${data?.product_data?.product_nutrition[key]} \n`;
                            })}
                          </>
                        ) : (
                          <></>
                        )}
                      </Text>
                    </View>
                  </>
                )}

                {data?.product_data?.product_information && (
                  <>
                    <Text className="mt-2 font-[500] text-[14px]">
                      Product Allergy Information
                    </Text>
                    <View className="my-1">
                      <Text className="text-[14px] font-[400] leading-[21px]">
                        {data?.product_data?.product_information?.description}
                      </Text>
                    </View>
                  </>
                )}

                {data?.product_data?.product_ingredient && (
                  <>
                    <Text className="mt-2 font-[500] text-[14px]">
                      Product Ingredient Information
                    </Text>
                    <View className="my-1">
                      <Text className="text-[14px] font-[400] leading-[21px]">
                        {data?.product_data?.product_ingredient?.description}
                      </Text>
                    </View>
                  </>
                )}

                {/* Variant Components Area */}

                <View className="w-full h-[1px] bg-[#e2e4e2] mt-2" />
              </View>
            </View>
          </ScrollView>
        )}
      </RBSheet>
    );
  },
);
export const TagComponent = ({ Tag, image }: any) => {
  return (
    <View className="flex-row space-x-1 items-center mr-3 my-1">
      <View className="">
        <VegIcon />
      </View>
      <View className="">
        <Text className="text-[18] font-[500]">{Tag}</Text>
      </View>
    </View>
  );
};
export const AddOnComponent = (props: any) => {
  const [select, setselect] = useState(props?.val == props?.id ? true : false);
  useEffect(() => {
    if (select) {
      props?.setOptions(props?.id);
    } else {
      props?.setOptions("");
    }
  }, [select]);
  return (
    <>
      <View className="mt-4 flex-row justify-between items-center">
        <View className="flex-row space-x-2">
          <View className="flex-row items-center space-x-4">
            <Image
              source={{ uri: props?.image }}
              height={50}
              width={50}
              className="rounded-[6px]"
            />
          </View>
          <View className="">
            <Text>{props?.text}</Text>
            <Text>{props?.amount}</Text>
          </View>
        </View>
        <View className="flex-row items-center space-x-2">
          <RsIcon />
          <Checkbox
            value={select}
            onValueChange={(val) => {
              setselect(val);
            }}
          />
        </View>
      </View>
    </>
  );
};
export const NewAddOnComponent = (props: any) => {
  const [AddonCount, setAddonCount] = useState(0);
  const {
    cartData: { data: cartlist, isFetching },
  } = useCartHook();

  useEffect(() => {
    if (cartlist?.cart_data) {
      const cartItem = cartlist?.cart_data.find(
        (item) => item.product_details.id === props?.productID,
      );
      if (cartItem) {
        const options = cartItem?.product_options.find(
          (item) => item.id === props?.id,
        )?.quantity;
        if (options) {
          setAddonCount(options);
        }
      }
    }
  }, [isFetching]);
  return (
    <>
      <View className="mt-4 flex-row justify-between items-center">
        <View className="flex-row space-x-2">
          <View className="flex-row items-center space-x-4">
            <Image
              source={{ uri: props?.image }}
              height={50}
              width={50}
              className="rounded-[6px]"
            />
          </View>
          <View className="">
            <Text>{props?.text}</Text>
            <Text>{props?.amount}</Text>
          </View>
        </View>
        {props?.control ? (
          <>
            <View
              className="flex-row items-center space-x-2"
              style={{
                alignSelf: "flex-start",
              }}
            >
              <View className="flex-row space-x-4 bg-[#A4F4AC] border-[1px] border-[#00660A] items-center w-[90px] h-[30px] justify-center rounded-[8px]">
                <TouchableOpacity
                  onPress={() => {
                    setAddonCount((count) => {
                      if (count >= 1) {
                        props?.setTotal((total) => {
                          return Number(total) - Number(props?.amount);
                        });
                        props?.setOptions((list) => {
                          return list.map((item) => {
                            if (item.id == props.id) {
                              item.quantity -= 1;
                              return item;
                            }
                            return item;
                          });
                        });
                        return count - 1;
                      }
                      props?.setOptions((list) => {
                        return list.filter((item) => {
                          if (item.id == props.id) {
                            return false;
                          }
                          return true;
                        });
                      });
                      return 0;
                    });
                  }}
                  className="z-10 flex-1 items-center justify-center h-full"
                >
                  <Icon />
                </TouchableOpacity>
                <View className="flex-shrink-0">
                  <Text>{AddonCount}</Text>
                </View>
                <TouchableOpacity
                  onPress={() => {
                    setAddonCount((count) => {
                      props?.setOptions((list) => {
                        let found;
                        list.map((item) => {
                          if (item.id == props.id) {
                            item.quantity = Number(item.quantity) + 1;
                            found = true;
                          }
                        });

                        if (found) {
                          return list;
                        }
                        return [...list, { id: props.id, quantity: 1 }];
                      });
                      props?.setTotal((total) => {
                        return Number(total) + Number(props?.amount);
                      });
                      return Number(count) + 1;
                    });
                  }}
                  className="z-10 flex-1 items-center justify-center h-full"
                >
                  <BtnPlus />
                </TouchableOpacity>
              </View>
            </View>
          </>
        ) : (
          <></>
        )}
      </View>
    </>
  );
};
export const QuantityField = (props: any) => {
  const units1 = [
    { label: "kg", value: "1" },
    { label: "grm", value: "3" },
  ];
  const units2 = [
    { label: "lt", value: "2" },
    { label: "ml", value: "4" },
  ];
  console.log(props?.defaultunit);
  return (
    <View className="px-2">
      <CustomFiled4
        text={"Select Quantity"}
        name1={props?.name1}
        name2={props?.name2}
        defaultprice={props?.defaultprice}
        defaultunit={props?.defaultunit}
        unitsplace={props?.unitsplace}
        placeholder={"Enter number"}
        onChangefun={props?.onChangefun}
        KeyType={"numeric"}
        data={
          props?.defaultunit === "kg" || props?.defaultunit === "grm"
            ? units1
            : units2
        }
        control={props?.control}
      />
    </View>
  );
};

export const ShirtBottomSheet = forwardRef((props, ref) => {
  return (
    <RBSheet
      ref={ref}
      customStyles={{
        container: {
          borderRadius: 20,
          height: "90%",
        },
        draggableIcon: {
          backgroundColor: "#000",
        },
      }}
      useNativeDriver={true}
      customModalProps={{
        statusBarTranslucent: true,
      }}
      customAvoidingViewProps={{
        enabled: false,
      }}
    >
      <ScrollView>
        <View className="flex-1">
          <View className="relative">
            <Image
              className="w-full h-[200px]"
              style={{ objectFit: "fill" }}
              source={require("../../../assets/images/Product Image/T-shirt.png")}
            />
            <View className="flex-row bottom-3 left-3 items-center space-x-2 mt-2 absolute bg-[#fff] px-[16px] py-[4px] rounded-[14px]">
              <GStarIcon fill={"#DBB900"} height={16} width={16} />
              <GStarIcon fill={"#DBB900"} height={16} width={16} />
              <GStarIcon fill={"#fff"} height={16} width={16} />
              <GStarIcon fill={"#fff"} height={16} width={16} />
              <GStarIcon fill={"#fff"} height={16} width={16} />
              <Text>(12)</Text>
            </View>
          </View>
          <View className="my-4 px-4">
            <View className="flex-row justify-between items-center">
              <View className="">
                <Text className="font-[600] text-[20px] leading-[30px]">
                  Butter Chicken Curry
                </Text>
              </View>
              <View className="flex-row items-center space-x-5">
                <TouchableOpacity className="w-[40px] h-[40px] border-[1px] border-[#BBBEBB] items-center justify-center rounded-full">
                  <HeartIcon />
                </TouchableOpacity>
                <TouchableOpacity className="w-[40px] h-[40px] border-[1px] border-[#BBBEBB] items-center justify-center rounded-full">
                  <UploadIcon />
                </TouchableOpacity>
              </View>
            </View>
            <View className="flex-row items-center space-x-4">
              <View className="flex-row items-center">
                <RsIcon fill={"#000"} />
                <Text
                  className=""
                  style={{ textDecorationLine: "line-through" }}
                >
                  1299
                </Text>
              </View>
              <View className="flex-row items-center">
                <RsIcon fill={"#000"} />
                <Text className="">1099</Text>
              </View>
              <View className="w-[70px] h-[24] rounded-[40px] items-center justify-center bg-[#00660A]">
                <Text className="text-[#fff]">50% off</Text>
              </View>
            </View>

            <View className="my-4">
              <Text className="text-[14px] font-[600] leading-[21px]">
                Variants
              </Text>
              <View className="mt-2 flex-row items-center space-x-2">
                <TouchableOpacity className="">
                  <Image
                    className="w-[50px] h-[60px] rounded-[4px]"
                    style={{ objectFit: "fill" }}
                    source={require("../../../assets/images/Product Image/T-shirt.png")}
                  />
                </TouchableOpacity>
                <TouchableOpacity className="">
                  <Image
                    className="w-[50px] h-[60px] rounded-[4px]"
                    style={{ objectFit: "fill" }}
                    source={require("../../../assets/images/Product Image/T-shirt.png")}
                  />
                </TouchableOpacity>
                <TouchableOpacity className="">
                  <Image
                    className="w-[50px] h-[60px] rounded-[4px]"
                    style={{ objectFit: "fill" }}
                    source={require("../../../assets/images/Product Image/T-shirt.png")}
                  />
                </TouchableOpacity>
                <TouchableOpacity className="">
                  <Image
                    className="w-[50px] h-[60px] rounded-[4px]"
                    style={{ objectFit: "fill" }}
                    source={require("../../../assets/images/Product Image/T-shirt.png")}
                  />
                </TouchableOpacity>
                <TouchableOpacity className="">
                  <Image
                    className="w-[50px] h-[60px] rounded-[4px]"
                    style={{ objectFit: "fill" }}
                    source={require("../../../assets/images/Product Image/T-shirt.png")}
                  />
                </TouchableOpacity>
                <TouchableOpacity className="">
                  <Image
                    className="w-[50px] h-[60px] rounded-[4px]"
                    style={{ objectFit: "fill" }}
                    source={require("../../../assets/images/Product Image/T-shirt.png")}
                  />
                </TouchableOpacity>
              </View>
            </View>

            <View className="p-4 border-[1px] border-[#E9EAE9]">
              <Text className="font-[600] text-[16px] leading-[24px]">
                Offers
              </Text>
              <Text className="font-[500] text-[14px] leading-[18px] my-1">
                Bank Offer
              </Text>
              <Text className="font-[500] text-[14px] leading-[18px] text-[#627164]">
                7.5% Discount on HDFC Debit card - Max Discount Upto  ₹750 on
                every spend
              </Text>
            </View>
            <View className="p-4 border-[1px] border-[#E9EAE9]">
              <Text className="font-[600] text-[16px] leading-[24px]">
                Select Options
              </Text>
              <View className="mt-1 flex-row items-center space-x-3">
                <TouchableOpacity className="border-[1px] border-[#E9EAE9] w-[38px] h-[41px] rounded-[8px] items-center justify-center">
                  <Text className="">36</Text>
                </TouchableOpacity>
                <TouchableOpacity className="border-[1px] border-[#E9EAE9] w-[38px] h-[41px] rounded-[8px] items-center justify-center">
                  <Text className="">38</Text>
                </TouchableOpacity>
                <TouchableOpacity className="border-[1px] border-[#E9EAE9] w-[38px] h-[41px] rounded-[8px] items-center justify-center">
                  <Text className="">40</Text>
                </TouchableOpacity>
                <TouchableOpacity className="border-[1px] border-[#E9EAE9] w-[38px] h-[41px] rounded-[8px] items-center justify-center">
                  <Text className="">42</Text>
                </TouchableOpacity>
                <TouchableOpacity className="border-[1px] border-[#E9EAE9] w-[38px] h-[41px] rounded-[8px] items-center justify-center">
                  <Text className="">46</Text>
                </TouchableOpacity>
                <TouchableOpacity className="border-[1px] border-[#E9EAE9] w-[38px] h-[41px] rounded-[8px] items-center justify-center">
                  <Text className="">48</Text>
                </TouchableOpacity>
              </View>
            </View>
            <View className="mt-4 space-y-1">
              <View className="flex-row items-center space-x-2">
                <View className="h-[6px] w-[6px] rounded-full items-center justify-center bg-[#001A03]" />
                <Text className="">Pay on Delivery is available</Text>
              </View>
              <View className="flex-row items-center space-x-2">
                <View className="h-[6px] w-[6px] rounded-full items-center justify-center bg-[#001A03]" />
                <Text className="">Hassle free 14 days Return & Exchange</Text>
              </View>
            </View>
            <View className="p-4 border-[1px] border-[#E9EAE9] my-2">
              <Text className="font-[500] text-[14px] leading-[18px] my-1">
                Product Details
              </Text>
              <Text className="font-[500] text-[14px] leading-[18px] text-[#627164]">
                Black casual shirt, has a spread collar, short sleeves, curved
                hem, and a 1 patch pocket.
              </Text>
            </View>

            <View className="">
              <Text className="font-[500] text-[14px] leading-[18px] my-1">
                More Information
              </Text>
              <Text className="font-[500] text-[14px] leading-[18px] text-[#627164]">
                Product Code: 10685458
              </Text>
            </View>
            <View className="my-3">
              <Text className="font-[500] text-[14px] leading-[18px] my-1">
                Manufacturer Detail
              </Text>
              <Text className="font-[500] text-[14px] leading-[18px] text-[#627164]">
                Universal SportsBiz Private Limited No. 500, 1st floor CMH Road,
                Binnamagla, 2nd Stage, Indiranagar 783372 KARNATAKA
              </Text>
            </View>
            <View className="">
              <Text className="font-[500] text-[14px] leading-[18px] my-1">
                Country of Origin
              </Text>
              <Text className="font-[500] text-[14px] leading-[18px] text-[#627164]">
                India
              </Text>
            </View>

            <View className="w-full h-[1px] bg-[#e2e4e2] mt-2" />

            <View className="flex-row space-x-6 items-center justify-center mt-4">
              <View className="flex-row space-x-4 bg-[#A4F4AC] border-[1px] border-[#00660A] items-center w-[124px] h-[46px] justify-center">
                <TouchableOpacity
                  onPress={() => {
                    // setitemCount((count) => {
                    //   if (count > 1) {
                    //     setdata((state) => {
                    //       state.pop();
                    //       return state;
                    //     });
                    //     return count - 1;
                    //   }
                    //   refProductTShirtRBSheet.current.close();
                    //   return 1;
                    // });
                    // CheckData();
                  }}
                  className="z-10 w-[20px] h-[20px] items-center justify-center"
                >
                  <Icon />
                </TouchableOpacity>
                <Text>{2}</Text>
                <TouchableOpacity
                  onPress={() => {
                    // setitemCount((count) => {
                    //   setdata((state) => {
                    //     state.push({ ...props.data, id: props.id });
                    //     return state;
                    //   });
                    //   return count + 1;
                    // });
                  }}
                  className="z-10 w-[20px] h-[20px] items-center justify-center"
                >
                  <BtnPlus />
                </TouchableOpacity>
              </View>
              <TouchableOpacity
                onPress={() => {
                  // addData(data);
                  // CheckData();
                  refProductTShirtRBSheet.current.close();
                }}
                className=" flex-row items-center h-[46px] w-[200px] justify-center rounded-[4px] bg-[#00660A]"
              >
                <Text className="text-[#fff] font-[400] text-[20px] leading-[30]">
                  Add Item -
                </Text>
                <RsIcon fill={"#fff"} />
                <Text className="text-[#fff] font-[400] text-[20px] leading-[30]">
                  400
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
    </RBSheet>
  );
});

export const GrayImage = ({ image }: any) => {
  const [imageUrl, setimageUrl] = useState("");
  useEffect(() => {
    const getimage = async () => {
      let url = await applyGrayscale(image);
      setimageUrl(url);
    };
    getimage();
  }, []);
  return (
    <Image
      source={{ uri: image }}
      style={{ width: 100, height: 100, tintColor: "gray" }}
    />
  );
};

export const OptionsAndConditions = ({ text1, text2, icon, index }: any) => {
  const BorderRadus = {
    0: {
      borderTopRightRadius: 10,
      borderBottomRightRadius: 10,
      borderTopLeftRadius: 0,
      borderBottomLeftRadius: 0,
    },
    1: {
      borderTopRightRadius: 10,
      borderBottomRightRadius: 10,
      borderTopLeftRadius: 10,
      borderBottomLeftRadius: 10,
    },
    2: {
      borderTopRightRadius: 0,
      borderBottomRightRadius: 0,
      borderTopLeftRadius: 10,
      borderBottomLeftRadius: 10,
    },
  };
  return (
    <View
      style={{
        shadowRadius: 10,
        width: "33%",
        backgroundColor: "#EFF4F1",
        ...BorderRadus[index % 3],
      }}
      className="space-y-2 mt-1 items-center justify-center px-3 pb-3 pt-5"
    >
      {icon}
      <View className="">
        <Text className="text-center text-[14px] font-[600px]">{text1}</Text>
        <Text className="text-center text-[12px]">{text2}</Text>
      </View>
    </View>
  );
};

export default OrderDetails;
