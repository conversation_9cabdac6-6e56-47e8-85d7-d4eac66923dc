import LoactionIcon from "../../../../assets/Icon/Orderdetails/LocationPinIcon.svg";
import NonVegIcon from "../../../../assets/Icon/nonVegIcon.svg";
import React, { useEffect, useState } from "react";
import VegIcon from "../../../../assets/Icon/VegIcon.svg";
import useGetApiData from "../../../../hooks/useGetApiData";
import useGetApiDatawithParam from "../../../../hooks/useGetApiDatawithParam";
import { useGlobalSearchParams } from "expo-router";
import { Text, View } from "react-native";
import { ActivityIndicator } from "react-native";
import { useOrderId } from "../../../../store";

const info = () => {
  const { data } = useGlobalSearchParams();
  const [rDara, setrDate] = useState(JSON.parse(data));
  const {
    data: CheckOutData,
    isLoading: CheckOutDataisLoading,
    triggerreFresh: CheckOutDataReFresh,
  } = useGetApiData({
    endpoint: "product/checkout_page",
  });
  return (
    <View className="flex-1 mt-4 px-4">
      {rDara?.order_shops?.map((items) => {
        return (
          <>
            <View className="flex-row space-x-6">
              <View className="items-center">
                <LoactionIcon />
                <View className="h-[50px] w-[1px] mt-2 mb-2 border-[1px] border-[#627164] border-dashed" />
              </View>
              <View>
                <View>
                  <Text>{items?.shop_name}</Text>
                </View>
                <View>
                  <Text>{items?.address}</Text>
                </View>
              </View>
            </View>
            <View className="flex-row space-x-6">
              <View className="items-center">
                <LoactionIcon />
              </View>
              <View>
                <View>
                  <Text>{rDara?.delivery_address?.receiver_name}</Text>
                </View>
                <View className="flex-row max-w-[300px]">
                  <Text>{rDara?.delivery_address?.address}</Text>
                </View>
              </View>
            </View>
          </>
        );
      })}

      <View className=" mt-8">
        <Text>Bill Details</Text>
      </View>

      <View className=" mt-5">
        {rDara?.order_shops?.map((items) => {
          return (
            <>
              <View>
                <Text>{items?.shop_name}</Text>
              </View>
              {items?.products?.map((products) => {
                return (
                  <>
                    <View className="flex-row items-center mt-3 space-x-2">
                      <VegIcon />
                      <Text>
                        {products?.product_name} x {products?.quantity}
                      </Text>
                    </View>
                  </>
                );
              })}
            </>
          );
        })}
      </View>

      <View className="mt-6 mb-4">
        <View className="flex-row items-center justify-between mt-3">
          <View>
            <Text>Item total</Text>
          </View>
          <View>
            <Text>₹{CheckOutData?.cart_details?.item_total}</Text>
          </View>
        </View>
        <View className="flex-row items-center justify-between mt-3">
          <View>
            <Text>Taxes</Text>
          </View>
          <View>
            <Text>₹{CheckOutData?.cart_details?.gst_total}</Text>
          </View>
        </View>
        <View className="flex-row items-center justify-between mt-3">
          <View>
            <Text>Delivery Fees</Text>
          </View>
          <View>
            <Text>₹{rDara?.delivery_fees}</Text>
          </View>
        </View>

        <View className="flex-row items-center justify-between mt-3">
          <View>
            <Text>Platform Fee</Text>
          </View>
          <View>
            <Text>₹{CheckOutData?.cart_details?.platform_fees}</Text>
          </View>
        </View>
        <View className="flex-row items-center justify-between mt-3">
          <View>
            <Text>Grand Total</Text>
          </View>
          <View>
            <Text>₹{CheckOutData?.cart_details?.final_amount}</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default info;
