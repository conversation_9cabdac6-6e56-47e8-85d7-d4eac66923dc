import * as Location from "expo-location";
import CellIcon from "../../../../assets/Icon/Orderdetails/cellIcon.svg";
import Icon2 from "@/assets/2.svg";
import Icon3 from "@/assets/3.svg";
import LoactionIcon from "../../../../assets/pund.svg";
import LoactionIcon2 from "../../../../assets/Untitled-2-01 1.svg";
import LoactionIcon3 from "../../../../assets/Untitled-3-01 1.svg";
import MapView from "react-native-maps";
import React, { useEffect, useRef, useState } from "react";
import VegIcon from "../../../../assets/Icon/VegIcon.svg";
import useTenStackHook from "@/hooks/TenStackHook/TenStackHook";
import useTenStackHook2 from "@/hooks/TenStackHook2/TenStackHook2";
import { LinearGradient } from "expo-linear-gradient";
import { useGlobalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";

import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  StyleSheet,
  Modal,
  ActivityIndicator,
} from "react-native";

const Deliverydetails = () => {
  const refRBSheet = useRef();
  const [modle, setmodle] = useState(false);

  const { id } = useGlobalSearchParams();

  const { data, isLoading } = useTenStackHook2({
    key: "order_details",
    canSave: true,
    endpoint: "customer/order/order_details",
    params: { order_id: id },
  });

  const [showTax, setshowTax] = useState(false);
  return (
    <SafeAreaView className="relative flex-1 bg-[#fff]">
      {isLoading ? (
        <>
          <ActivityIndicator />
        </>
      ) : (
        <>
          <View style={{ position: "relative" }}>
            <View className={"w-full mt-2 relative items-start  px-6 flex-1"}>
              <StatusMap status={data?.data?.status_id} />;
            </View>
            <View className="relative bg-white">
              <View
                className="p-2 bg-[#fff] w-[111px] h-[111px] rounded-full"
                style={{
                  shadowColor: "#00660A",
                  elevation: 8,
                  shadowRadius: 10,
                  left: "36%",
                }}
              >
                <View className="bg-[#00660A] z-10 flex-1 rounded-full p-[5px]">
                  <View className="bg-[#fff] flex-1 rounded-full items-center justify-center">
                    <Text className="font-[600] text-[24px] leading-[24px]">
                      {data?.data?.edt}
                    </Text>
                    <Text className="font-[400] text-[14px] leading-[18px] text-[#627164]">
                      mins
                    </Text>
                  </View>
                </View>
              </View>
            </View>

            <View className="px-6 mt-4 items-center justify-center">
              <View className="w-[398px] h-[73px] items-center justify-center bg-[#D4FFD8] rounded-[8px]">
                <View className="items-center justify-center">
                  <Text>Share this OTP with your delivery partner </Text>
                </View>
                <View className="flex-row space-x-2 mt-2">
                  <View className="w-[24px] h-[25px] items-center justify-center border-[1px] border-[#00660A] rounded-[4px]">
                    <Text>{`${data?.data?.order_otp}`.split("")[0]}</Text>
                  </View>
                  <View className="w-[24px] h-[25px] items-center justify-center border-[1px] border-[#00660A] rounded-[4px]">
                    <Text>{`${data?.data?.order_otp}`.split("")[1]}</Text>
                  </View>
                  <View className="w-[24px] h-[25px] items-center justify-center border-[1px] border-[#00660A] rounded-[4px]">
                    <Text>{`${data?.data?.order_otp}`.split("")[2]}</Text>
                  </View>
                  <View className="w-[24px] h-[25px] items-center justify-center border-[1px] border-[#00660A] rounded-[4px]">
                    <Text>{`${data?.data?.order_otp}`.split("")[3]}</Text>
                  </View>
                </View>
              </View>
            </View>

            {modle && <ModleComponent modle={modle} setmodle={setmodle} />}
          </View>
          <View className="px-4 mt-4">
            <Text className="font-[500] text-[14px] my-4">
              Delivery Address
            </Text>
            <View className="flex-row space-x-6">
              <View>
                <View>
                  <Text>{data?.data?.delivery_address?.receiver_name}</Text>
                </View>
                <View className="flex-row max-w-[300px]">
                  <Text>{data?.data?.delivery_address?.address}</Text>
                </View>
              </View>
            </View>
            <View className=" mt-8">
              <Text>Bill Details</Text>
            </View>
            <View className=" mt-5">
              {data?.data?.order_shops?.map((items) => {
                return (
                  <>
                    <View>
                      <Text>{items?.shop_name}</Text>
                    </View>
                    {items?.products?.map((products) => {
                      return (
                        <View className="flex-row items-center justify-between mt-2">
                          <View className="flex-row items-center space-x-2">
                            <VegIcon />
                            <Text>
                              {products?.product_name} x{" "}
                              {products?.quantity !== 0
                                ? products?.quantity
                                : `${products?.unpackage_prd_qty} ${products?.unpackage_prd_unit}`}
                            </Text>
                          </View>
                          <Text>₹{products?.total}</Text>
                        </View>
                      );
                    })}
                  </>
                );
              })}
            </View>
            <View className="mt-6 mb-4">
              <View className="flex-row items-center justify-between mt-3">
                <TouchableOpacity
                  onPress={() => {
                    setshowTax(!showTax);
                  }}
                  className="flex-row items-center"
                >
                  <Text>Taxes</Text>
                  {showTax ? (
                    <>
                      <Icon2 width={20} height={20} />
                    </>
                  ) : (
                    <>
                      <Icon3 width={20} height={20} />
                    </>
                  )}
                </TouchableOpacity>
                <View>
                  <Text>₹{data?.data?.gst_total}</Text>
                </View>
              </View>
              {showTax && (
                <View className="px-2">
                  <View className="flex-row items-center justify-between mt-3">
                    <View>
                      <Text>GST Total</Text>
                    </View>
                    <View>
                      <Text>₹{data?.data?.gst_total}</Text>
                    </View>
                  </View>
                  <View className="flex-row items-center justify-between mt-3">
                    <View>
                      <Text>CGST Total</Text>
                    </View>
                    <View>
                      <Text>₹{data?.data?.cgst_total}</Text>
                    </View>
                  </View>
                  <View className="flex-row items-center justify-between mt-3">
                    <View>
                      <Text>SGST Total</Text>
                    </View>
                    <View>
                      <Text>₹{data?.data?.sgst_total}</Text>
                    </View>
                  </View>
                  <View className="flex-row items-center justify-between mt-3">
                    <View>
                      <Text>CESS Total</Text>
                    </View>
                    <View>
                      <Text>₹{data?.data?.cess_total}</Text>
                    </View>
                  </View>
                </View>
              )}

              <View className="flex-row items-center justify-between mt-3">
                <View>
                  <Text>Delivery Fees</Text>
                </View>
                <View>
                  <Text>₹{data?.data?.delivery_fees}</Text>
                </View>
              </View>
              <View className="flex-row items-center justify-between mt-3">
                <View>
                  <Text>Convenience Fees</Text>
                </View>
                <View>
                  <Text>₹{data?.data?.cash_handling_charges}</Text>
                </View>
              </View>
              <View className="flex-row items-center justify-between mt-3">
                <View>
                  <Text>Platform Fee</Text>
                </View>
                <View>
                  <Text>₹{data?.data?.platform_fees}</Text>
                </View>
              </View>
              <View className="flex-row items-center justify-between mt-3">
                <View>
                  <Text>Grand Total</Text>
                </View>
                <View>
                  <Text>₹{data?.data?.order_amnt}</Text>
                </View>
              </View>
            </View>
          </View>
        </>
      )}
    </SafeAreaView>
  );
};

const OrderStatusComponent = (props) => {
  return (
    <>
      <View className="p-3 flex-row space-x-2 items-center">
        <View
          className=""
          style={{
            alignSelf: "flex-start",
          }}
        >
          {/* <ResIcon /> */}
        </View>
        <View>
          <View className="flex-row">
            <Text>{props?.shop_name}</Text>
            <Text>order has been</Text>
          </View>
          <View className="flex-row items-center space-x-2">
            <Text>picked up</Text>
            <CellIcon />
          </View>
          {/* <View className="max-w-[280px]">
            <Text>
              Pavana A will reach the location by 4:45 PM to pickup your order.
            </Text>
          </View> */}
        </View>
      </View>
    </>
  );
};

export const MapAndSideBar = () => {
  const [region, setRegion] = useState(null);
  const mapRef = useRef(null);
  const [PinCoords, setPinCoord] = useState(null);

  const requestLocationPermission = async () => {
    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Permission denied",
        "Allow location access to use this feature.",
      );
      return;
    }

    let location = await Location.getCurrentPositionAsync({});
    const { latitude, longitude } = location.coords;
    const newRegion = {
      latitude,
      longitude,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    };
    setRegion(newRegion);
    if (mapRef.current) {
      mapRef.current.animateToRegion(newRegion, 0);
    }
  };

  useEffect(() => {
    requestLocationPermission();
  }, []);

  return (
    <View className="flex-1 relative">
      <>
        <MapView
          style={StyleSheet.absoluteFill}
          region={region}
          provider="google"
          ref={mapRef}
          showsUserLocation
          zoomEnabled={true}
          onRegionChange={(re) => {
            setPinCoord(re);
          }}
        ></MapView>
      </>
    </View>
  );
};

const ModleComponent = ({ modle, setmodle }) => {
  return (
    <>
      <>
        <LinearGradient
          colors={["#000", "#000"]}
          start={[0, 0]}
          style={{
            position: "absolute",
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            zIndex: 1,
            flex: 1,
            opacity: 0.8,
          }}
        />
        <Modal visible={modle} style={{}} transparent={true}>
          <View className="flex-1 items-center justify-center">
            <View className="w-[310px] h-[294px] bg-[#fff] rounded-[20px] justify-between">
              <View className="items-center mt-8">
                <View className="mt-2">
                  <Text className="font-[600] text-[18px] leading-[24px] text-[#0E0E0E]">
                    We value your feedback!
                  </Text>
                </View>
                <View className="mt-2 px-5">
                  <Text className="font-[400] text-center text-[16px] leading-[20px] text-[#616161]">
                    Help us out with a quick App Store rating.
                  </Text>
                </View>
              </View>
              <View>
                <View className="items-center mb-4 px-5 mt-4">
                  <TouchableOpacity
                    onPress={() => {
                      setmodle(false);
                    }}
                    className="h-[48px] bg-[#00660A] w-full rounded-[8px] items-center justify-center"
                  >
                    <Text className="font-[500] text-[16px] text-[#FFFFFF] leading-[24px]">
                      Go to Play Store
                    </Text>
                  </TouchableOpacity>
                </View>
                <View className="items-center mb-4 px-5">
                  <TouchableOpacity
                    onPress={() => {
                      setmodle(false);
                    }}
                    className="h-[48px] bg-[#fff] border-[1px] border-[#00660A] w-full rounded-[8px] items-center justify-center"
                  >
                    <Text className="font-[500] text-[16px] text-[#00660A] leading-[24px]">
                      Maybe Later
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </Modal>
      </>
    </>
  );
};

const StatusMap = ({ status }: { status: Number }) => {
  if (status === 1) {
    return (
      <View className="flex-row space-x-1">
        <View className="items-center">
          <LoactionIcon />
        </View>
        <View className="mt-1">
          <Text>Waiting for Seller to accept the order</Text>
        </View>
      </View>
    );
  }
  if (status === 2) {
    return (
      <View className="flex-row items-center justify-center">
        <View className="items-start flex-1">
          <LoactionIcon2 />
        </View>
        <View className="">
          <Text>Seller Accepted the order, Preparing the order</Text>
        </View>
      </View>
    );
  }
  if (status === 3) {
    return (
      <View className="flex-row items-center justify-center">
        <View className="items-center">
          <LoactionIcon2 />
        </View>
        <View className="mt-1">
          <Text>Order ready, yet to be picked up by Delivery Partner</Text>
        </View>
      </View>
    );
  }
  if (status === 4) {
    return (
      <View className="flex-row items-center justify-center">
        <View className="items-center">
          <LoactionIcon2 />
        </View>
        <View className="mt-1">
          <Text>Delivery Partner Assigned.</Text>
        </View>
      </View>
    );
  }
  if (status === 5) {
    return (
      <View className="flex-row items-center justify-center">
        <View className="items-center">
          <LoactionIcon2 />
        </View>
        <View className="mt-1">
          <Text>Order Picked up by Delivery Partner.</Text>
        </View>
      </View>
    );
  }
  if (status === 6) {
    return (
      <View className="flex-row items-center justify-center">
        <View className="items-center">
          <LoactionIcon2 />
        </View>
        <View className="mt-1">
          <Text>Your order is on the way!</Text>
        </View>
      </View>
    );
  }
  if (status === 7) {
    return (
      <View className="flex-row space-x-1 items-center ">
        <View className="items-center justify-center">
          <LoactionIcon3 />
        </View>
        <View className="mt-1">
          <Text>Order Reached Your Location</Text>
        </View>
      </View>
    );
  }
};
export default Deliverydetails;
