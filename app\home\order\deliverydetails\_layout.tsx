import BackIcon from "../../../../assets/OrderDetails/backIcon.svg";
import DotIcon from "../../../../assets/Icon/Orderdetails/threedot.svg";
import HelpIcon from "../../../../assets/Icon/Orderdetails/helpIcon.svg";
import Icon from "../../../../assets/Icon/Orderdetails/!.svg";
import React, { useEffect, useState } from "react";
import useGetApiDatawithParam from "../../../../hooks/useGetApiDatawithParam";
import { Tooltip } from "@rneui/themed";
import { SafeAreaView } from "react-native-safe-area-context";
import { useOrderId } from "../../../../store";

import {
  ActivityIndicator,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

import {
  Slot,
  router,
  useGlobalSearchParams,
  useLocalSearchParams,
} from "expo-router";

const _layout = () => {
  const [open, setOpen] = useState(false);
  const [showinfo, setshowinfo] = useState(true);
  const { id } = useGlobalSearchParams();

  const { data, isLoading, triggerreFresh } = useGetApiDatawithParam({
    endpoint: "order/order_details",
    param: { order_id: id },
  });
  const [time12Hour, settime12Hour] = useState();
  useEffect(() => {
    const date = new Date(data?.data?.order_time);

    let hours = date.getUTCHours();
    const minutes = date.getUTCMinutes().toString().padStart(2, "0");
    const seconds = date.getUTCSeconds().toString().padStart(2, "0");

    // Determine AM or PM
    const ampm = hours >= 12 ? "PM" : "AM";

    // Convert to 12-hour format
    hours = hours % 12;
    hours = hours ? hours : 12; // If hours is 0, set to 12

    settime12Hour(`${hours}:${minutes} ${ampm}`);
  }, [isLoading]);

  return (
    <SafeAreaView>
      <ScrollView className="relative bg-[#fff]">
        <View className="px-4 flex-row items-center justify-between">
          <View className="flex-row items-center justify-start relative mt-6">
            <TouchableOpacity
              className="mr-4"
              onPress={() => {
                router.back();
              }}
            >
              <BackIcon />
            </TouchableOpacity>
            <View className="space-y-1">
              <Text className="font-[400] text-[16px] leading-[30px]">
                Order id #{data?.data?.main_order_id}
              </Text>
              <View className="flex-row items-center space-x-2">
                <TouchableOpacity
                  onPress={() => {
                    router.push({
                      pathname: "/home/<USER>/deliverydetails/info",
                      params: { data: JSON.stringify(data?.data) },
                    });
                  }}
                >
                  <Text className="font-[500] text-[12px] leading-[18px] text-[#00660A]">
                    View Details
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
          <TouchableOpacity
            onPress={() => {
              router.navigate("/home/<USER>/help");
            }}
            className="flex-row items-center justify-center mt-2 border-2 border-[#00660A] px-3 py-1 rounded-[4px]"
          >
            <Text className="text-[#000]">Help</Text>
          </TouchableOpacity>
        </View>
        <Slot />
      </ScrollView>
    </SafeAreaView>
  );
};

export default _layout;
