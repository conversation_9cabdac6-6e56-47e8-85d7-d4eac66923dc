com.mohandhass28.customer:xml/library_file_paths = 0x7f150005
com.mohandhass28.customer:xml/image_picker_provider_paths = 0x7f150003
com.mohandhass28.customer:styleable/WalletFragmentStyle = 0x7f14009e
com.mohandhass28.customer:styleable/ViewPager2 = 0x7f14009b
com.mohandhass28.customer:styleable/View = 0x7f140099
com.mohandhass28.customer:styleable/Variant = 0x7f140098
com.mohandhass28.customer:styleable/Transition = 0x7f140097
com.mohandhass28.customer:styleable/Transform = 0x7f140096
com.mohandhass28.customer:styleable/Tooltip = 0x7f140095
com.mohandhass28.customer:styleable/TextInputEditText = 0x7f140091
com.mohandhass28.customer:styleable/TabLayout = 0x7f14008f
com.mohandhass28.customer:styleable/SwitchMaterial = 0x7f14008d
com.mohandhass28.customer:styleable/SwitchCompat = 0x7f14008c
com.mohandhass28.customer:styleable/StyledPlayerView = 0x7f14008a
com.mohandhass28.customer:styleable/StateSet = 0x7f140088
com.mohandhass28.customer:styleable/StateListDrawableItem = 0x7f140087
com.mohandhass28.customer:styleable/Spinner = 0x7f140084
com.mohandhass28.customer:styleable/SnackbarLayout = 0x7f140083
com.mohandhass28.customer:styleable/ShapeAppearance = 0x7f14007d
com.mohandhass28.customer:styleable/SearchView = 0x7f14007c
com.mohandhass28.customer:styleable/ScrollingViewBehavior_Layout = 0x7f14007b
com.mohandhass28.customer:styleable/RecyclerView = 0x7f140079
com.mohandhass28.customer:styleable/RecycleListView = 0x7f140078
com.mohandhass28.customer:styleable/RangeSlider = 0x7f140077
com.mohandhass28.customer:styleable/RadialViewGroup = 0x7f140076
com.mohandhass28.customer:styleable/PreviewView = 0x7f140074
com.mohandhass28.customer:styleable/PopupWindowBackgroundState = 0x7f140073
com.mohandhass28.customer:styleable/PlayerView = 0x7f140071
com.mohandhass28.customer:styleable/OnSwipe = 0x7f14006f
com.mohandhass28.customer:styleable/NavigationBarActiveIndicator = 0x7f14006a
com.mohandhass28.customer:styleable/MotionTelltales = 0x7f140069
com.mohandhass28.customer:styleable/MotionLayout = 0x7f140067
com.mohandhass28.customer:styleable/MaterialToolbar = 0x7f140060
com.mohandhass28.customer:styleable/MaterialTimePicker = 0x7f14005f
com.mohandhass28.customer:styleable/MaterialTextView = 0x7f14005e
com.mohandhass28.customer:styleable/MaterialTextAppearance = 0x7f14005d
com.mohandhass28.customer:styleable/MaterialButtonToggleGroup = 0x7f140055
com.mohandhass28.customer:styleable/MaterialButton = 0x7f140054
com.mohandhass28.customer:styleable/MaterialAutoCompleteTextView = 0x7f140053
com.mohandhass28.customer:styleable/MapAttrs = 0x7f140050
com.mohandhass28.customer:styleable/LinearLayoutCompat_Layout = 0x7f14004c
com.mohandhass28.customer:styleable/LinearLayoutCompat = 0x7f14004b
com.mohandhass28.customer:styleable/Layout = 0x7f14004a
com.mohandhass28.customer:styleable/KeyPosition = 0x7f140047
com.mohandhass28.customer:styleable/KeyFrame = 0x7f140044
com.mohandhass28.customer:styleable/KeyAttribute = 0x7f140042
com.mohandhass28.customer:styleable/GradientColorItem = 0x7f14003f
com.mohandhass28.customer:styleable/GenericDraweeHierarchy = 0x7f14003d
com.mohandhass28.customer:styleable/FontFamilyFont = 0x7f140039
com.mohandhass28.customer:styleable/FontFamily = 0x7f140038
com.mohandhass28.customer:styleable/FlowLayout = 0x7f140037
com.mohandhass28.customer:styleable/FloatingActionButton = 0x7f140035
com.mohandhass28.customer:styleable/DrawerLayout = 0x7f140032
com.mohandhass28.customer:styleable/CropImageView = 0x7f14002d
com.mohandhass28.customer:styleable/CoordinatorLayout = 0x7f14002b
com.mohandhass28.customer:styleable/ConstraintSet = 0x7f14002a
com.mohandhass28.customer:styleable/ConstraintLayout_placeholder = 0x7f140029
com.mohandhass28.customer:styleable/ConstraintLayout_Layout = 0x7f140028
com.mohandhass28.customer:styleable/CollapsingToolbarLayout = 0x7f140023
com.mohandhass28.customer:styleable/ClockHandView = 0x7f140022
com.mohandhass28.customer:styleable/Chip = 0x7f14001e
com.mohandhass28.customer:styleable/ButtonBarLayout = 0x7f14001a
com.mohandhass28.customer:styleable/BottomSheetBehavior_Layout = 0x7f140019
com.mohandhass28.customer:styleable/BottomNavigationView = 0x7f140018
com.mohandhass28.customer:styleable/BaseProgressIndicator = 0x7f140016
com.mohandhass28.customer:styleable/AppCompatTheme = 0x7f140012
com.mohandhass28.customer:styleable/AppCompatTextHelper = 0x7f140010
com.mohandhass28.customer:styleable/AppCompatSeekBar = 0x7f14000f
com.mohandhass28.customer:styleable/AppBarLayoutStates = 0x7f14000b
com.mohandhass28.customer:styleable/AnimatedStateListDrawableTransition = 0x7f140009
com.mohandhass28.customer:styleable/AnimatedStateListDrawableItem = 0x7f140008
com.mohandhass28.customer:styleable/ActionMenuItemView = 0x7f140002
com.mohandhass28.customer:style/amu_Bubble.TextAppearance.Dark = 0x7f130475
com.mohandhass28.customer:styleable/CompoundButton = 0x7f140026
com.mohandhass28.customer:style/Widget.Support.CoordinatorLayout = 0x7f130474
com.mohandhass28.customer:styleable/Insets = 0x7f140041
com.mohandhass28.customer:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f130472
com.mohandhass28.customer:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f130471
com.mohandhass28.customer:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f130470
com.mohandhass28.customer:xml/file_provider_paths = 0x7f150001
com.mohandhass28.customer:style/Widget.MaterialComponents.Toolbar = 0x7f13046f
com.mohandhass28.customer:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f13046d
com.mohandhass28.customer:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f13046c
com.mohandhass28.customer:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f13046a
com.mohandhass28.customer:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f130467
com.mohandhass28.customer:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f13045c
com.mohandhass28.customer:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f13045b
com.mohandhass28.customer:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f13045a
com.mohandhass28.customer:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f130459
com.mohandhass28.customer:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f130457
com.mohandhass28.customer:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f130453
com.mohandhass28.customer:style/Widget.MaterialComponents.Snackbar = 0x7f130452
com.mohandhass28.customer:styleable/OnClick = 0x7f14006e
com.mohandhass28.customer:style/Widget.MaterialComponents.Slider = 0x7f130451
com.mohandhass28.customer:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f13044c
com.mohandhass28.customer:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f130448
com.mohandhass28.customer:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f130447
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialDivider = 0x7f130444
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f130442
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f130441
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f130440
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f13043c
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f13043a
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f130436
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f130435
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f130434
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f130431
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f13042f
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar = 0x7f13042d
com.mohandhass28.customer:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f13042b
com.mohandhass28.customer:styleable/MotionHelper = 0x7f140066
com.mohandhass28.customer:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f130428
com.mohandhass28.customer:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f130426
com.mohandhass28.customer:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f13045d
com.mohandhass28.customer:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f130425
com.mohandhass28.customer:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f130424
com.mohandhass28.customer:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f130423
com.mohandhass28.customer:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f130422
com.mohandhass28.customer:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f13041f
com.mohandhass28.customer:style/Widget.MaterialComponents.Chip.Filter = 0x7f13041d
com.mohandhass28.customer:style/Widget.MaterialComponents.Chip.Choice = 0x7f13041b
com.mohandhass28.customer:style/Widget.MaterialComponents.CardView = 0x7f130418
com.mohandhass28.customer:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f130417
com.mohandhass28.customer:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f130414
com.mohandhass28.customer:style/Widget.MaterialComponents.Button.TextButton = 0x7f130410
com.mohandhass28.customer:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f13040e
com.mohandhass28.customer:style/Widget.MaterialComponents.BottomNavigationView = 0x7f130407
com.mohandhass28.customer:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f130405
com.mohandhass28.customer:style/Widget.MaterialComponents.BottomAppBar = 0x7f130404
com.mohandhass28.customer:style/Widget.MaterialComponents.Badge = 0x7f130403
com.mohandhass28.customer:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f130402
com.mohandhass28.customer:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f130400
com.mohandhass28.customer:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f1303fe
com.mohandhass28.customer:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f1303fd
com.mohandhass28.customer:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f1303fc
com.mohandhass28.customer:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f1303fa
com.mohandhass28.customer:style/Widget.Material3.Toolbar.Surface = 0x7f1303f5
com.mohandhass28.customer:style/Widget.Material3.Toolbar.OnSurface = 0x7f1303f4
com.mohandhass28.customer:styleable/ViewStubCompat = 0x7f14009c
com.mohandhass28.customer:styleable/CardView = 0x7f14001c
com.mohandhass28.customer:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f1303f2
com.mohandhass28.customer:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f130456
com.mohandhass28.customer:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f1303f1
com.mohandhass28.customer:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f1303ee
com.mohandhass28.customer:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f1303ed
com.mohandhass28.customer:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1303ea
com.mohandhass28.customer:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f1303e8
com.mohandhass28.customer:style/Widget.Material3.TabLayout.Secondary = 0x7f1303e6
com.mohandhass28.customer:style/Widget.Material3.TabLayout.OnSurface = 0x7f1303e5
com.mohandhass28.customer:style/Widget.Material3.TabLayout = 0x7f1303e4
com.mohandhass28.customer:style/Widget.Material3.Snackbar.TextView = 0x7f1303e3
com.mohandhass28.customer:style/Widget.Material3.Snackbar = 0x7f1303e1
com.mohandhass28.customer:style/Widget.Material3.Slider = 0x7f1303e0
com.mohandhass28.customer:style/Widget.Material3.PopupMenu.Overflow = 0x7f1303df
com.mohandhass28.customer:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f1303de
com.mohandhass28.customer:style/Widget.Material3.PopupMenu = 0x7f1303dc
com.mohandhass28.customer:style/Widget.Material3.NavigationView = 0x7f1303db
com.mohandhass28.customer:style/Widget.Material3.NavigationRailView = 0x7f1303d9
com.mohandhass28.customer:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1303d6
com.mohandhass28.customer:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1303d3
com.mohandhass28.customer:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1303d1
com.mohandhass28.customer:style/Widget.Material3.MaterialDivider = 0x7f1303ce
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1303cb
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1303c6
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f1303c5
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1303c4
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f1303c2
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f1303bf
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f1303be
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f1303bc
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.Day = 0x7f1303b9
com.mohandhass28.customer:style/Widget.Material3.Light.ActionBar.Solid = 0x7f1303b6
com.mohandhass28.customer:style/Widget.Material3.FloatingActionButton.Primary = 0x7f1303b2
com.mohandhass28.customer:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f130413
com.mohandhass28.customer:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f1303b1
com.mohandhass28.customer:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f1303b0
com.mohandhass28.customer:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f1303ae
com.mohandhass28.customer:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1303ad
com.mohandhass28.customer:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1303ab
com.mohandhass28.customer:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f1303a9
com.mohandhass28.customer:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f1303a8
com.mohandhass28.customer:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f1303a1
com.mohandhass28.customer:style/Widget.Material3.CollapsingToolbar.Large = 0x7f1303a0
com.mohandhass28.customer:style/Widget.Material3.CollapsingToolbar = 0x7f13039f
com.mohandhass28.customer:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f13039c
com.mohandhass28.customer:style/Widget.Material3.CircularProgressIndicator = 0x7f13039b
com.mohandhass28.customer:style/Widget.Material3.ChipGroup = 0x7f13039a
com.mohandhass28.customer:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f130399
com.mohandhass28.customer:style/Widget.Material3.Chip.Input.Icon = 0x7f130396
com.mohandhass28.customer:style/Widget.Material3.Chip.Input.Elevated = 0x7f130395
com.mohandhass28.customer:style/Widget.Material3.Chip.Filter.Elevated = 0x7f130393
com.mohandhass28.customer:style/Widget.Material3.Chip.Assist.Elevated = 0x7f130391
com.mohandhass28.customer:style/Widget.Material3.Chip.Assist = 0x7f130390
com.mohandhass28.customer:style/Widget.Material3.Button.UnelevatedButton = 0x7f13038b
com.mohandhass28.customer:style/Widget.Material3.Button.TextButton.Icon = 0x7f130387
com.mohandhass28.customer:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f130385
com.mohandhass28.customer:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f130382
com.mohandhass28.customer:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f13037e
com.mohandhass28.customer:style/Widget.Material3.Button.ElevatedButton = 0x7f13037d
com.mohandhass28.customer:style/Widget.Material3.BottomNavigationView = 0x7f130378
com.mohandhass28.customer:style/Widget.Material3.Badge = 0x7f130376
com.mohandhass28.customer:style/Widget.Material3.ActionMode = 0x7f130370
com.mohandhass28.customer:style/Widget.Material3.ActionBar.Solid = 0x7f13036f
com.mohandhass28.customer:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f1303f9
com.mohandhass28.customer:style/Widget.Design.TabLayout = 0x7f13036c
com.mohandhass28.customer:style/Widget.MaterialComponents.CheckedTextView = 0x7f130419
com.mohandhass28.customer:style/Widget.Design.FloatingActionButton = 0x7f130368
com.mohandhass28.customer:styleable/Autofill.InlineSuggestion = 0x7f140014
com.mohandhass28.customer:style/Widget.Design.BottomSheet.Modal = 0x7f130366
com.mohandhass28.customer:style/Widget.Design.AppBarLayout = 0x7f130364
com.mohandhass28.customer:style/Widget.Compat.NotificationActionContainer = 0x7f130362
com.mohandhass28.customer:style/Widget.Autofill.InlineSuggestionSubtitle = 0x7f130360
com.mohandhass28.customer:style/Widget.Autofill.InlineSuggestionChip = 0x7f13035d
com.mohandhass28.customer:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f13035b
com.mohandhass28.customer:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f130359
com.mohandhass28.customer:xml/file_provider_path_checkout = 0x7f150000
com.mohandhass28.customer:styleable/ListPopupWindow = 0x7f14004e
com.mohandhass28.customer:style/Widget.AppCompat.Spinner.Underlined = 0x7f130357
com.mohandhass28.customer:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f130356
com.mohandhass28.customer:style/Widget.AppCompat.SeekBar.Discrete = 0x7f130353
com.mohandhass28.customer:style/Widget.AppCompat.SeekBar = 0x7f130352
com.mohandhass28.customer:style/Widget.AppCompat.SearchView = 0x7f130350
com.mohandhass28.customer:style/Widget.AppCompat.RatingBar = 0x7f13034d
com.mohandhass28.customer:style/Widget.AppCompat.ListView.Menu = 0x7f130347
com.mohandhass28.customer:style/Widget.AppCompat.ListPopupWindow = 0x7f130344
com.mohandhass28.customer:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f13033d
com.mohandhass28.customer:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f13033c
com.mohandhass28.customer:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f13033a
com.mohandhass28.customer:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f130339
com.mohandhass28.customer:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f130334
com.mohandhass28.customer:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f130331
com.mohandhass28.customer:xml/sharing_provider_paths = 0x7f150009
com.mohandhass28.customer:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f13032e
com.mohandhass28.customer:style/Widget.AppCompat.Light.ActionBar = 0x7f13032d
com.mohandhass28.customer:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f13032a
com.mohandhass28.customer:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f130327
com.mohandhass28.customer:style/Widget.AppCompat.AutoCompleteTextView = 0x7f13031d
com.mohandhass28.customer:style/Widget.AppCompat.ActionButton.Overflow = 0x7f13031a
com.mohandhass28.customer:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f130319
com.mohandhass28.customer:style/Widget.AppCompat.ActionBar.TabView = 0x7f130317
com.mohandhass28.customer:style/Widget.AppCompat.ActionBar.TabText = 0x7f130316
com.mohandhass28.customer:style/Widget.AppCompat.ActionBar.Solid = 0x7f130314
com.mohandhass28.customer:style/WalletFragmentDefaultStyle = 0x7f130312
com.mohandhass28.customer:style/WalletFragmentDefaultButtonTextAppearance = 0x7f13030f
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f13030b
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f130307
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f130302
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f130300
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f1302ff
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f1302fe
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f1302fc
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1302fa
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f1302f9
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f1302f7
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1302f6
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f1302f5
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f1302f4
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1302f1
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f1302f0
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1302ee
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1302ec
com.mohandhass28.customer:style/Widget.Material3.Button.TonalButton = 0x7f130389
com.mohandhass28.customer:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f13034c
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1302eb
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f1302ea
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f1302e8
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1302e7
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents = 0x7f1302e6
com.mohandhass28.customer:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1302e3
com.mohandhass28.customer:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1302e1
com.mohandhass28.customer:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1302e0
com.mohandhass28.customer:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1302df
com.mohandhass28.customer:style/ThemeOverlay.Material3.Snackbar = 0x7f1302de
com.mohandhass28.customer:style/ThemeOverlay.Material3.NavigationView = 0x7f1302dd
com.mohandhass28.customer:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1302dc
com.mohandhass28.customer:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1302da
com.mohandhass28.customer:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1302d9
com.mohandhass28.customer:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1302d6
com.mohandhass28.customer:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1302d5
com.mohandhass28.customer:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1302ce
com.mohandhass28.customer:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f1302cd
com.mohandhass28.customer:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f1302ca
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f130443
com.mohandhass28.customer:style/ThemeOverlay.Material3.Dialog = 0x7f1302c8
com.mohandhass28.customer:xml/secure_store_data_extraction_rules = 0x7f150008
com.mohandhass28.customer:style/ThemeOverlay.Material3.Dark = 0x7f1302c5
com.mohandhass28.customer:style/ThemeOverlay.Material3.Chip.Assist = 0x7f1302c4
com.mohandhass28.customer:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f1302c2
com.mohandhass28.customer:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f1302c1
com.mohandhass28.customer:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f1302ba
com.mohandhass28.customer:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f130386
com.mohandhass28.customer:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f1302b9
com.mohandhass28.customer:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f1302b8
com.mohandhass28.customer:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f1302b7
com.mohandhass28.customer:style/ThemeOverlay.Material3 = 0x7f1302b5
com.mohandhass28.customer:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f1302b2
com.mohandhass28.customer:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f13032f
com.mohandhass28.customer:style/ThemeOverlay.AppCompat.Dialog = 0x7f1302b1
com.mohandhass28.customer:styleable/ColorStateListItem = 0x7f140025
com.mohandhass28.customer:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f1302b0
com.mohandhass28.customer:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f1302ae
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1303c8
com.mohandhass28.customer:style/ThemeOverlay.AppCompat.Dark = 0x7f1302ad
com.mohandhass28.customer:style/Theme.MaterialComponents.NoActionBar = 0x7f1302a3
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f1302a1
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f13029e
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f13029c
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f13029b
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f130299
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.Dialog = 0x7f130297
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f130295
com.mohandhass28.customer:style/Theme.ReactNative.AppCompat.Light = 0x7f1302a5
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.Bridge = 0x7f130294
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f130293
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.BarSize = 0x7f130292
com.mohandhass28.customer:style/Theme.MaterialComponents.Light = 0x7f130291
com.mohandhass28.customer:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f13028a
com.mohandhass28.customer:style/Theme.MaterialComponents.Dialog.Alert = 0x7f130289
com.mohandhass28.customer:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f130286
com.mohandhass28.customer:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f130285
com.mohandhass28.customer:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f130280
com.mohandhass28.customer:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f13027f
com.mohandhass28.customer:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f13027e
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f130305
com.mohandhass28.customer:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f13027d
com.mohandhass28.customer:style/Widget.Design.TextInputEditText = 0x7f13036d
com.mohandhass28.customer:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f13027c
com.mohandhass28.customer:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f13027b
com.mohandhass28.customer:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f130279
com.mohandhass28.customer:style/Theme.MaterialComponents.Bridge = 0x7f130276
com.mohandhass28.customer:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f130275
com.mohandhass28.customer:style/Theme.MaterialComponents = 0x7f130274
com.mohandhass28.customer:style/Theme.Material3.Light.NoActionBar = 0x7f130273
com.mohandhass28.customer:style/Theme.Material3.Light.DialogWhenLarge = 0x7f130272
com.mohandhass28.customer:style/Theme.Material3.Light.Dialog.Alert = 0x7f130270
com.mohandhass28.customer:style/Theme.Material3.DynamicColors.DayNight = 0x7f13026b
com.mohandhass28.customer:style/Theme.Material3.DynamicColors.Dark = 0x7f13026a
com.mohandhass28.customer:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f130268
com.mohandhass28.customer:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f130267
com.mohandhass28.customer:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f130260
com.mohandhass28.customer:style/Theme.Material3.Dark.Dialog.Alert = 0x7f13025f
com.mohandhass28.customer:style/Widget.Autofill.InlineSuggestionTitle = 0x7f130361
com.mohandhass28.customer:style/Theme.Material3.Dark = 0x7f13025c
com.mohandhass28.customer:style/Theme.FullScreenDialogAnimatedSlide = 0x7f13025b
com.mohandhass28.customer:style/Theme.FullScreenDialogAnimatedFade = 0x7f13025a
com.mohandhass28.customer:style/Theme.Design.NoActionBar = 0x7f130258
com.mohandhass28.customer:style/Theme.Design.Light = 0x7f130255
com.mohandhass28.customer:style/Theme.ReactNative.TextInput.DefaultBackground = 0x7f1302a7
com.mohandhass28.customer:style/Theme.Catalyst.RedBox = 0x7f130252
com.mohandhass28.customer:style/Theme.Catalyst = 0x7f130250
com.mohandhass28.customer:style/Theme.AppCompat.NoActionBar = 0x7f13024e
com.mohandhass28.customer:style/Theme.AppCompat.Light.NoActionBar = 0x7f13024d
com.mohandhass28.customer:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f13024c
com.mohandhass28.customer:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f13024b
com.mohandhass28.customer:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f13024a
com.mohandhass28.customer:style/Theme.AppCompat.Light.Dialog = 0x7f130249
com.mohandhass28.customer:style/Theme.AppCompat.Empty = 0x7f130246
com.mohandhass28.customer:style/Theme.AppCompat.Dialog.Alert = 0x7f130243
com.mohandhass28.customer:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f130333
com.mohandhass28.customer:style/Theme.AppCompat.Dialog = 0x7f130242
com.mohandhass28.customer:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f130241
com.mohandhass28.customer:style/Theme.AppCompat.DayNight.Dialog = 0x7f13023d
com.mohandhass28.customer:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f13023c
com.mohandhass28.customer:style/Theme.AppCompat.DayNight = 0x7f13023b
com.mohandhass28.customer:style/Theme.AppCompat.CompactMenu = 0x7f13023a
com.mohandhass28.customer:style/Widget.AppCompat.Spinner = 0x7f130354
com.mohandhass28.customer:style/Theme.AppCompat = 0x7f130239
com.mohandhass28.customer:style/Theme.App.SplashScreen = 0x7f130238
com.mohandhass28.customer:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f130235
com.mohandhass28.customer:style/TextAppearance.Test.UsesDp = 0x7f130232
com.mohandhass28.customer:style/TextAppearance.Test.NoTextSize = 0x7f130231
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.Tooltip = 0x7f130230
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f13022d
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.Headline6 = 0x7f13022b
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.Headline4 = 0x7f130229
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.Headline2 = 0x7f130227
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.Caption = 0x7f130224
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.Button = 0x7f130223
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.Body1 = 0x7f130221
com.mohandhass28.customer:style/TextAppearance.Material3.TitleSmall = 0x7f13021f
com.mohandhass28.customer:style/TextAppearance.Material3.TitleMedium = 0x7f13021e
com.mohandhass28.customer:style/TextAppearance.Material3.LabelMedium = 0x7f13021a
com.mohandhass28.customer:styleable/TextInputLayout = 0x7f140092
com.mohandhass28.customer:style/TextAppearance.Material3.LabelLarge = 0x7f130219
com.mohandhass28.customer:style/TextAppearance.Material3.HeadlineLarge = 0x7f130216
com.mohandhass28.customer:style/TextAppearance.Material3.DisplayLarge = 0x7f130213
com.mohandhass28.customer:style/TextAppearance.Material3.ActionBar.Title = 0x7f13020f
com.mohandhass28.customer:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f13020e
com.mohandhass28.customer:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f13020d
com.mohandhass28.customer:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f13020c
com.mohandhass28.customer:style/Widget.Design.NavigationView = 0x7f130369
com.mohandhass28.customer:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f13020a
com.mohandhass28.customer:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f130209
com.mohandhass28.customer:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f130206
com.mohandhass28.customer:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f130202
com.mohandhass28.customer:style/TextAppearance.Design.Prefix = 0x7f1301fb
com.mohandhass28.customer:style/TextAppearance.Design.Hint = 0x7f1301f9
com.mohandhass28.customer:styleable/Fragment = 0x7f14003b
com.mohandhass28.customer:style/TextAppearance.Design.Counter = 0x7f1301f5
com.mohandhass28.customer:style/TextAppearance.Compat.Notification.Title = 0x7f1301f2
com.mohandhass28.customer:style/TextAppearance.Compat.Notification.Time = 0x7f1301f0
com.mohandhass28.customer:style/TextAppearance.Compat.Notification.Media = 0x7f1301ef
com.mohandhass28.customer:style/TextAppearance.Compat.Notification.Line2.Media = 0x7f1301ee
com.mohandhass28.customer:style/TextAppearance.Compat.Notification.Info = 0x7f1301eb
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.LargeTouch = 0x7f1302a0
com.mohandhass28.customer:style/TextAppearance.Compat.Notification = 0x7f1301ea
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1301e7
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1301e6
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1301e5
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1301e2
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1301e1
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1301dd
com.mohandhass28.customer:style/TextAppearance.AppCompat.Title = 0x7f1301d4
com.mohandhass28.customer:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1301d1
com.mohandhass28.customer:style/TextAppearance.AppCompat.Small = 0x7f1301d0
com.mohandhass28.customer:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f13033e
com.mohandhass28.customer:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1301cf
com.mohandhass28.customer:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1301cc
com.mohandhass28.customer:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1301ca
com.mohandhass28.customer:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1301c9
com.mohandhass28.customer:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1301c8
com.mohandhass28.customer:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1301c7
com.mohandhass28.customer:style/TextAppearance.AppCompat.Inverse = 0x7f1301c4
com.mohandhass28.customer:style/TextAppearance.AppCompat.Display4 = 0x7f1301c2
com.mohandhass28.customer:style/TextAppearance.AppCompat.Display3 = 0x7f1301c1
com.mohandhass28.customer:style/TextAppearance.AppCompat.Display2 = 0x7f1301c0
com.mohandhass28.customer:style/TestThemeWithLineHeight = 0x7f1301b8
com.mohandhass28.customer:style/TestStyleWithThemeLineHeightAttribute = 0x7f1301b6
com.mohandhass28.customer:style/Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f1301b3
com.mohandhass28.customer:style/Test.Widget.MaterialComponents.MaterialCalendar.Day = 0x7f1301b2
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.Button = 0x7f1301e0
com.mohandhass28.customer:style/Test.Widget.MaterialComponents.MaterialCalendar = 0x7f1301b1
com.mohandhass28.customer:style/Test.Theme.MaterialComponents.MaterialCalendar = 0x7f1301b0
com.mohandhass28.customer:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f1303f0
com.mohandhass28.customer:style/SpinnerTimePickerStyle = 0x7f1301ae
com.mohandhass28.customer:style/SpinnerTimePickerDialog = 0x7f1301ac
com.mohandhass28.customer:style/SpinnerDatePickerStyle = 0x7f1301ab
com.mohandhass28.customer:style/SpinnerDatePickerDialogBase = 0x7f1301aa
com.mohandhass28.customer:style/SpinnerDatePickerDialog = 0x7f1301a9
com.mohandhass28.customer:style/ShapeAppearanceOverlay.TopLeftCut = 0x7f1301a7
com.mohandhass28.customer:styleable/MaterialCheckBox = 0x7f140059
com.mohandhass28.customer:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f1301a6
com.mohandhass28.customer:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f1301a5
com.mohandhass28.customer:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f1301a4
com.mohandhass28.customer:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f13044d
com.mohandhass28.customer:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f1301a2
com.mohandhass28.customer:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f13019e
com.mohandhass28.customer:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f13019c
com.mohandhass28.customer:style/ShapeAppearanceOverlay.Material3.Button = 0x7f130199
com.mohandhass28.customer:style/ShapeAppearanceOverlay.Cut = 0x7f130197
com.mohandhass28.customer:style/ShapeAppearanceOverlay.BottomLeftDifferentCornerSize = 0x7f130195
com.mohandhass28.customer:style/ShapeAppearanceOverlay = 0x7f130194
com.mohandhass28.customer:style/ShapeAppearance.MaterialComponents = 0x7f13018e
com.mohandhass28.customer:style/ShapeAppearance.Material3.MediumComponent = 0x7f13018a
com.mohandhass28.customer:style/ShapeAppearance.Material3.LargeComponent = 0x7f130189
com.mohandhass28.customer:style/ShapeAppearance.Material3.Corner.Small = 0x7f130188
com.mohandhass28.customer:style/ShapeAppearance.Material3.Corner.None = 0x7f130187
com.mohandhass28.customer:style/ShapeAppearance.Material3.Corner.Medium = 0x7f130186
com.mohandhass28.customer:style/ShapeAppearance.Material3.Corner.Full = 0x7f130184
com.mohandhass28.customer:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f130180
com.mohandhass28.customer:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f13017f
com.mohandhass28.customer:styleable/GradientColor = 0x7f14003e
com.mohandhass28.customer:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f13017c
com.mohandhass28.customer:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall.Top = 0x7f13017a
com.mohandhass28.customer:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f130179
com.mohandhass28.customer:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f130175
com.mohandhass28.customer:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f130171
com.mohandhass28.customer:style/ThemeOverlay.Material3.Light = 0x7f1302d4
com.mohandhass28.customer:style/ShapeAppearance.Material3.Tooltip = 0x7f13018d
com.mohandhass28.customer:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f13016f
com.mohandhass28.customer:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f13016d
com.mohandhass28.customer:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f130169
com.mohandhass28.customer:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f130166
com.mohandhass28.customer:style/Platform.Widget.AppCompat.Spinner = 0x7f130164
com.mohandhass28.customer:style/Platform.V25.AppCompat = 0x7f130162
com.mohandhass28.customer:style/Platform.V21.AppCompat = 0x7f130160
com.mohandhass28.customer:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f13015f
com.mohandhass28.customer:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f13015e
com.mohandhass28.customer:style/Platform.ThemeOverlay.AppCompat = 0x7f13015d
com.mohandhass28.customer:style/Platform.MaterialComponents.Light = 0x7f13015b
com.mohandhass28.customer:style/Platform.AppCompat.Light = 0x7f130158
com.mohandhass28.customer:style/NoAnimationDialog = 0x7f130156
com.mohandhass28.customer:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f130154
com.mohandhass28.customer:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f130153
com.mohandhass28.customer:style/Widget.Design.BottomNavigationView = 0x7f130365
com.mohandhass28.customer:style/Platform.V25.AppCompat.Light = 0x7f130163
com.mohandhass28.customer:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f130152
com.mohandhass28.customer:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f130151
com.mohandhass28.customer:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f130150
com.mohandhass28.customer:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f13014f
com.mohandhass28.customer:style/MaterialAlertDialog.MaterialComponents = 0x7f13014c
com.mohandhass28.customer:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f13014b
com.mohandhass28.customer:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f130149
com.mohandhass28.customer:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f130147
com.mohandhass28.customer:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f130145
com.mohandhass28.customer:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1303d4
com.mohandhass28.customer:style/MaterialAlertDialog.Material3 = 0x7f130143
com.mohandhass28.customer:style/FloatingDialogWindowTheme = 0x7f130142
com.mohandhass28.customer:styleable/KeyTimeCycle = 0x7f140048
com.mohandhass28.customer:style/ExoStyledControls.TimeText.Position = 0x7f13013f
com.mohandhass28.customer:style/ExoStyledControls.TimeText.Duration = 0x7f13013e
com.mohandhass28.customer:style/ExoStyledControls.TimeText = 0x7f13013d
com.mohandhass28.customer:style/Widget.Material3.BottomSheet.Modal = 0x7f13037b
com.mohandhass28.customer:style/ExoStyledControls.Button.Center.RewWithAmount = 0x7f13013b
com.mohandhass28.customer:style/ExoStyledControls.Button.Center.Previous = 0x7f13013a
com.mohandhass28.customer:style/ExoStyledControls.Button.Bottom.RepeatToggle = 0x7f130132
com.mohandhass28.customer:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1302e5
com.mohandhass28.customer:style/ExoStyledControls.Button.Bottom.PlaybackSpeed = 0x7f130131
com.mohandhass28.customer:style/ExoStyledControls.Button.Bottom.OverflowShow = 0x7f130130
com.mohandhass28.customer:style/ExoStyledControls.Button.Bottom.AudioTrack = 0x7f13012c
com.mohandhass28.customer:style/ExoStyledControls.Button.Bottom = 0x7f13012b
com.mohandhass28.customer:style/ExoStyledControls.Button = 0x7f13012a
com.mohandhass28.customer:style/ExoMediaButton.Previous = 0x7f130126
com.mohandhass28.customer:style/ExoMediaButton.Play = 0x7f130125
com.mohandhass28.customer:style/ExoMediaButton.Pause = 0x7f130124
com.mohandhass28.customer:style/ExoMediaButton.FastForward = 0x7f130122
com.mohandhass28.customer:style/ExoMediaButton = 0x7f130121
com.mohandhass28.customer:style/DialogWindowTheme = 0x7f13011f
com.mohandhass28.customer:style/DialogAnimationFade = 0x7f13011d
com.mohandhass28.customer:style/CheckoutTheme = 0x7f13011c
com.mohandhass28.customer:style/CardView.Light = 0x7f13011b
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f130306
com.mohandhass28.customer:style/Base.v27.Theme.SplashScreen.Light = 0x7f130116
com.mohandhass28.customer:style/Base.v21.Theme.SplashScreen.Light = 0x7f130114
com.mohandhass28.customer:style/Base.v21.Theme.SplashScreen = 0x7f130113
com.mohandhass28.customer:style/Widget.Material3.Button = 0x7f13037c
com.mohandhass28.customer:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f130111
com.mohandhass28.customer:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f130110
com.mohandhass28.customer:style/Base.Widget.MaterialComponents.Snackbar = 0x7f13010f
com.mohandhass28.customer:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f13010d
com.mohandhass28.customer:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f13010c
com.mohandhass28.customer:style/TestStyleWithLineHeight = 0x7f1301b4
com.mohandhass28.customer:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f13010b
com.mohandhass28.customer:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f13010a
com.mohandhass28.customer:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f130109
com.mohandhass28.customer:style/Base.Widget.MaterialComponents.Chip = 0x7f130107
com.mohandhass28.customer:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f130104
com.mohandhass28.customer:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f130103
com.mohandhass28.customer:style/Base.Widget.Material3.TabLayout = 0x7f130102
com.mohandhass28.customer:style/Base.Widget.Material3.Snackbar = 0x7f130101
com.mohandhass28.customer:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f1300fe
com.mohandhass28.customer:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f1300fa
com.mohandhass28.customer:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f1300f9
com.mohandhass28.customer:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f1300f8
com.mohandhass28.customer:style/Base.Widget.Material3.CollapsingToolbar = 0x7f1300f7
com.mohandhass28.customer:style/Base.Widget.Material3.Chip = 0x7f1300f6
com.mohandhass28.customer:style/Base.Widget.Material3.CardView = 0x7f1300f5
com.mohandhass28.customer:style/Base.Widget.AppCompat.Toolbar = 0x7f1300f0
com.mohandhass28.customer:style/Base.Widget.AppCompat.TextView = 0x7f1300ee
com.mohandhass28.customer:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1300ed
com.mohandhass28.customer:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1300e9
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f130439
com.mohandhass28.customer:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1300e7
com.mohandhass28.customer:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1300e6
com.mohandhass28.customer:style/Base.Widget.AppCompat.RatingBar = 0x7f1300e5
com.mohandhass28.customer:style/Base.Widget.AppCompat.ProgressBar = 0x7f1300e3
com.mohandhass28.customer:style/Base.Widget.AppCompat.PopupWindow = 0x7f1300e2
com.mohandhass28.customer:style/Base.Widget.AppCompat.PopupMenu = 0x7f1300e0
com.mohandhass28.customer:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1300dc
com.mohandhass28.customer:style/Base.Widget.AppCompat.ListMenuView = 0x7f1300db
com.mohandhass28.customer:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1300d7
com.mohandhass28.customer:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1300d6
com.mohandhass28.customer:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1301f4
com.mohandhass28.customer:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1300d5
com.mohandhass28.customer:styleable/SwipeRefreshLayout = 0x7f14008b
com.mohandhass28.customer:style/Base.Widget.AppCompat.ImageButton = 0x7f1300d2
com.mohandhass28.customer:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1300ce
com.mohandhass28.customer:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1300cd
com.mohandhass28.customer:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1300cb
com.mohandhass28.customer:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1300ca
com.mohandhass28.customer:style/Base.Widget.AppCompat.ButtonBar = 0x7f1300c9
com.mohandhass28.customer:style/Base.Widget.AppCompat.Button.Small = 0x7f1300c8
com.mohandhass28.customer:style/Base.Widget.AppCompat.Button.Colored = 0x7f1300c7
com.mohandhass28.customer:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1300c6
com.mohandhass28.customer:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1300c4
com.mohandhass28.customer:style/Base.Widget.AppCompat.Button = 0x7f1300c3
com.mohandhass28.customer:style/Base.Widget.AppCompat.ActionMode = 0x7f1300c0
com.mohandhass28.customer:style/Base.Widget.AppCompat.ActionButton = 0x7f1300bd
com.mohandhass28.customer:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1300ba
com.mohandhass28.customer:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1300b9
com.mohandhass28.customer:style/Base.Widget.AppCompat.ActionBar = 0x7f1300b8
com.mohandhass28.customer:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1300b7
com.mohandhass28.customer:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1300b5
com.mohandhass28.customer:style/Base.V7.Theme.AppCompat.Light = 0x7f1300b2
com.mohandhass28.customer:style/Base.V7.Theme.AppCompat = 0x7f1300b0
com.mohandhass28.customer:style/Base.V28.Theme.AppCompat = 0x7f1300ae
com.mohandhass28.customer:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1300aa
com.mohandhass28.customer:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1300a8
com.mohandhass28.customer:style/Base.V24.Theme.Material3.Dark = 0x7f1300a7
com.mohandhass28.customer:xml/secure_store_backup_rules = 0x7f150007
com.mohandhass28.customer:style/Base.V23.Theme.AppCompat.Light = 0x7f1300a6
com.mohandhass28.customer:style/Base.V23.Theme.AppCompat = 0x7f1300a5
com.mohandhass28.customer:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1300a1
com.mohandhass28.customer:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f13009f
com.mohandhass28.customer:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f13009d
com.mohandhass28.customer:style/Base.V21.Theme.AppCompat.Light = 0x7f13009a
com.mohandhass28.customer:style/Base.V21.Theme.AppCompat = 0x7f130098
com.mohandhass28.customer:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f130097
com.mohandhass28.customer:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f130095
com.mohandhass28.customer:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f130094
com.mohandhass28.customer:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1300bc
com.mohandhass28.customer:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f130092
com.mohandhass28.customer:styleable/AnimatedStateListDrawableCompat = 0x7f140007
com.mohandhass28.customer:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f130091
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.Badge = 0x7f130220
com.mohandhass28.customer:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f130090
com.mohandhass28.customer:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f13008d
com.mohandhass28.customer:styleable/MaterialShape = 0x7f14005c
com.mohandhass28.customer:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f13008c
com.mohandhass28.customer:style/Base.V14.Theme.Material3.Light = 0x7f130087
com.mohandhass28.customer:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f130086
com.mohandhass28.customer:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f130271
com.mohandhass28.customer:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f130083
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.Headline3 = 0x7f130228
com.mohandhass28.customer:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f130082
com.mohandhass28.customer:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f130081
com.mohandhass28.customer:style/ExoStyledControls.Button.Bottom.VR = 0x7f130135
com.mohandhass28.customer:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f13007f
com.mohandhass28.customer:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f13007e
com.mohandhass28.customer:style/Base.ThemeOverlay.Material3.Dialog = 0x7f13007d
com.mohandhass28.customer:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f130078
com.mohandhass28.customer:styleable/TabItem = 0x7f14008e
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f13042c
com.mohandhass28.customer:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f130077
com.mohandhass28.customer:style/Base.ThemeOverlay.AppCompat = 0x7f130074
com.mohandhass28.customer:style/Base.Theme.SplashScreen = 0x7f130071
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f130070
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f13006e
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f13006b
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f130066
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f130062
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.Dialog = 0x7f130061
com.mohandhass28.customer:style/ThemeOverlayColorAccentRed = 0x7f13030e
com.mohandhass28.customer:style/Base.Theme.Material3.Light.Dialog = 0x7f13005d
com.mohandhass28.customer:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f13005c
com.mohandhass28.customer:style/Base.Theme.Material3.Light = 0x7f13005b
com.mohandhass28.customer:style/Base.Theme.Material3.Dark.Dialog = 0x7f13005a
com.mohandhass28.customer:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f130059
com.mohandhass28.customer:style/Widget.MaterialComponents.Chip.Entry = 0x7f13041c
com.mohandhass28.customer:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f130055
com.mohandhass28.customer:styleable/KeyTrigger = 0x7f140049
com.mohandhass28.customer:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f130054
com.mohandhass28.customer:style/Base.Theme.AppCompat.Light.Dialog = 0x7f130053
com.mohandhass28.customer:style/Base.Theme.AppCompat.Light = 0x7f130051
com.mohandhass28.customer:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f1301ff
com.mohandhass28.customer:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f130050
com.mohandhass28.customer:styleable/ScrimInsetsFrameLayout = 0x7f14007a
com.mohandhass28.customer:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f13004f
com.mohandhass28.customer:style/Base.Theme.AppCompat.Dialog = 0x7f13004c
com.mohandhass28.customer:style/Base.TextAppearance.MaterialComponents.Button = 0x7f130044
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f130041
com.mohandhass28.customer:xml/standalone_badge_gravity_bottom_end = 0x7f15000b
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f130040
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f13003e
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f13003c
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f130039
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f130038
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f130035
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f130032
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Title = 0x7f13002f
com.mohandhass28.customer:styleable/AlertDialog = 0x7f140006
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f13002e
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Subhead = 0x7f13002d
com.mohandhass28.customer:styleable/CustomWalletTheme = 0x7f14002f
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Small = 0x7f13002b
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f130028
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Medium = 0x7f130025
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f130023
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f130022
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Large = 0x7f130021
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Inverse = 0x7f130020
com.mohandhass28.customer:style/Widget.AppCompat.DrawerArrowToggle = 0x7f130329
com.mohandhass28.customer:style/TestStyleWithoutLineHeight = 0x7f1301b7
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Display4 = 0x7f13001e
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Display1 = 0x7f13001b
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Caption = 0x7f13001a
com.mohandhass28.customer:style/Theme.Material3.DayNight.Dialog = 0x7f130265
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Button = 0x7f130019
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Body1 = 0x7f130017
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat = 0x7f130016
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f130437
com.mohandhass28.customer:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f130015
com.mohandhass28.customer:style/Base.DialogWindowTitle.AppCompat = 0x7f130011
com.mohandhass28.customer:style/Base.Animation.AppCompat.Tooltip = 0x7f13000f
com.mohandhass28.customer:style/Base.Animation.AppCompat.DropDownUp = 0x7f13000e
com.mohandhass28.customer:style/Base.AlertDialog.AppCompat = 0x7f13000b
com.mohandhass28.customer:style/AppTheme = 0x7f13000a
com.mohandhass28.customer:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f130009
com.mohandhass28.customer:style/Animation.Design.BottomSheetDialog = 0x7f130008
com.mohandhass28.customer:style/Animation.Catalyst.LogBox = 0x7f130006
com.mohandhass28.customer:style/Animation.AppCompat.Tooltip = 0x7f130005
com.mohandhass28.customer:style/Animation.AppCompat.DropDownUp = 0x7f130004
com.mohandhass28.customer:style/Animation.AppCompat.Dialog = 0x7f130003
com.mohandhass28.customer:string/tooltip_description = 0x7f120138
com.mohandhass28.customer:string/timer_description = 0x7f120136
com.mohandhass28.customer:string/tab = 0x7f120133
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1301e3
com.mohandhass28.customer:string/status_bar_notification_info_overflow = 0x7f120130
com.mohandhass28.customer:string/state_unselected_description = 0x7f12012f
com.mohandhass28.customer:string/state_on = 0x7f12012d
com.mohandhass28.customer:string/state_mixed_description = 0x7f12012a
com.mohandhass28.customer:string/state_collapsed_description = 0x7f120127
com.mohandhass28.customer:string/scrollbar_description = 0x7f120122
com.mohandhass28.customer:string/range_start = 0x7f120120
com.mohandhass28.customer:string/range_end = 0x7f12011f
com.mohandhass28.customer:string/pick_image_chooser_title = 0x7f12011a
com.mohandhass28.customer:string/path_password_strike_through = 0x7f120118
com.mohandhass28.customer:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f13025d
com.mohandhass28.customer:string/path_password_eye_mask_visible = 0x7f120117
com.mohandhass28.customer:string/navigation_menu = 0x7f120112
com.mohandhass28.customer:string/mtrl_picker_toggle_to_text_input_mode = 0x7f12010f
com.mohandhass28.customer:string/mtrl_picker_text_input_year_abbr = 0x7f12010c
com.mohandhass28.customer:string/mtrl_picker_text_input_day_abbr = 0x7f12010a
com.mohandhass28.customer:string/mtrl_picker_text_input_date_range_end_hint = 0x7f120108
com.mohandhass28.customer:string/mtrl_picker_text_input_date_hint = 0x7f120107
com.mohandhass28.customer:styleable/MaterialAlertDialog = 0x7f140051
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f13002a
com.mohandhass28.customer:string/mtrl_picker_save = 0x7f120106
com.mohandhass28.customer:style/TextAppearance.Material3.BodyLarge = 0x7f130210
com.mohandhass28.customer:string/mtrl_picker_range_header_unselected = 0x7f120105
com.mohandhass28.customer:string/mtrl_picker_range_header_selected = 0x7f120103
com.mohandhass28.customer:string/mtrl_picker_range_header_only_start_selected = 0x7f120102
com.mohandhass28.customer:string/mtrl_picker_range_header_only_end_selected = 0x7f120101
com.mohandhass28.customer:string/mtrl_picker_out_of_range = 0x7f120100
com.mohandhass28.customer:string/mtrl_picker_navigate_to_year_description = 0x7f1200ff
com.mohandhass28.customer:string/mtrl_picker_invalid_format = 0x7f1200fb
com.mohandhass28.customer:string/mtrl_picker_date_header_unselected = 0x7f1200f9
com.mohandhass28.customer:string/mtrl_picker_date_header_title = 0x7f1200f8
com.mohandhass28.customer:string/mtrl_picker_confirm = 0x7f1200f6
com.mohandhass28.customer:string/mtrl_exceed_max_badge_number_suffix = 0x7f1200f1
com.mohandhass28.customer:string/menuitem_description = 0x7f1200ed
com.mohandhass28.customer:string/menu_description = 0x7f1200eb
com.mohandhass28.customer:string/material_timepicker_text_input_mode_description = 0x7f1200ea
com.mohandhass28.customer:string/material_timepicker_minute = 0x7f1200e7
com.mohandhass28.customer:string/material_timepicker_hour = 0x7f1200e6
com.mohandhass28.customer:string/material_timepicker_clock_mode_description = 0x7f1200e5
com.mohandhass28.customer:string/material_slider_range_start = 0x7f1200e3
com.mohandhass28.customer:string/material_slider_range_end = 0x7f1200e2
com.mohandhass28.customer:string/material_motion_easing_standard = 0x7f1200e1
com.mohandhass28.customer:string/material_motion_easing_emphasized = 0x7f1200df
com.mohandhass28.customer:string/material_motion_easing_decelerated = 0x7f1200de
com.mohandhass28.customer:string/material_minute_suffix = 0x7f1200dc
com.mohandhass28.customer:string/material_minute_selection = 0x7f1200db
com.mohandhass28.customer:string/material_hour_selection = 0x7f1200d9
com.mohandhass28.customer:string/material_clock_toggle_content_description = 0x7f1200d8
com.mohandhass28.customer:string/m3_sys_motion_easing_standard_decelerate = 0x7f1200d6
com.mohandhass28.customer:style/Base.V28.Theme.AppCompat.Light = 0x7f1300af
com.mohandhass28.customer:string/m3_sys_motion_easing_standard_accelerate = 0x7f1200d5
com.mohandhass28.customer:style/Widget.AppCompat.RatingBar.Indicator = 0x7f13034e
com.mohandhass28.customer:string/m3_sys_motion_easing_legacy_accelerate = 0x7f1200d1
com.mohandhass28.customer:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f130409
com.mohandhass28.customer:string/m3_sys_motion_easing_legacy = 0x7f1200d0
com.mohandhass28.customer:style/Widget.Material3.BottomSheet = 0x7f13037a
com.mohandhass28.customer:string/m3_sys_motion_easing_emphasized_path_data = 0x7f1200cf
com.mohandhass28.customer:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f1200ce
com.mohandhass28.customer:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f1200cd
com.mohandhass28.customer:string/m3_sys_motion_easing_legacy_decelerate = 0x7f1200d2
com.mohandhass28.customer:string/m3_sys_motion_easing_emphasized = 0x7f1200cc
com.mohandhass28.customer:string/m3_ref_typeface_plain_regular = 0x7f1200cb
com.mohandhass28.customer:style/Widget.Material3.Snackbar.FullWidth = 0x7f1303e2
com.mohandhass28.customer:string/m3_ref_typeface_plain_medium = 0x7f1200ca
com.mohandhass28.customer:string/link_description = 0x7f1200c7
com.mohandhass28.customer:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f130201
com.mohandhass28.customer:string/label = 0x7f1200c6
com.mohandhass28.customer:string/indeterminate = 0x7f1200c3
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.Dark = 0x7f1302f2
com.mohandhass28.customer:string/in_progress = 0x7f1200c2
com.mohandhass28.customer:style/Widget.MaterialComponents.TabLayout = 0x7f130455
com.mohandhass28.customer:string/imagebutton_description = 0x7f1200c1
com.mohandhass28.customer:string/image_description = 0x7f1200c0
com.mohandhass28.customer:string/icon_content_description = 0x7f1200bf
com.mohandhass28.customer:string/ic_rotate_right_24 = 0x7f1200be
com.mohandhass28.customer:string/ic_rotate_left_24 = 0x7f1200bd
com.mohandhass28.customer:string/ic_flip_24_vertically = 0x7f1200bc
com.mohandhass28.customer:string/ic_flip_24 = 0x7f1200ba
com.mohandhass28.customer:string/hide_bottom_view_on_scroll_behavior = 0x7f1200b9
com.mohandhass28.customer:style/Theme.SplashScreen = 0x7f1302a8
com.mohandhass28.customer:string/header_description = 0x7f1200b8
com.mohandhass28.customer:string/google_storage_bucket = 0x7f1200b7
com.mohandhass28.customer:string/google_api_key = 0x7f1200b4
com.mohandhass28.customer:string/generic_error_user_canceled = 0x7f1200b3
com.mohandhass28.customer:string/generic_error_no_keyguard = 0x7f1200b2
com.mohandhass28.customer:string/generic_error_no_device_credential = 0x7f1200b1
com.mohandhass28.customer:string/fingerprint_not_recognized = 0x7f1200af
com.mohandhass28.customer:string/fingerprint_error_user_canceled = 0x7f1200ae
com.mohandhass28.customer:string/fingerprint_error_no_fingerprints = 0x7f1200ad
com.mohandhass28.customer:string/fingerprint_error_hw_not_present = 0x7f1200ab
com.mohandhass28.customer:style/Theme.SplashScreen.Common = 0x7f1302a9
com.mohandhass28.customer:string/fcm_fallback_notification_channel_label = 0x7f1200a8
com.mohandhass28.customer:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1300bb
com.mohandhass28.customer:string/fallback_menu_item_open_in_browser = 0x7f1200a6
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1301d9
com.mohandhass28.customer:string/fallback_menu_item_copy_link = 0x7f1200a5
com.mohandhass28.customer:string/fab_transformation_sheet_behavior = 0x7f1200a4
com.mohandhass28.customer:string/expo_system_ui_user_interface_style = 0x7f1200a1
com.mohandhass28.customer:string/expo_notifications_fallback_channel_name = 0x7f12009e
com.mohandhass28.customer:string/exo_track_unknown = 0x7f12009d
com.mohandhass28.customer:string/exo_track_surround_5_point_1 = 0x7f12009b
com.mohandhass28.customer:string/exo_track_stereo = 0x7f120099
com.mohandhass28.customer:string/exo_track_selection_none = 0x7f120095
com.mohandhass28.customer:string/exo_track_selection_auto = 0x7f120094
com.mohandhass28.customer:string/exo_track_role_supplementary = 0x7f120093
com.mohandhass28.customer:string/exo_track_role_commentary = 0x7f120092
com.mohandhass28.customer:string/exo_track_role_closed_captions = 0x7f120091
com.mohandhass28.customer:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f130182
com.mohandhass28.customer:string/exo_track_role_alternate = 0x7f120090
com.mohandhass28.customer:string/exo_track_mono = 0x7f12008e
com.mohandhass28.customer:string/exo_item_list = 0x7f12008c
com.mohandhass28.customer:string/exo_download_removing = 0x7f12008b
com.mohandhass28.customer:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f130446
com.mohandhass28.customer:style/ThemeOverlay.Design.TextInputEditText = 0x7f1302b4
com.mohandhass28.customer:string/exo_download_paused_for_wifi = 0x7f12008a
com.mohandhass28.customer:string/exo_download_notification_channel_name = 0x7f120087
com.mohandhass28.customer:string/exo_download_description = 0x7f120084
com.mohandhass28.customer:string/exo_download_completed = 0x7f120083
com.mohandhass28.customer:string/exo_controls_time_placeholder = 0x7f120081
com.mohandhass28.customer:string/exo_controls_shuffle_on_description = 0x7f12007f
com.mohandhass28.customer:styleable/LoadingImageView = 0x7f14004f
com.mohandhass28.customer:string/exo_controls_shuffle_off_description = 0x7f12007e
com.mohandhass28.customer:string/exo_controls_show = 0x7f12007d
com.mohandhass28.customer:string/exo_controls_rewind_description = 0x7f12007a
com.mohandhass28.customer:string/exo_controls_repeat_all_description = 0x7f120077
com.mohandhass28.customer:string/exo_controls_previous_description = 0x7f120076
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f130033
com.mohandhass28.customer:string/fingerprint_error_lockout = 0x7f1200ac
com.mohandhass28.customer:string/exo_controls_next_description = 0x7f120070
com.mohandhass28.customer:string/exo_controls_fullscreen_exit_description = 0x7f12006e
com.mohandhass28.customer:string/mtrl_picker_date_header_selected = 0x7f1200f7
com.mohandhass28.customer:string/exo_controls_vr_description = 0x7f120082
com.mohandhass28.customer:string/exo_controls_fullscreen_enter_description = 0x7f12006d
com.mohandhass28.customer:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f13016c
com.mohandhass28.customer:string/exo_controls_cc_disabled_description = 0x7f120069
com.mohandhass28.customer:string/error_icon_content_description = 0x7f120067
com.mohandhass28.customer:string/dropdown_menu = 0x7f120065
com.mohandhass28.customer:string/default_popup_window_title = 0x7f120064
com.mohandhass28.customer:string/default_error_msg = 0x7f120062
com.mohandhass28.customer:string/crop_image_activity_title = 0x7f12005f
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.Item = 0x7f1303c7
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f130024
com.mohandhass28.customer:string/crop_image_activity_no_permissions = 0x7f12005e
com.mohandhass28.customer:string/confirm_device_credential_password = 0x7f12005c
com.mohandhass28.customer:string/common_signin_button_text_long = 0x7f12005b
com.mohandhass28.customer:style/Animation.Catalyst.RedBox = 0x7f130007
com.mohandhass28.customer:string/common_open_on_phone = 0x7f120059
com.mohandhass28.customer:string/common_google_play_services_wear_update_text = 0x7f120058
com.mohandhass28.customer:string/common_google_play_services_updating_text = 0x7f120057
com.mohandhass28.customer:style/Widget.MaterialComponents.BottomSheet = 0x7f13040a
com.mohandhass28.customer:string/common_google_play_services_update_text = 0x7f120055
com.mohandhass28.customer:string/common_google_play_services_notification_ticker = 0x7f120051
com.mohandhass28.customer:style/Widget.Material3.CompoundButton.CheckBox = 0x7f1303a2
com.mohandhass28.customer:string/common_google_play_services_install_button = 0x7f12004d
com.mohandhass28.customer:string/common_google_play_services_enable_title = 0x7f12004c
com.mohandhass28.customer:string/common_google_play_services_enable_button = 0x7f12004a
com.mohandhass28.customer:string/common_google_play_services_enable_text = 0x7f12004b
com.mohandhass28.customer:string/chip_text = 0x7f120045
com.mohandhass28.customer:string/character_counter_overflowed_content_description = 0x7f120043
com.mohandhass28.customer:string/character_counter_content_description = 0x7f120042
com.mohandhass28.customer:string/catalyst_settings_title = 0x7f120041
com.mohandhass28.customer:string/catalyst_sample_profiler_toggle = 0x7f12003f
com.mohandhass28.customer:string/catalyst_report_button = 0x7f12003e
com.mohandhass28.customer:string/catalyst_reload_button = 0x7f12003c
com.mohandhass28.customer:string/catalyst_reload = 0x7f12003b
com.mohandhass28.customer:string/catalyst_open_debugger_error = 0x7f120038
com.mohandhass28.customer:styleable/AppBarLayout = 0x7f14000a
com.mohandhass28.customer:string/catalyst_hot_reloading_auto_enable = 0x7f120034
com.mohandhass28.customer:string/catalyst_heap_capture = 0x7f120031
com.mohandhass28.customer:string/catalyst_dismiss_button = 0x7f120030
com.mohandhass28.customer:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1300cf
com.mohandhass28.customer:string/crop_image_menu_crop = 0x7f120060
com.mohandhass28.customer:string/catalyst_dev_menu_header = 0x7f12002e
com.mohandhass28.customer:string/catalyst_debug_open = 0x7f12002c
com.mohandhass28.customer:string/catalyst_debug_connecting = 0x7f12002a
com.mohandhass28.customer:string/call_notification_incoming_text = 0x7f120025
com.mohandhass28.customer:string/call_notification_hang_up_action = 0x7f120024
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.Headline1 = 0x7f130226
com.mohandhass28.customer:string/call_notification_answer_video_action = 0x7f120022
com.mohandhass28.customer:string/bottom_sheet_behavior = 0x7f12001f
com.mohandhass28.customer:string/androidx_startup = 0x7f12001c
com.mohandhass28.customer:string/abc_shareactionprovider_share_with_application = 0x7f120019
com.mohandhass28.customer:styleable/Toolbar = 0x7f140094
com.mohandhass28.customer:string/abc_searchview_description_voice = 0x7f120017
com.mohandhass28.customer:string/abc_searchview_description_search = 0x7f120015
com.mohandhass28.customer:string/abc_searchview_description_query = 0x7f120014
com.mohandhass28.customer:string/abc_prepend_shortcut_label = 0x7f120011
com.mohandhass28.customer:string/abc_menu_meta_shortcut_label = 0x7f12000d
com.mohandhass28.customer:string/abc_menu_function_shortcut_label = 0x7f12000c
com.mohandhass28.customer:string/abc_menu_enter_shortcut_label = 0x7f12000b
com.mohandhass28.customer:string/abc_menu_ctrl_shortcut_label = 0x7f120009
com.mohandhass28.customer:string/abc_menu_alt_shortcut_label = 0x7f120008
com.mohandhass28.customer:string/abc_capital_on = 0x7f120007
com.mohandhass28.customer:string/abc_activitychooserview_choose_application = 0x7f120005
com.mohandhass28.customer:string/abc_activity_chooser_view_see_all = 0x7f120004
com.mohandhass28.customer:string/abc_action_bar_home_description = 0x7f120000
com.mohandhass28.customer:raw/otpelf = 0x7f110011
com.mohandhass28.customer:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_zocial = 0x7f110010
com.mohandhass28.customer:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_octicons = 0x7f11000e
com.mohandhass28.customer:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_materialicons = 0x7f11000d
com.mohandhass28.customer:style/Base.Widget.Design.TabLayout = 0x7f1300f2
com.mohandhass28.customer:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_foundation = 0x7f11000a
com.mohandhass28.customer:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f130096
com.mohandhass28.customer:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontisto = 0x7f110009
com.mohandhass28.customer:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_regular = 0x7f110007
com.mohandhass28.customer:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_brands = 0x7f110006
com.mohandhass28.customer:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_feather = 0x7f110004
com.mohandhass28.customer:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_antdesign = 0x7f110001
com.mohandhass28.customer:raw/firebase_common_keep = 0x7f110000
com.mohandhass28.customer:styleable/DefaultTimeBar = 0x7f140030
com.mohandhass28.customer:plurals/mtrl_badge_content_description = 0x7f100002
com.mohandhass28.customer:plurals/exo_controls_rewind_by_amount_description = 0x7f100001
com.mohandhass28.customer:plurals/exo_controls_fastforward_by_amount_description = 0x7f100000
com.mohandhass28.customer:mipmap/ic_launcher_round = 0x7f0f0002
com.mohandhass28.customer:mipmap/ic_launcher_foreground = 0x7f0f0001
com.mohandhass28.customer:mipmap/ic_launcher = 0x7f0f0000
com.mohandhass28.customer:menu/crop_image_menu = 0x7f0e0000
com.mohandhass28.customer:layout/text_view_without_line_height = 0x7f0d00a0
com.mohandhass28.customer:layout/text_view_with_line_height_from_style = 0x7f0d009e
com.mohandhass28.customer:layout/text_view_with_line_height_from_appearance = 0x7f0d009c
com.mohandhass28.customer:layout/test_toolbar_surface = 0x7f0d009b
com.mohandhass28.customer:layout/test_toolbar_elevation = 0x7f0d009a
com.mohandhass28.customer:layout/test_toolbar_custom_background = 0x7f0d0099
com.mohandhass28.customer:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f1303a6
com.mohandhass28.customer:layout/test_toolbar = 0x7f0d0098
com.mohandhass28.customer:layout/test_reflow_chipgroup = 0x7f0d0097
com.mohandhass28.customer:layout/test_navigation_bar_item_layout = 0x7f0d0096
com.mohandhass28.customer:layout/test_exposed_dropdown_menu = 0x7f0d0095
com.mohandhass28.customer:layout/test_design_radiobutton = 0x7f0d0094
com.mohandhass28.customer:layout/test_design_checkbox = 0x7f0d0093
com.mohandhass28.customer:layout/support_simple_spinner_dropdown_item = 0x7f0d0090
com.mohandhass28.customer:style/Widget.Autofill.InlineSuggestionEndIconStyle = 0x7f13035e
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.Chip = 0x7f130225
com.mohandhass28.customer:layout/single_item = 0x7f0d008e
com.mohandhass28.customer:layout/select_dialog_singlechoice_material = 0x7f0d008d
com.mohandhass28.customer:layout/sdk_integration_status = 0x7f0d008a
com.mohandhass28.customer:style/TextAppearance.Compat.Notification.Time.Media = 0x7f1301f1
com.mohandhass28.customer:layout/rzp_loader = 0x7f0d0088
com.mohandhass28.customer:layout/redbox_view = 0x7f0d0087
com.mohandhass28.customer:layout/redbox_item_title = 0x7f0d0086
com.mohandhass28.customer:layout/redbox_item_frame = 0x7f0d0085
com.mohandhass28.customer:layout/notification_template_media = 0x7f0d0080
com.mohandhass28.customer:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1300df
com.mohandhass28.customer:string/summary_description = 0x7f120131
com.mohandhass28.customer:layout/notification_template_lines_media = 0x7f0d007f
com.mohandhass28.customer:layout/notification_template_custom_big = 0x7f0d007d
com.mohandhass28.customer:layout/notification_action_tombstone = 0x7f0d0076
com.mohandhass28.customer:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1302e2
com.mohandhass28.customer:layout/mtrl_picker_text_input_date = 0x7f0d0073
com.mohandhass28.customer:layout/mtrl_picker_header_title_text = 0x7f0d0071
com.mohandhass28.customer:layout/mtrl_picker_header_selection_text = 0x7f0d0070
com.mohandhass28.customer:layout/mtrl_picker_header_fullscreen = 0x7f0d006f
com.mohandhass28.customer:layout/mtrl_picker_header_dialog = 0x7f0d006e
com.mohandhass28.customer:layout/mtrl_picker_fullscreen = 0x7f0d006d
com.mohandhass28.customer:style/ThemeOverlay.Material3.ActionBar = 0x7f1302b6
com.mohandhass28.customer:layout/mtrl_picker_dialog = 0x7f0d006c
com.mohandhass28.customer:layout/mtrl_picker_actions = 0x7f0d006b
com.mohandhass28.customer:layout/mtrl_navigation_rail_item = 0x7f0d006a
com.mohandhass28.customer:style/Platform.MaterialComponents.Dialog = 0x7f13015a
com.mohandhass28.customer:layout/mtrl_layout_snackbar = 0x7f0d0068
com.mohandhass28.customer:layout/mtrl_calendar_months = 0x7f0d0065
com.mohandhass28.customer:layout/mtrl_calendar_month = 0x7f0d0062
com.mohandhass28.customer:layout/mtrl_calendar_horizontal = 0x7f0d0061
com.mohandhass28.customer:layout/mtrl_calendar_days_of_week = 0x7f0d0060
com.mohandhass28.customer:layout/mtrl_calendar_day_of_week = 0x7f0d005f
com.mohandhass28.customer:layout/mtrl_auto_complete_simple_item = 0x7f0d005d
com.mohandhass28.customer:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f130089
com.mohandhass28.customer:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0d005c
com.mohandhass28.customer:layout/mtrl_alert_select_dialog_item = 0x7f0d005a
com.mohandhass28.customer:layout/mtrl_alert_dialog_actions = 0x7f0d0058
com.mohandhass28.customer:layout/mtrl_alert_dialog = 0x7f0d0057
com.mohandhass28.customer:layout/material_timepicker_textinput_display = 0x7f0d0056
com.mohandhass28.customer:layout/material_timepicker_dialog = 0x7f0d0055
com.mohandhass28.customer:layout/material_time_input = 0x7f0d0053
com.mohandhass28.customer:style/Widget.AppCompat.Light.PopupMenu = 0x7f13033f
com.mohandhass28.customer:style/Theme.AppCompat.Light = 0x7f130247
com.mohandhass28.customer:layout/material_time_chip = 0x7f0d0052
com.mohandhass28.customer:layout/material_clock_period_toggle = 0x7f0d004c
com.mohandhass28.customer:layout/material_clock_display = 0x7f0d004a
com.mohandhass28.customer:layout/material_chip_input_combo = 0x7f0d0049
com.mohandhass28.customer:layout/m3_auto_complete_simple_item = 0x7f0d0048
com.mohandhass28.customer:layout/m3_alert_dialog_title = 0x7f0d0047
com.mohandhass28.customer:layout/m3_alert_dialog = 0x7f0d0045
com.mohandhass28.customer:layout/ime_secondary_split_test_activity = 0x7f0d0044
com.mohandhass28.customer:layout/ime_base_split_test_activity = 0x7f0d0043
com.mohandhass28.customer:layout/expo_media_controller = 0x7f0d0040
com.mohandhass28.customer:layout/exo_track_selection_dialog = 0x7f0d003f
com.mohandhass28.customer:layout/exo_styled_settings_list = 0x7f0d003c
com.mohandhass28.customer:layout/exo_styled_player_view = 0x7f0d003b
com.mohandhass28.customer:layout/exo_styled_player_control_rewind_button = 0x7f0d0039
com.mohandhass28.customer:style/Widget.AppCompat.Light.SearchView = 0x7f130341
com.mohandhass28.customer:layout/exo_player_control_view = 0x7f0d0036
com.mohandhass28.customer:style/Widget.Material3.Button.TonalButton.Icon = 0x7f13038a
com.mohandhass28.customer:layout/design_text_input_start_icon = 0x7f0d0033
com.mohandhass28.customer:layout/design_navigation_menu_item = 0x7f0d0031
com.mohandhass28.customer:layout/design_navigation_item_separator = 0x7f0d002e
com.mohandhass28.customer:layout/design_navigation_item_header = 0x7f0d002d
com.mohandhass28.customer:layout/design_layout_tab_icon = 0x7f0d0029
com.mohandhass28.customer:layout/design_layout_snackbar_include = 0x7f0d0028
com.mohandhass28.customer:layout/design_bottom_sheet_dialog = 0x7f0d0026
com.mohandhass28.customer:layout/design_bottom_navigation_item = 0x7f0d0025
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f130298
com.mohandhass28.customer:layout/select_dialog_item_material = 0x7f0d008b
com.mohandhass28.customer:layout/crop_image_view = 0x7f0d0023
com.mohandhass28.customer:layout/crop_image_activity = 0x7f0d0022
com.mohandhass28.customer:layout/browser_actions_context_menu_row = 0x7f0d0021
com.mohandhass28.customer:layout/browser_actions_context_menu_page = 0x7f0d0020
com.mohandhass28.customer:layout/autofill_inline_suggestion = 0x7f0d001f
com.mohandhass28.customer:layout/amu_info_window = 0x7f0d001c
com.mohandhass28.customer:style/TextAppearance.AppCompat.Menu = 0x7f1301cd
com.mohandhass28.customer:layout/abc_tooltip = 0x7f0d001b
com.mohandhass28.customer:layout/abc_search_view = 0x7f0d0019
com.mohandhass28.customer:layout/abc_screen_simple = 0x7f0d0015
com.mohandhass28.customer:layout/abc_popup_menu_header_item_layout = 0x7f0d0012
com.mohandhass28.customer:style/Base.Theme.SplashScreen.DayNight = 0x7f130072
com.mohandhass28.customer:layout/abc_list_menu_item_radio = 0x7f0d0011
com.mohandhass28.customer:layout/abc_list_menu_item_icon = 0x7f0d000f
com.mohandhass28.customer:style/Base.Widget.Material3.FloatingActionButton = 0x7f1300fd
com.mohandhass28.customer:layout/abc_dialog_title_material = 0x7f0d000c
com.mohandhass28.customer:layout/abc_cascading_menu_item_layout = 0x7f0d000b
com.mohandhass28.customer:style/ShapeAppearanceOverlay.BottomRightCut = 0x7f130196
com.mohandhass28.customer:layout/abc_alert_dialog_material = 0x7f0d0009
com.mohandhass28.customer:layout/abc_alert_dialog_button_bar_material = 0x7f0d0008
com.mohandhass28.customer:layout/abc_activity_chooser_view_list_item = 0x7f0d0007
com.mohandhass28.customer:layout/abc_action_menu_item_layout = 0x7f0d0002
com.mohandhass28.customer:layout/abc_action_bar_up_container = 0x7f0d0001
com.mohandhass28.customer:style/TextAppearance.Compat.Notification.Info.Media = 0x7f1301ec
com.mohandhass28.customer:layout/abc_action_bar_title_item = 0x7f0d0000
com.mohandhass28.customer:interpolator/mtrl_fast_out_linear_in = 0x7f0c0007
com.mohandhass28.customer:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0c0005
com.mohandhass28.customer:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0c0004
com.mohandhass28.customer:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0c0003
com.mohandhass28.customer:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0c0002
com.mohandhass28.customer:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0c0001
com.mohandhass28.customer:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f130463
com.mohandhass28.customer:integer/status_bar_notification_info_maxnum = 0x7f0b0039
com.mohandhass28.customer:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f130406
com.mohandhass28.customer:integer/show_password_duration = 0x7f0b0038
com.mohandhass28.customer:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f13004e
com.mohandhass28.customer:integer/react_native_dev_server_port = 0x7f0b0037
com.mohandhass28.customer:integer/mtrl_view_visible = 0x7f0b0036
com.mohandhass28.customer:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f0b0033
com.mohandhass28.customer:integer/mtrl_card_anim_duration_ms = 0x7f0b0031
com.mohandhass28.customer:string/abc_searchview_description_clear = 0x7f120013
com.mohandhass28.customer:integer/mtrl_calendar_year_selector_span = 0x7f0b002f
com.mohandhass28.customer:integer/mtrl_calendar_header_orientation = 0x7f0b002d
com.mohandhass28.customer:integer/mtrl_badge_max_character_count = 0x7f0b002a
com.mohandhass28.customer:integer/material_motion_path = 0x7f0b0029
com.mohandhass28.customer:integer/material_motion_duration_short_2 = 0x7f0b0028
com.mohandhass28.customer:integer/material_motion_duration_medium_2 = 0x7f0b0026
com.mohandhass28.customer:integer/material_motion_duration_medium_1 = 0x7f0b0025
com.mohandhass28.customer:string/path_password_eye = 0x7f120115
com.mohandhass28.customer:integer/material_motion_duration_long_2 = 0x7f0b0024
com.mohandhass28.customer:integer/m3_sys_motion_duration_800 = 0x7f0b0020
com.mohandhass28.customer:integer/m3_sys_motion_duration_700 = 0x7f0b001f
com.mohandhass28.customer:integer/m3_sys_motion_duration_500 = 0x7f0b001c
com.mohandhass28.customer:integer/m3_sys_motion_duration_50 = 0x7f0b001b
com.mohandhass28.customer:style/TextAppearance.Design.Error = 0x7f1301f7
com.mohandhass28.customer:integer/m3_sys_motion_duration_400 = 0x7f0b0019
com.mohandhass28.customer:integer/m3_sys_motion_duration_300 = 0x7f0b0017
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f13003f
com.mohandhass28.customer:integer/m3_sys_motion_duration_250 = 0x7f0b0016
com.mohandhass28.customer:integer/m3_sys_motion_duration_200 = 0x7f0b0015
com.mohandhass28.customer:integer/m3_sys_motion_duration_150 = 0x7f0b0014
com.mohandhass28.customer:integer/m3_chip_anim_duration = 0x7f0b0011
com.mohandhass28.customer:integer/m3_card_anim_duration_ms = 0x7f0b0010
com.mohandhass28.customer:integer/m3_card_anim_delay_ms = 0x7f0b000f
com.mohandhass28.customer:integer/m3_btn_anim_delay_ms = 0x7f0b000d
com.mohandhass28.customer:integer/exo_media_button_opacity_percentage_enabled = 0x7f0b000a
com.mohandhass28.customer:integer/design_snackbar_text_max_lines = 0x7f0b0007
com.mohandhass28.customer:integer/config_tooltipAnimTime = 0x7f0b0005
com.mohandhass28.customer:integer/cancel_button_image_alpha = 0x7f0b0004
com.mohandhass28.customer:string/appbar_scrolling_view_behavior = 0x7f12001e
com.mohandhass28.customer:integer/app_bar_elevation_anim_duration = 0x7f0b0002
com.mohandhass28.customer:integer/abc_config_activityShortDur = 0x7f0b0001
com.mohandhass28.customer:integer/abc_config_activityDefaultDur = 0x7f0b0000
com.mohandhass28.customer:xml/standalone_badge = 0x7f15000a
com.mohandhass28.customer:id/zoom = 0x7f0a0283
com.mohandhass28.customer:id/zero_corner_chip = 0x7f0a0282
com.mohandhass28.customer:id/wrapped_composition_tag = 0x7f0a0281
com.mohandhass28.customer:id/wrap_content = 0x7f0a0280
com.mohandhass28.customer:id/withText = 0x7f0a027d
com.mohandhass28.customer:id/window = 0x7f0a027c
com.mohandhass28.customer:styleable/CoordinatorLayout_Layout = 0x7f14002c
com.mohandhass28.customer:id/wide = 0x7f0a027b
com.mohandhass28.customer:xml/rn_dev_preferences = 0x7f150006
com.mohandhass28.customer:id/webview = 0x7f0a0279
com.mohandhass28.customer:id/when_playing = 0x7f0a027a
com.mohandhass28.customer:id/visible = 0x7f0a0277
com.mohandhass28.customer:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0a0274
com.mohandhass28.customer:id/view_tree_lifecycle_owner = 0x7f0a0273
com.mohandhass28.customer:id/view_tag_instance_handle = 0x7f0a0271
com.mohandhass28.customer:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f130168
com.mohandhass28.customer:id/view_offset_helper = 0x7f0a0270
com.mohandhass28.customer:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f1302bf
com.mohandhass28.customer:id/video_decoder_gl_surface_view = 0x7f0a026f
com.mohandhass28.customer:integer/mtrl_btn_anim_duration_ms = 0x7f0b002c
com.mohandhass28.customer:id/up = 0x7f0a026c
com.mohandhass28.customer:id/unlabeled = 0x7f0a026b
com.mohandhass28.customer:id/uniform = 0x7f0a026a
com.mohandhass28.customer:id/unchecked = 0x7f0a0269
com.mohandhass28.customer:id/tv_title = 0x7f0a0268
com.mohandhass28.customer:layout/design_layout_tab_text = 0x7f0d002a
com.mohandhass28.customer:id/transition_transform = 0x7f0a0265
com.mohandhass28.customer:id/transition_scene_layoutid_cache = 0x7f0a0264
com.mohandhass28.customer:id/transition_position = 0x7f0a0263
com.mohandhass28.customer:id/transition_layout_save = 0x7f0a0262
com.mohandhass28.customer:id/transitionToStart = 0x7f0a0260
com.mohandhass28.customer:styleable/MenuView = 0x7f140063
com.mohandhass28.customer:styleable/MaterialRadioButton = 0x7f14005b
com.mohandhass28.customer:id/transform_origin = 0x7f0a025e
com.mohandhass28.customer:style/Base.Widget.Material3.ActionBar.Solid = 0x7f1300f3
com.mohandhass28.customer:id/transform = 0x7f0a025d
com.mohandhass28.customer:layout/material_clock_period_toggle_land = 0x7f0d004d
com.mohandhass28.customer:id/touch_outside = 0x7f0a025c
com.mohandhass28.customer:string/exo_controls_overflow_hide_description = 0x7f120071
com.mohandhass28.customer:id/topPanel = 0x7f0a025b
com.mohandhass28.customer:id/top = 0x7f0a025a
com.mohandhass28.customer:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f130287
com.mohandhass28.customer:id/title_template = 0x7f0a0258
com.mohandhass28.customer:id/titleDividerNoCustom = 0x7f0a0257
com.mohandhass28.customer:id/title = 0x7f0a0256
com.mohandhass28.customer:id/texture_view = 0x7f0a0254
com.mohandhass28.customer:id/textinput_placeholder = 0x7f0a0251
com.mohandhass28.customer:style/AndroidThemeColorAccentYellow = 0x7f130002
com.mohandhass28.customer:id/textinput_helper_text = 0x7f0a0250
com.mohandhass28.customer:id/textinput_error = 0x7f0a024f
com.mohandhass28.customer:id/textinput_counter = 0x7f0a024e
com.mohandhass28.customer:id/text_input_start_icon = 0x7f0a024d
com.mohandhass28.customer:id/text_input_error_icon = 0x7f0a024c
com.mohandhass28.customer:id/text_input_end_icon = 0x7f0a024b
com.mohandhass28.customer:id/textSpacerNoTitle = 0x7f0a0248
com.mohandhass28.customer:id/textSpacerNoButtons = 0x7f0a0247
com.mohandhass28.customer:id/textEnd = 0x7f0a0246
com.mohandhass28.customer:id/text = 0x7f0a0244
com.mohandhass28.customer:layout/mtrl_alert_dialog_title = 0x7f0d0059
com.mohandhass28.customer:id/test_radiobutton_app_button_tint = 0x7f0a0243
com.mohandhass28.customer:id/terrain = 0x7f0a023e
com.mohandhass28.customer:id/tag_unhandled_key_listeners = 0x7f0a023c
com.mohandhass28.customer:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_ionicons = 0x7f11000b
com.mohandhass28.customer:id/tag_transition_group = 0x7f0a023a
com.mohandhass28.customer:id/tag_state_description = 0x7f0a0239
com.mohandhass28.customer:layout/mtrl_calendar_day = 0x7f0d005e
com.mohandhass28.customer:id/tag_on_receive_content_mime_types = 0x7f0a0237
com.mohandhass28.customer:id/tag_on_receive_content_listener = 0x7f0a0236
com.mohandhass28.customer:id/tag_on_apply_window_listener = 0x7f0a0235
com.mohandhass28.customer:id/tag_accessibility_pane_title = 0x7f0a0234
com.mohandhass28.customer:id/tag_accessibility_heading = 0x7f0a0233
com.mohandhass28.customer:id/tag_accessibility_clickable_spans = 0x7f0a0232
com.mohandhass28.customer:id/tag_accessibility_actions = 0x7f0a0231
com.mohandhass28.customer:id/tabMode = 0x7f0a0230
com.mohandhass28.customer:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1301ce
com.mohandhass28.customer:id/submit_area = 0x7f0a022e
com.mohandhass28.customer:id/submenuarrow = 0x7f0a022d
com.mohandhass28.customer:id/strict_sandbox = 0x7f0a022c
com.mohandhass28.customer:id/stretch = 0x7f0a022b
com.mohandhass28.customer:string/exposed_dropdown_menu_content_description = 0x7f1200a2
com.mohandhass28.customer:string/common_google_play_services_update_button = 0x7f120054
com.mohandhass28.customer:id/surface_view = 0x7f0a022f
com.mohandhass28.customer:id/stop = 0x7f0a022a
com.mohandhass28.customer:id/staticLayout = 0x7f0a0227
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f130430
com.mohandhass28.customer:id/startVertical = 0x7f0a0226
com.mohandhass28.customer:id/startHorizontal = 0x7f0a0224
com.mohandhass28.customer:id/start = 0x7f0a0223
com.mohandhass28.customer:id/src_in = 0x7f0a0220
com.mohandhass28.customer:string/spinbutton_description = 0x7f120125
com.mohandhass28.customer:id/square = 0x7f0a021e
com.mohandhass28.customer:id/spline = 0x7f0a021a
com.mohandhass28.customer:id/splashscreen_icon_view = 0x7f0a0219
com.mohandhass28.customer:id/spherical_gl_surface_view = 0x7f0a0218
com.mohandhass28.customer:id/special_effects_controller_view_tag = 0x7f0a0217
com.mohandhass28.customer:id/spacer = 0x7f0a0216
com.mohandhass28.customer:id/snapMargins = 0x7f0a0215
com.mohandhass28.customer:string/close_sheet = 0x7f120048
com.mohandhass28.customer:id/snackbar_text = 0x7f0a0213
com.mohandhass28.customer:style/Widget.AppCompat.ActionButton = 0x7f130318
com.mohandhass28.customer:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f130205
com.mohandhass28.customer:id/slide = 0x7f0a0211
com.mohandhass28.customer:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f13045f
com.mohandhass28.customer:style/Platform.MaterialComponents.Light.Dialog = 0x7f13015c
com.mohandhass28.customer:id/skip_next_button = 0x7f0a020f
com.mohandhass28.customer:id/skipCollapsed = 0x7f0a020e
com.mohandhass28.customer:id/sin = 0x7f0a020d
com.mohandhass28.customer:id/showTitle = 0x7f0a020c
com.mohandhass28.customer:id/showHome = 0x7f0a020b
com.mohandhass28.customer:id/showCustom = 0x7f0a020a
com.mohandhass28.customer:id/shortcut = 0x7f0a0209
com.mohandhass28.customer:string/enter_fullscreen_mode = 0x7f120066
com.mohandhass28.customer:id/selectionDetails = 0x7f0a0207
com.mohandhass28.customer:string/exo_controls_stop_description = 0x7f120080
com.mohandhass28.customer:id/selected = 0x7f0a0206
com.mohandhass28.customer:string/project_id = 0x7f12011d
com.mohandhass28.customer:id/select_dialog_listview = 0x7f0a0205
com.mohandhass28.customer:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f130342
com.mohandhass28.customer:id/search_voice_btn = 0x7f0a0203
com.mohandhass28.customer:id/search_mag_icon = 0x7f0a0200
com.mohandhass28.customer:id/search_edit_frame = 0x7f0a01fe
com.mohandhass28.customer:id/search_close_btn = 0x7f0a01fd
com.mohandhass28.customer:style/Base.V21.Theme.MaterialComponents.Light = 0x7f13009e
com.mohandhass28.customer:id/search_button = 0x7f0a01fc
com.mohandhass28.customer:id/search_bar = 0x7f0a01fb
com.mohandhass28.customer:id/search_badge = 0x7f0a01fa
com.mohandhass28.customer:id/scrollable = 0x7f0a01f9
com.mohandhass28.customer:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1300d9
com.mohandhass28.customer:id/scrollView = 0x7f0a01f8
com.mohandhass28.customer:id/scrollIndicatorUp = 0x7f0a01f7
com.mohandhass28.customer:id/scrollIndicatorDown = 0x7f0a01f6
com.mohandhass28.customer:id/scale = 0x7f0a01f3
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar = 0x7f1303b8
com.mohandhass28.customer:id/save_overlay_view = 0x7f0a01f1
com.mohandhass28.customer:id/save_non_transition_alpha = 0x7f0a01f0
com.mohandhass28.customer:id/rzp_theMainMagicView = 0x7f0a01ed
com.mohandhass28.customer:id/rzp_outerbox = 0x7f0a01eb
com.mohandhass28.customer:id/rounded = 0x7f0a01e8
com.mohandhass28.customer:id/rn_redbox_report_label = 0x7f0a01e5
com.mohandhass28.customer:id/role = 0x7f0a01e7
com.mohandhass28.customer:id/rn_redbox_report_button = 0x7f0a01e4
com.mohandhass28.customer:id/rn_redbox_reload_button = 0x7f0a01e3
com.mohandhass28.customer:style/CardView.Dark = 0x7f13011a
com.mohandhass28.customer:id/rn_redbox_loading_indicator = 0x7f0a01e2
com.mohandhass28.customer:id/rn_redbox_line_separator = 0x7f0a01e1
com.mohandhass28.customer:style/SpinnerTimePickerDialogBase = 0x7f1301ad
com.mohandhass28.customer:id/rn_redbox_dismiss_button = 0x7f0a01e0
com.mohandhass28.customer:id/rn_frame_method = 0x7f0a01df
com.mohandhass28.customer:string/material_motion_easing_accelerated = 0x7f1200dd
com.mohandhass28.customer:id/right_icon = 0x7f0a01dc
com.mohandhass28.customer:id/rightToLeft = 0x7f0a01db
com.mohandhass28.customer:id/right = 0x7f0a01da
com.mohandhass28.customer:style/Base.v27.Theme.SplashScreen = 0x7f130115
com.mohandhass28.customer:id/reverseSawtooth = 0x7f0a01d8
com.mohandhass28.customer:string/ic_flip_24_horizontally = 0x7f1200bb
com.mohandhass28.customer:id/report_drawn = 0x7f0a01d7
com.mohandhass28.customer:styleable/MotionScene = 0x7f140068
com.mohandhass28.customer:id/rectangles = 0x7f0a01d6
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Display2 = 0x7f13001c
com.mohandhass28.customer:id/rectangleVerticalOnly = 0x7f0a01d5
com.mohandhass28.customer:id/rectangle = 0x7f0a01d3
com.mohandhass28.customer:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f130046
com.mohandhass28.customer:id/react_test_id = 0x7f0a01d2
com.mohandhass28.customer:id/progress_circular = 0x7f0a01ce
com.mohandhass28.customer:id/progressBar = 0x7f0a01cd
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1303c9
com.mohandhass28.customer:id/production = 0x7f0a01cc
com.mohandhass28.customer:id/pooling_container_listener_holder_tag = 0x7f0a01c9
com.mohandhass28.customer:id/play_button = 0x7f0a01c7
com.mohandhass28.customer:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_entypo = 0x7f110002
com.mohandhass28.customer:id/pin = 0x7f0a01c6
com.mohandhass28.customer:id/pathRelative = 0x7f0a01c2
com.mohandhass28.customer:id/path = 0x7f0a01c1
com.mohandhass28.customer:id/password_toggle = 0x7f0a01c0
com.mohandhass28.customer:id/parentRelative = 0x7f0a01be
com.mohandhass28.customer:style/TextAppearance.Design.Counter.Overflow = 0x7f1301f6
com.mohandhass28.customer:id/parent = 0x7f0a01bc
com.mohandhass28.customer:id/parallax = 0x7f0a01bb
com.mohandhass28.customer:id/outline = 0x7f0a01b7
com.mohandhass28.customer:id/on = 0x7f0a01b4
com.mohandhass28.customer:string/m3_sys_motion_easing_linear = 0x7f1200d3
com.mohandhass28.customer:id/notification_main_column_container = 0x7f0a01b2
com.mohandhass28.customer:id/notification_main_column = 0x7f0a01b1
com.mohandhass28.customer:id/notification_background = 0x7f0a01b0
com.mohandhass28.customer:id/none = 0x7f0a01ae
com.mohandhass28.customer:id/never = 0x7f0a01ac
com.mohandhass28.customer:id/navigation_bar_item_active_indicator_view = 0x7f0a01a5
com.mohandhass28.customer:id/multiply = 0x7f0a01a4
com.mohandhass28.customer:id/mtrl_picker_title_text = 0x7f0a01a2
com.mohandhass28.customer:id/mtrl_picker_text_input_range_start = 0x7f0a01a1
com.mohandhass28.customer:style/Widget.Material3.CardView.Filled = 0x7f13038d
com.mohandhass28.customer:id/mtrl_picker_text_input_range_end = 0x7f0a01a0
com.mohandhass28.customer:id/mtrl_picker_header_toggle = 0x7f0a019e
com.mohandhass28.customer:id/mtrl_picker_header_title_and_selection = 0x7f0a019d
com.mohandhass28.customer:id/mtrl_picker_header_selection_text = 0x7f0a019c
com.mohandhass28.customer:id/mtrl_picker_text_input_date = 0x7f0a019f
com.mohandhass28.customer:id/mtrl_picker_header = 0x7f0a019b
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1301de
com.mohandhass28.customer:id/mtrl_picker_fullscreen = 0x7f0a019a
com.mohandhass28.customer:id/mtrl_internal_children_alpha_tag = 0x7f0a0198
com.mohandhass28.customer:id/mtrl_calendar_text_input_frame = 0x7f0a0194
com.mohandhass28.customer:style/Widget.Material3.LinearProgressIndicator = 0x7f1303b7
com.mohandhass28.customer:id/mtrl_calendar_months = 0x7f0a0192
com.mohandhass28.customer:id/mtrl_calendar_main_pane = 0x7f0a0191
com.mohandhass28.customer:id/mtrl_calendar_frame = 0x7f0a0190
com.mohandhass28.customer:style/ShapeAppearance.M3.Sys.Shape.Corner.Large.End = 0x7f13017d
com.mohandhass28.customer:id/mtrl_calendar_days_of_week = 0x7f0a018f
com.mohandhass28.customer:id/mtrl_calendar_day_selector_frame = 0x7f0a018e
com.mohandhass28.customer:id/mtrl_anchor_parent = 0x7f0a018d
com.mohandhass28.customer:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f130326
com.mohandhass28.customer:id/motion_base = 0x7f0a018c
com.mohandhass28.customer:id/month_navigation_previous = 0x7f0a018a
com.mohandhass28.customer:id/month_navigation_next = 0x7f0a0189
com.mohandhass28.customer:id/month_navigation_bar = 0x7f0a0187
com.mohandhass28.customer:id/month_grid = 0x7f0a0186
com.mohandhass28.customer:id/mix_blend_mode = 0x7f0a0184
com.mohandhass28.customer:string/common_google_play_services_install_title = 0x7f12004f
com.mohandhass28.customer:id/mini = 0x7f0a0183
com.mohandhass28.customer:id/snap = 0x7f0a0214
com.mohandhass28.customer:id/middle = 0x7f0a0182
com.mohandhass28.customer:id/message = 0x7f0a0181
com.mohandhass28.customer:id/media_controller_compat_view_tag = 0x7f0a0180
com.mohandhass28.customer:string/google_app_id = 0x7f1200b5
com.mohandhass28.customer:id/material_value_index = 0x7f0a017d
com.mohandhass28.customer:id/material_timepicker_ok_button = 0x7f0a017b
com.mohandhass28.customer:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f1303da
com.mohandhass28.customer:id/material_timepicker_mode_button = 0x7f0a017a
com.mohandhass28.customer:id/material_timepicker_edit_text = 0x7f0a0179
com.mohandhass28.customer:id/material_textinput_timepicker = 0x7f0a0176
com.mohandhass28.customer:id/material_minute_tv = 0x7f0a0175
com.mohandhass28.customer:layout/wallet_test_layout = 0x7f0d00a1
com.mohandhass28.customer:id/material_hour_tv = 0x7f0a0172
com.mohandhass28.customer:id/material_clock_period_toggle = 0x7f0a0170
com.mohandhass28.customer:id/material_clock_period_pm_button = 0x7f0a016f
com.mohandhass28.customer:style/Widget.MaterialComponents.TimePicker.Display = 0x7f130468
com.mohandhass28.customer:string/exo_track_selection_title_audio = 0x7f120096
com.mohandhass28.customer:id/material_clock_hand = 0x7f0a016d
com.mohandhass28.customer:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f1301a3
com.mohandhass28.customer:id/material_clock_face = 0x7f0a016c
com.mohandhass28.customer:id/match_parent = 0x7f0a0169
com.mohandhass28.customer:id/masked = 0x7f0a0168
com.mohandhass28.customer:id/logo_only = 0x7f0a0167
com.mohandhass28.customer:style/Widget.Material3.Chip.Filter = 0x7f130392
com.mohandhass28.customer:style/DialogAnimationSlide = 0x7f13011e
com.mohandhass28.customer:id/linear = 0x7f0a0163
com.mohandhass28.customer:id/line3 = 0x7f0a0162
com.mohandhass28.customer:id/line1 = 0x7f0a0161
com.mohandhass28.customer:id/light = 0x7f0a0160
com.mohandhass28.customer:style/Theme.Material3.DynamicColors.Light = 0x7f13026c
com.mohandhass28.customer:id/leftToRight = 0x7f0a015f
com.mohandhass28.customer:string/is_expo_go = 0x7f1200c4
com.mohandhass28.customer:id/left = 0x7f0a015e
com.mohandhass28.customer:id/layout = 0x7f0a015d
com.mohandhass28.customer:id/right_side = 0x7f0a01dd
com.mohandhass28.customer:id/labelled_by = 0x7f0a015c
com.mohandhass28.customer:id/labeled = 0x7f0a015b
com.mohandhass28.customer:id/item_touch_helper_previous_elevation = 0x7f0a0157
com.mohandhass28.customer:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f130093
com.mohandhass28.customer:id/italic = 0x7f0a0156
com.mohandhass28.customer:id/startToEnd = 0x7f0a0225
com.mohandhass28.customer:id/is_pooling_container_tag = 0x7f0a0155
com.mohandhass28.customer:id/invalidate_transform = 0x7f0a0152
com.mohandhass28.customer:id/ignoreRequest = 0x7f0a014e
com.mohandhass28.customer:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f130401
com.mohandhass28.customer:id/icon_group = 0x7f0a014a
com.mohandhass28.customer:id/time = 0x7f0a0255
com.mohandhass28.customer:id/ic_rotate_right_24 = 0x7f0a0148
com.mohandhass28.customer:id/ic_rotate_left_24 = 0x7f0a0147
com.mohandhass28.customer:id/ic_flip_24_vertically = 0x7f0a0146
com.mohandhass28.customer:id/ic_flip_24 = 0x7f0a0144
com.mohandhass28.customer:styleable/State = 0x7f140085
com.mohandhass28.customer:id/hybrid = 0x7f0a0143
com.mohandhass28.customer:id/honorRequest = 0x7f0a0142
com.mohandhass28.customer:style/Widget.AppCompat.TextView = 0x7f130358
com.mohandhass28.customer:id/homeAsUp = 0x7f0a0141
com.mohandhass28.customer:style/Widget.MaterialComponents.ProgressIndicator = 0x7f13044f
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1303cd
com.mohandhass28.customer:id/holo_dark = 0x7f0a013e
com.mohandhass28.customer:id/hideable = 0x7f0a013d
com.mohandhass28.customer:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1302d3
com.mohandhass28.customer:style/ShapeAppearanceOverlay.DifferentCornerSize = 0x7f130198
com.mohandhass28.customer:id/hide_in_inspector_tag = 0x7f0a013c
com.mohandhass28.customer:id/hide_ime_id = 0x7f0a013b
com.mohandhass28.customer:id/hide_graphics_layer_in_inspector_tag = 0x7f0a013a
com.mohandhass28.customer:style/Widget.AppCompat.Toolbar = 0x7f13035a
com.mohandhass28.customer:id/header_title = 0x7f0a0139
com.mohandhass28.customer:id/guideline = 0x7f0a0138
com.mohandhass28.customer:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1303d7
com.mohandhass28.customer:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f1302bd
com.mohandhass28.customer:id/groups = 0x7f0a0137
com.mohandhass28.customer:style/Widget.Design.Snackbar = 0x7f13036b
com.mohandhass28.customer:id/google_wallet_monochrome = 0x7f0a0132
com.mohandhass28.customer:id/google_wallet_classic = 0x7f0a0130
com.mohandhass28.customer:string/fingerprint_error_hw_not_available = 0x7f1200aa
com.mohandhass28.customer:id/ghost_view_holder = 0x7f0a012c
com.mohandhass28.customer:string/exo_track_resolution = 0x7f12008f
com.mohandhass28.customer:id/material_clock_display = 0x7f0a016b
com.mohandhass28.customer:id/ghost_view = 0x7f0a012b
com.mohandhass28.customer:id/month_title = 0x7f0a018b
com.mohandhass28.customer:id/fullscreen_mode_button = 0x7f0a012a
com.mohandhass28.customer:id/fragment_container_view_tag = 0x7f0a0128
com.mohandhass28.customer:id/fps_text = 0x7f0a0127
com.mohandhass28.customer:id/forever = 0x7f0a0126
com.mohandhass28.customer:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f130193
com.mohandhass28.customer:id/fixed_width = 0x7f0a0122
com.mohandhass28.customer:id/fixed_height = 0x7f0a0121
com.mohandhass28.customer:id/fitToContents = 0x7f0a011e
com.mohandhass28.customer:string/common_google_play_services_update_title = 0x7f120056
com.mohandhass28.customer:id/progress_horizontal = 0x7f0a01cf
com.mohandhass28.customer:id/fitEnd = 0x7f0a011c
com.mohandhass28.customer:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f1303d8
com.mohandhass28.customer:style/Widget.AppCompat.PopupMenu = 0x7f130348
com.mohandhass28.customer:id/fitCenter = 0x7f0a011b
com.mohandhass28.customer:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f1303e9
com.mohandhass28.customer:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1300a0
com.mohandhass28.customer:id/fitBottomStart = 0x7f0a011a
com.mohandhass28.customer:id/fingerprint_subtitle = 0x7f0a0118
com.mohandhass28.customer:id/fingerprint_error = 0x7f0a0116
com.mohandhass28.customer:integer/google_play_services_version = 0x7f0b000b
com.mohandhass28.customer:id/filled = 0x7f0a0113
com.mohandhass28.customer:style/Widget.AppCompat.ProgressBar = 0x7f13034b
com.mohandhass28.customer:id/parentPanel = 0x7f0a01bd
com.mohandhass28.customer:id/fillStart = 0x7f0a0110
com.mohandhass28.customer:id/fillEnd = 0x7f0a010f
com.mohandhass28.customer:id/fillCenter = 0x7f0a010e
com.mohandhass28.customer:id/transition_current_scene = 0x7f0a0261
com.mohandhass28.customer:id/radio = 0x7f0a01d0
com.mohandhass28.customer:id/fill = 0x7f0a010d
com.mohandhass28.customer:id/fast_forward_button = 0x7f0a010c
com.mohandhass28.customer:id/fade = 0x7f0a010b
com.mohandhass28.customer:id/expand_activities_button = 0x7f0a0109
com.mohandhass28.customer:id/exo_track_selection_view = 0x7f0a0107
com.mohandhass28.customer:id/position = 0x7f0a01ca
com.mohandhass28.customer:id/exo_time = 0x7f0a0106
com.mohandhass28.customer:id/exo_text = 0x7f0a0105
com.mohandhass28.customer:id/exo_subtitles = 0x7f0a0104
com.mohandhass28.customer:id/exo_subtitle = 0x7f0a0103
com.mohandhass28.customer:id/exo_sub_text = 0x7f0a0102
com.mohandhass28.customer:id/exo_shutter = 0x7f0a0101
com.mohandhass28.customer:style/ThemeOverlay.AppCompat.Light = 0x7f1302b3
com.mohandhass28.customer:id/exo_settings_listview = 0x7f0a00ff
com.mohandhass28.customer:drawable/assets_resimages_4 = 0x7f080076
com.mohandhass28.customer:id/exo_settings = 0x7f0a00fe
com.mohandhass28.customer:id/exo_extra_controls = 0x7f0a00e6
com.mohandhass28.customer:id/exo_repeat_toggle = 0x7f0a00fb
com.mohandhass28.customer:id/exo_position = 0x7f0a00f7
com.mohandhass28.customer:color/cardview_light_background = 0x7f060031
com.mohandhass28.customer:id/exo_play_pause = 0x7f0a00f5
com.mohandhass28.customer:style/TextAppearance.Compat.Notification.Line2 = 0x7f1301ed
com.mohandhass28.customer:color/mtrl_textinput_filled_box_default_background_color = 0x7f060253
com.mohandhass28.customer:id/exo_play = 0x7f0a00f4
com.mohandhass28.customer:string/mtrl_picker_text_input_date_range_start_hint = 0x7f120109
com.mohandhass28.customer:color/m3_sys_color_light_error = 0x7f06018d
com.mohandhass28.customer:id/exo_minimal_fullscreen = 0x7f0a00ee
com.mohandhass28.customer:id/exo_minimal_controls = 0x7f0a00ed
com.mohandhass28.customer:style/Theme.Design = 0x7f130253
com.mohandhass28.customer:id/exo_icon = 0x7f0a00eb
com.mohandhass28.customer:string/abc_menu_space_shortcut_label = 0x7f12000f
com.mohandhass28.customer:id/exo_ffwd = 0x7f0a00e8
com.mohandhass28.customer:id/exo_extra_controls_scroll_view = 0x7f0a00e7
com.mohandhass28.customer:id/exo_duration = 0x7f0a00e4
com.mohandhass28.customer:id/exo_controls_background = 0x7f0a00e3
com.mohandhass28.customer:id/exo_controller_placeholder = 0x7f0a00e2
com.mohandhass28.customer:id/exo_buffering = 0x7f0a00dd
com.mohandhass28.customer:color/material_blue_grey_950 = 0x7f0601bd
com.mohandhass28.customer:drawable/notification_template_icon_low_bg = 0x7f080157
com.mohandhass28.customer:drawable/exo_styled_controls_speed = 0x7f0800fc
com.mohandhass28.customer:id/exo_bottom_bar = 0x7f0a00dc
com.mohandhass28.customer:style/TextAppearance.Material3.LabelSmall = 0x7f13021b
com.mohandhass28.customer:attr/cropBorderCornerColor = 0x7f04013a
com.mohandhass28.customer:id/exo_audio_track = 0x7f0a00da
com.mohandhass28.customer:id/edit_text_id = 0x7f0a00cd
com.mohandhass28.customer:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1300b3
com.mohandhass28.customer:drawable/ic_call_answer_video = 0x7f080107
com.mohandhass28.customer:id/easeOut = 0x7f0a00cb
com.mohandhass28.customer:id/standard = 0x7f0a0222
com.mohandhass28.customer:id/easeInOut = 0x7f0a00ca
com.mohandhass28.customer:id/test_radiobutton_android_button_tint = 0x7f0a0242
com.mohandhass28.customer:attr/materialAlertDialogBodyTextStyle = 0x7f0402c7
com.mohandhass28.customer:id/easeIn = 0x7f0a00c9
com.mohandhass28.customer:dimen/abc_alert_dialog_button_dimen = 0x7f070011
com.mohandhass28.customer:id/dropdown_menu = 0x7f0a00c7
com.mohandhass28.customer:attr/colorOnSecondaryContainer = 0x7f0400f3
com.mohandhass28.customer:id/dragStart = 0x7f0a00c4
com.mohandhass28.customer:color/common_google_signin_btn_text_light_default = 0x7f06003f
com.mohandhass28.customer:id/dragRight = 0x7f0a00c3
com.mohandhass28.customer:id/dragLeft = 0x7f0a00c2
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f130064
com.mohandhass28.customer:layout/abc_list_menu_item_layout = 0x7f0d0010
com.mohandhass28.customer:id/dragEnd = 0x7f0a00c1
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f130065
com.mohandhass28.customer:id/tag_screen_reader_focusable = 0x7f0a0238
com.mohandhass28.customer:id/donate_with = 0x7f0a00be
com.mohandhass28.customer:id/disjoint = 0x7f0a00bd
com.mohandhass28.customer:color/m3_timepicker_secondary_text_button_text_color = 0x7f0601b9
com.mohandhass28.customer:id/disableScroll = 0x7f0a00bc
com.mohandhass28.customer:id/disableHome = 0x7f0a00ba
com.mohandhass28.customer:id/direct = 0x7f0a00b9
com.mohandhass28.customer:style/Theme.MaterialComponents.Dialog = 0x7f130288
com.mohandhass28.customer:id/dialog_button = 0x7f0a00b7
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f130296
com.mohandhass28.customer:color/m3_ref_palette_primary40 = 0x7f06011b
com.mohandhass28.customer:id/design_menu_item_text = 0x7f0a00b5
com.mohandhass28.customer:drawable/exo_rounded_rectangle = 0x7f0800e9
com.mohandhass28.customer:id/design_menu_item_action_area = 0x7f0a00b3
com.mohandhass28.customer:id/default_activity_button = 0x7f0a00b0
com.mohandhass28.customer:id/decelerateAndComplete = 0x7f0a00ae
com.mohandhass28.customer:dimen/mtrl_slider_label_radius = 0x7f070230
com.mohandhass28.customer:id/cut = 0x7f0a00aa
com.mohandhass28.customer:string/common_google_play_services_notification_channel_name = 0x7f120050
com.mohandhass28.customer:id/customPanel = 0x7f0a00a9
com.mohandhass28.customer:id/current_time_text = 0x7f0a00a7
com.mohandhass28.customer:id/crop_image_menu_crop = 0x7f0a00a6
com.mohandhass28.customer:id/coordinator = 0x7f0a00a2
com.mohandhass28.customer:attr/endIconDrawable = 0x7f0401a0
com.mohandhass28.customer:id/contiguous = 0x7f0a00a1
com.mohandhass28.customer:layout/abc_select_dialog_material = 0x7f0d001a
com.mohandhass28.customer:id/packed = 0x7f0a01ba
com.mohandhass28.customer:id/content = 0x7f0a009f
com.mohandhass28.customer:id/container = 0x7f0a009e
com.mohandhass28.customer:id/confirm_button = 0x7f0a009c
com.mohandhass28.customer:id/design_bottom_sheet = 0x7f0a00b2
com.mohandhass28.customer:id/compress = 0x7f0a009b
com.mohandhass28.customer:string/fingerprint_dialog_touch_sensor = 0x7f1200a9
com.mohandhass28.customer:id/compatible = 0x7f0a0099
com.mohandhass28.customer:drawable/exo_ic_fullscreen_enter = 0x7f0800c6
com.mohandhass28.customer:id/collapseActionView = 0x7f0a0098
com.mohandhass28.customer:string/exo_controls_repeat_off_description = 0x7f120078
com.mohandhass28.customer:dimen/mtrl_shape_corner_size_small_component = 0x7f07022d
com.mohandhass28.customer:id/clip_horizontal = 0x7f0a0095
com.mohandhass28.customer:id/clear_text = 0x7f0a0094
com.mohandhass28.customer:id/chip_group = 0x7f0a0090
com.mohandhass28.customer:dimen/item_touch_helper_swipe_escape_velocity = 0x7f0700c5
com.mohandhass28.customer:id/chains = 0x7f0a0088
com.mohandhass28.customer:id/exo_controller = 0x7f0a00e1
com.mohandhass28.customer:string/tooltip_label = 0x7f120139
com.mohandhass28.customer:dimen/mtrl_calendar_year_width = 0x7f0701e1
com.mohandhass28.customer:id/cancel_button = 0x7f0a0080
com.mohandhass28.customer:id/buy_with = 0x7f0a007d
com.mohandhass28.customer:layout/m3_alert_dialog_actions = 0x7f0d0046
com.mohandhass28.customer:id/buyButton = 0x7f0a007b
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f130303
com.mohandhass28.customer:color/m3_ref_palette_primary95 = 0x7f060121
com.mohandhass28.customer:id/button = 0x7f0a0078
com.mohandhass28.customer:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f13023f
com.mohandhass28.customer:id/bounce = 0x7f0a0072
com.mohandhass28.customer:string/fallback_menu_item_share_link = 0x7f1200a7
com.mohandhass28.customer:id/blocking = 0x7f0a006f
com.mohandhass28.customer:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f130057
com.mohandhass28.customer:id/ifRoom = 0x7f0a014c
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_primary_container = 0x7f060185
com.mohandhass28.customer:id/beginning = 0x7f0a006e
com.mohandhass28.customer:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f0701b1
com.mohandhass28.customer:id/beginOnFirstDraw = 0x7f0a006d
com.mohandhass28.customer:id/baseline = 0x7f0a006c
com.mohandhass28.customer:id/barrier = 0x7f0a006b
com.mohandhass28.customer:id/autofill_inline_suggestion_title = 0x7f0a006a
com.mohandhass28.customer:integer/m3_sys_motion_duration_900 = 0x7f0b0021
com.mohandhass28.customer:id/autofill_inline_suggestion_start_icon = 0x7f0a0068
com.mohandhass28.customer:id/auto = 0x7f0a0063
com.mohandhass28.customer:id/animateToEnd = 0x7f0a005e
com.mohandhass28.customer:id/android_pay_light = 0x7f0a005b
com.mohandhass28.customer:id/SHOW_ALL = 0x7f0a000b
com.mohandhass28.customer:id/always = 0x7f0a0057
com.mohandhass28.customer:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1300de
com.mohandhass28.customer:id/aligned = 0x7f0a0055
com.mohandhass28.customer:style/TextAppearance.Material3.DisplayMedium = 0x7f130214
com.mohandhass28.customer:id/adjust_height = 0x7f0a0052
com.mohandhass28.customer:style/Platform.V21.AppCompat.Light = 0x7f130161
com.mohandhass28.customer:layout/design_menu_item_action_area = 0x7f0d002b
com.mohandhass28.customer:animator/linear_indeterminate_line2_head_interpolator = 0x7f02000b
com.mohandhass28.customer:id/actions = 0x7f0a004f
com.mohandhass28.customer:attr/scrubber_drawable = 0x7f04038a
com.mohandhass28.customer:color/m3_ref_palette_dynamic_secondary40 = 0x7f0600da
com.mohandhass28.customer:id/action_mode_close_button = 0x7f0a004d
com.mohandhass28.customer:attr/contentInsetEndWithActions = 0x7f040113
com.mohandhass28.customer:attr/listPreferredItemPaddingRight = 0x7f0402b3
com.mohandhass28.customer:id/action_mode_bar = 0x7f0a004b
com.mohandhass28.customer:color/mtrl_outlined_stroke_color = 0x7f060248
com.mohandhass28.customer:id/action_menu_presenter = 0x7f0a004a
com.mohandhass28.customer:color/abc_tint_seek_thumb = 0x7f060016
com.mohandhass28.customer:id/action_context_bar = 0x7f0a0046
com.mohandhass28.customer:id/action_bar_subtitle = 0x7f0a0043
com.mohandhass28.customer:attr/checkedIconTint = 0x7f0400af
com.mohandhass28.customer:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f07013f
com.mohandhass28.customer:id/action_bar_root = 0x7f0a0041
com.mohandhass28.customer:id/action0 = 0x7f0a003d
com.mohandhass28.customer:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f130332
com.mohandhass28.customer:attr/checkMarkTint = 0x7f0400a5
com.mohandhass28.customer:id/accessibility_hint = 0x7f0a0036
com.mohandhass28.customer:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f130075
com.mohandhass28.customer:string/common_signin_button_text = 0x7f12005a
com.mohandhass28.customer:attr/layout_editor_absoluteX = 0x7f040292
com.mohandhass28.customer:attr/lineHeight = 0x7f0402a3
com.mohandhass28.customer:id/accessibility_custom_action_30 = 0x7f0a002e
com.mohandhass28.customer:id/accessibility_custom_action_28 = 0x7f0a002b
com.mohandhass28.customer:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f130349
com.mohandhass28.customer:id/accessibility_custom_action_27 = 0x7f0a002a
com.mohandhass28.customer:id/accessibility_custom_action_24 = 0x7f0a0027
com.mohandhass28.customer:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_materialcommunityicons = 0x7f11000c
com.mohandhass28.customer:attr/titleEnabled = 0x7f040450
com.mohandhass28.customer:id/accessibility_custom_action_19 = 0x7f0a0021
com.mohandhass28.customer:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f080012
com.mohandhass28.customer:id/accessibility_custom_action_18 = 0x7f0a0020
com.mohandhass28.customer:attr/tabPaddingEnd = 0x7f0403f7
com.mohandhass28.customer:attr/layout_collapseParallaxMultiplier = 0x7f040266
com.mohandhass28.customer:drawable/notification_bg_normal_pressed = 0x7f080153
com.mohandhass28.customer:id/accessibility_custom_action_15 = 0x7f0a001d
com.mohandhass28.customer:attr/show_timeout = 0x7f0403a9
com.mohandhass28.customer:id/accessibility_custom_action_14 = 0x7f0a001c
com.mohandhass28.customer:id/accessibility_custom_action_10 = 0x7f0a0018
com.mohandhass28.customer:drawable/assets_profileimgcopy_profileimg = 0x7f080073
com.mohandhass28.customer:id/browser_actions_header_text = 0x7f0a0073
com.mohandhass28.customer:color/mtrl_textinput_focused_box_stroke_color = 0x7f060254
com.mohandhass28.customer:id/accessibility_custom_action_0 = 0x7f0a0016
com.mohandhass28.customer:id/accessibility_collection = 0x7f0a0014
com.mohandhass28.customer:id/accessibility_action_clickable_span = 0x7f0a0012
com.mohandhass28.customer:id/accessibility_value = 0x7f0a003c
com.mohandhass28.customer:attr/lastBaselineToBottomHeight = 0x7f040258
com.mohandhass28.customer:id/accelerate = 0x7f0a0011
com.mohandhass28.customer:attr/onPositiveCross = 0x7f040327
com.mohandhass28.customer:id/TOP_START = 0x7f0a0010
com.mohandhass28.customer:style/ExoStyledControls.Button.Bottom.Shuffle = 0x7f130134
com.mohandhass28.customer:id/TOP_END = 0x7f0a000f
com.mohandhass28.customer:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1300da
com.mohandhass28.customer:id/SHOW_PROGRESS = 0x7f0a000d
com.mohandhass28.customer:id/row_index_key = 0x7f0a01e9
com.mohandhass28.customer:color/m3_dynamic_hint_foreground = 0x7f06009e
com.mohandhass28.customer:id/SHIFT = 0x7f0a000a
com.mohandhass28.customer:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1301c6
com.mohandhass28.customer:layout/abc_expanded_menu_layout = 0x7f0d000d
com.mohandhass28.customer:id/NO_DEBUG = 0x7f0a0009
com.mohandhass28.customer:style/ShapeAppearanceOverlay.TopRightDifferentCornerSize = 0x7f1301a8
com.mohandhass28.customer:style/Base.Widget.AppCompat.ListView = 0x7f1300dd
com.mohandhass28.customer:attr/actionOverflowMenuStyle = 0x7f040020
com.mohandhass28.customer:id/META = 0x7f0a0008
com.mohandhass28.customer:attr/overlapAnchor = 0x7f04032a
com.mohandhass28.customer:color/m3_dynamic_primary_text_disable_only = 0x7f06009f
com.mohandhass28.customer:id/ImageView_image = 0x7f0a0007
com.mohandhass28.customer:color/material_grey_50 = 0x7f060205
com.mohandhass28.customer:id/FUNCTION = 0x7f0a0006
com.mohandhass28.customer:style/Widget.Material3.Toolbar = 0x7f1303f3
com.mohandhass28.customer:id/BOTTOM_START = 0x7f0a0002
com.mohandhass28.customer:id/BOTTOM_END = 0x7f0a0001
com.mohandhass28.customer:attr/indeterminateAnimationType = 0x7f040226
com.mohandhass28.customer:id/ALT = 0x7f0a0000
com.mohandhass28.customer:attr/colorSecondary = 0x7f040101
com.mohandhass28.customer:drawable/tooltip_frame_light = 0x7f08016d
com.mohandhass28.customer:style/Widget.MaterialComponents.TimePicker.Button = 0x7f130466
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f130438
com.mohandhass28.customer:drawable/test_custom_background = 0x7f08016a
com.mohandhass28.customer:color/m3_ref_palette_secondary70 = 0x7f06012b
com.mohandhass28.customer:drawable/splashscreen_logo = 0x7f080169
com.mohandhass28.customer:drawable/rzp_white_border_black_bg = 0x7f080168
com.mohandhass28.customer:dimen/mtrl_btn_padding_left = 0x7f0701ad
com.mohandhass28.customer:drawable/rzp_secured_by_bg = 0x7f080167
com.mohandhass28.customer:drawable/rzp_poweredby = 0x7f080166
com.mohandhass28.customer:drawable/rzp_name_logo = 0x7f080165
com.mohandhass28.customer:drawable/rzp_loader_circle = 0x7f080163
com.mohandhass28.customer:drawable/exo_styled_controls_shuffle_on = 0x7f0800fb
com.mohandhass28.customer:drawable/rzp_border = 0x7f080160
com.mohandhass28.customer:drawable/redbox_top_border_background = 0x7f08015c
com.mohandhass28.customer:color/design_fab_shadow_end_color = 0x7f060061
com.mohandhass28.customer:drawable/notification_oversize_large_icon_bg = 0x7f080155
com.mohandhass28.customer:drawable/notification_icon_background = 0x7f080154
com.mohandhass28.customer:drawable/notification_bg_normal = 0x7f080152
com.mohandhass28.customer:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f13039e
com.mohandhass28.customer:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f080096
com.mohandhass28.customer:drawable/notification_bg_low_normal = 0x7f080150
com.mohandhass28.customer:attr/layout_scrollInterpolator = 0x7f04029f
com.mohandhass28.customer:color/common_google_signin_btn_text_light_disabled = 0x7f060040
com.mohandhass28.customer:drawable/notification_bg = 0x7f08014e
com.mohandhass28.customer:color/m3_text_button_ripple_color_selector = 0x7f0601aa
com.mohandhass28.customer:drawable/notification_action_background = 0x7f08014d
com.mohandhass28.customer:style/Base.Widget.AppCompat.EditText = 0x7f1300d1
com.mohandhass28.customer:drawable/node_modules_reactnativeratings_dist_images_rocket = 0x7f080146
com.mohandhass28.customer:id/visible_removing_fragment_view_tag = 0x7f0a0278
com.mohandhass28.customer:drawable/node_modules_reactnativeratings_dist_images_bell = 0x7f080144
com.mohandhass28.customer:layout/notification_template_big_media = 0x7f0d0079
com.mohandhass28.customer:drawable/exo_ic_skip_next = 0x7f0800cc
com.mohandhass28.customer:drawable/node_modules_reactnativeratings_dist_images_airbnbstar = 0x7f080142
com.mohandhass28.customer:drawable/node_modules_reactnativeelementdropdown_src_assets_close = 0x7f080140
com.mohandhass28.customer:attr/paddingRightSystemWindowInsets = 0x7f040331
com.mohandhass28.customer:color/m3_ref_palette_neutral_variant30 = 0x7f06010d
com.mohandhass28.customer:id/android_pay_light_with_border = 0x7f0a005c
com.mohandhass28.customer:drawable/node_modules_exporouter_assets_sitemap = 0x7f08013e
com.mohandhass28.customer:id/accessibility_custom_action_4 = 0x7f0a0030
com.mohandhass28.customer:drawable/node_modules_exporouter_assets_pkg = 0x7f08013d
com.mohandhass28.customer:drawable/navigation_empty_icon = 0x7f080139
com.mohandhass28.customer:dimen/exo_media_button_width = 0x7f0700a0
com.mohandhass28.customer:drawable/mtrl_tabs_default_indicator = 0x7f080138
com.mohandhass28.customer:dimen/design_snackbar_action_text_color_alpha = 0x7f070084
com.mohandhass28.customer:drawable/mtrl_popupmenu_background = 0x7f080136
com.mohandhass28.customer:attr/tabBackground = 0x7f0403e5
com.mohandhass28.customer:drawable/mtrl_ic_cancel = 0x7f080133
com.mohandhass28.customer:drawable/mtrl_ic_arrow_drop_up = 0x7f080132
com.mohandhass28.customer:style/Base.Widget.AppCompat.SeekBar = 0x7f1300ea
com.mohandhass28.customer:drawable/mtrl_dropdown_arrow = 0x7f080130
com.mohandhass28.customer:drawable/mtrl_dialog_background = 0x7f08012f
com.mohandhass28.customer:attr/clockIcon = 0x7f0400cc
com.mohandhass28.customer:attr/mapType = 0x7f0402bb
com.mohandhass28.customer:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f08012a
com.mohandhass28.customer:attr/currentState = 0x7f04015e
com.mohandhass28.customer:drawable/material_ic_calendar_black_24dp = 0x7f080126
com.mohandhass28.customer:drawable/material_cursor_drawable = 0x7f080125
com.mohandhass28.customer:id/checkbox = 0x7f0a008a
com.mohandhass28.customer:id/outward = 0x7f0a01b8
com.mohandhass28.customer:attr/expandedTitleMarginStart = 0x7f0401b6
com.mohandhass28.customer:color/m3_ref_palette_dynamic_tertiary20 = 0x7f0600e5
com.mohandhass28.customer:dimen/abc_action_bar_content_inset_material = 0x7f070000
com.mohandhass28.customer:drawable/m3_tabs_line_indicator = 0x7f080122
com.mohandhass28.customer:drawable/m3_popupmenu_background_overlay = 0x7f08011e
com.mohandhass28.customer:drawable/icon_background = 0x7f08011c
com.mohandhass28.customer:drawable/ic_tick_mark = 0x7f08011b
com.mohandhass28.customer:styleable/FragmentContainerView = 0x7f14003c
com.mohandhass28.customer:id/androidx_compose_ui_view_composition_context = 0x7f0a005d
com.mohandhass28.customer:drawable/ic_mtrl_chip_close_circle = 0x7f080117
com.mohandhass28.customer:attr/thumbTint = 0x7f040441
com.mohandhass28.customer:drawable/ic_mtrl_chip_checked_circle = 0x7f080116
com.mohandhass28.customer:drawable/ic_keyboard_black_24dp = 0x7f08010f
com.mohandhass28.customer:drawable/ic_clock_black_24dp = 0x7f08010b
com.mohandhass28.customer:color/m3_card_foreground_color = 0x7f060087
com.mohandhass28.customer:drawable/ic_call_decline_low = 0x7f08010a
com.mohandhass28.customer:attr/show_vr_button = 0x7f0403aa
com.mohandhass28.customer:drawable/ic_call_answer_video_low = 0x7f080108
com.mohandhass28.customer:styleable/ActionMenuView = 0x7f140003
com.mohandhass28.customer:drawable/ic_call_answer = 0x7f080105
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f1302ef
com.mohandhass28.customer:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f040433
com.mohandhass28.customer:drawable/fingerprint_dialog_error = 0x7f080100
com.mohandhass28.customer:drawable/exo_styled_controls_vr = 0x7f0800ff
com.mohandhass28.customer:id/exo_artwork = 0x7f0a00d9
com.mohandhass28.customer:attr/expandedHintEnabled = 0x7f0401b1
com.mohandhass28.customer:drawable/exo_styled_controls_subtitle_on = 0x7f0800fe
com.mohandhass28.customer:drawable/exo_styled_controls_subtitle_off = 0x7f0800fd
com.mohandhass28.customer:drawable/exo_styled_controls_rewind = 0x7f0800f8
com.mohandhass28.customer:string/item_view_role_description = 0x7f1200c5
com.mohandhass28.customer:drawable/exo_styled_controls_repeat_one = 0x7f0800f7
com.mohandhass28.customer:drawable/exo_styled_controls_play = 0x7f0800f3
com.mohandhass28.customer:id/accessibility_state_expanded = 0x7f0a003b
com.mohandhass28.customer:attr/constraintSetEnd = 0x7f04010c
com.mohandhass28.customer:drawable/exo_styled_controls_overflow_hide = 0x7f0800f0
com.mohandhass28.customer:style/amu_Bubble.TextAppearance.Light = 0x7f130476
com.mohandhass28.customer:color/m3_ref_palette_neutral_variant95 = 0x7f060114
com.mohandhass28.customer:drawable/exo_styled_controls_fullscreen_enter = 0x7f0800ed
com.mohandhass28.customer:attr/ensureMinTouchTargetSize = 0x7f0401a6
com.mohandhass28.customer:drawable/exo_styled_controls_check = 0x7f0800eb
com.mohandhass28.customer:drawable/exo_notification_stop = 0x7f0800e8
com.mohandhass28.customer:integer/bottom_sheet_slide_duration = 0x7f0b0003
com.mohandhass28.customer:drawable/exo_notification_previous = 0x7f0800e5
com.mohandhass28.customer:dimen/mtrl_btn_icon_btn_padding_left = 0x7f0701a7
com.mohandhass28.customer:drawable/exo_notification_pause = 0x7f0800e3
com.mohandhass28.customer:id/exo_next = 0x7f0a00ef
com.mohandhass28.customer:drawable/exo_icon_vr = 0x7f0800e0
com.mohandhass28.customer:color/colorPrimary = 0x7f060037
com.mohandhass28.customer:drawable/exo_icon_previous = 0x7f0800d8
com.mohandhass28.customer:style/Theme.ReactNative.AppCompat.Light.NoActionBar.FullScreen = 0x7f1302a6
com.mohandhass28.customer:dimen/mtrl_extended_fab_top_padding = 0x7f0701fa
com.mohandhass28.customer:drawable/node_modules_reactnavigation_elements_lib_commonjs_assets_backicon = 0x7f080148
com.mohandhass28.customer:string/default_error_message = 0x7f120061
com.mohandhass28.customer:drawable/exo_icon_play = 0x7f0800d7
com.mohandhass28.customer:drawable/exo_icon_pause = 0x7f0800d6
com.mohandhass28.customer:id/fill_vertical = 0x7f0a0112
com.mohandhass28.customer:color/common_google_signin_btn_tint = 0x7f060043
com.mohandhass28.customer:drawable/exo_icon_fullscreen_enter = 0x7f0800d3
com.mohandhass28.customer:color/m3_ref_palette_tertiary60 = 0x7f060137
com.mohandhass28.customer:drawable/exo_icon_fastforward = 0x7f0800d2
com.mohandhass28.customer:attr/buffered_color = 0x7f040080
com.mohandhass28.customer:drawable/exo_ic_rewind = 0x7f0800ca
com.mohandhass28.customer:drawable/exo_ic_play_circle_filled = 0x7f0800c9
com.mohandhass28.customer:id/toggle = 0x7f0a0259
com.mohandhass28.customer:drawable/googleg_disabled_color_18 = 0x7f080102
com.mohandhass28.customer:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f130181
com.mohandhass28.customer:attr/textAppearanceTitleLarge = 0x7f040428
com.mohandhass28.customer:id/exo_pause = 0x7f0a00f3
com.mohandhass28.customer:drawable/exo_ic_fullscreen_exit = 0x7f0800c7
com.mohandhass28.customer:drawable/exo_ic_chevron_left = 0x7f0800c2
com.mohandhass28.customer:id/edit_query = 0x7f0a00cc
com.mohandhass28.customer:id/accessibility_actions = 0x7f0a0013
com.mohandhass28.customer:drawable/exo_edit_mode_logo = 0x7f0800bf
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f060168
com.mohandhass28.customer:color/foreground_material_light = 0x7f060079
com.mohandhass28.customer:drawable/exo_controls_shuffle_on = 0x7f0800bd
com.mohandhass28.customer:drawable/exo_controls_shuffle_off = 0x7f0800bc
com.mohandhass28.customer:drawable/exo_controls_rewind = 0x7f0800bb
com.mohandhass28.customer:color/m3_ref_palette_tertiary10 = 0x7f060131
com.mohandhass28.customer:drawable/exo_controls_repeat_one = 0x7f0800ba
com.mohandhass28.customer:style/Widget.Autofill.InlineSuggestionStartIconStyle = 0x7f13035f
com.mohandhass28.customer:drawable/exo_controls_repeat_all = 0x7f0800b8
com.mohandhass28.customer:layout/material_clock_display_divider = 0x7f0d004b
com.mohandhass28.customer:drawable/exo_controls_play = 0x7f0800b6
com.mohandhass28.customer:drawable/exo_controls_pause = 0x7f0800b5
com.mohandhass28.customer:attr/triggerSlack = 0x7f040479
com.mohandhass28.customer:drawable/exo_controls_next = 0x7f0800b4
com.mohandhass28.customer:style/Base.Theme.MaterialComponents = 0x7f13005e
com.mohandhass28.customer:drawable/exo_controls_fullscreen_enter = 0x7f0800b2
com.mohandhass28.customer:color/material_slider_active_tick_marks_color = 0x7f060218
com.mohandhass28.customer:drawable/design_ic_visibility_off = 0x7f0800ae
com.mohandhass28.customer:drawable/compat_splash_screen = 0x7f0800aa
com.mohandhass28.customer:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1302d8
com.mohandhass28.customer:string/alert_description = 0x7f12001b
com.mohandhass28.customer:attr/splashScreenIconSize = 0x7f0403bb
com.mohandhass28.customer:drawable/common_google_signin_btn_text_light_normal_background = 0x7f0800a9
com.mohandhass28.customer:string/state_off = 0x7f12012b
com.mohandhass28.customer:drawable/common_google_signin_btn_text_disabled = 0x7f0800a5
com.mohandhass28.customer:attr/paddingBottomNoButtons = 0x7f04032d
com.mohandhass28.customer:drawable/common_google_signin_btn_icon_light_normal = 0x7f08009f
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f130026
com.mohandhass28.customer:string/mtrl_picker_text_input_month_abbr = 0x7f12010b
com.mohandhass28.customer:drawable/common_google_signin_btn_icon_disabled = 0x7f08009c
com.mohandhass28.customer:id/jumpToEnd = 0x7f0a0159
com.mohandhass28.customer:drawable/ic_call_answer_low = 0x7f080106
com.mohandhass28.customer:interpolator/fast_out_slow_in = 0x7f0c0006
com.mohandhass28.customer:drawable/common_google_signin_btn_icon_dark_normal = 0x7f08009a
com.mohandhass28.customer:color/mtrl_scrim_color = 0x7f06024a
com.mohandhass28.customer:attr/expandActivityOverflowButtonDrawable = 0x7f0401af
com.mohandhass28.customer:drawable/common_full_open_on_phone = 0x7f080097
com.mohandhass28.customer:style/Base.Animation.AppCompat.Dialog = 0x7f13000d
com.mohandhass28.customer:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f080094
com.mohandhass28.customer:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f080092
com.mohandhass28.customer:drawable/btn_checkbox_unchecked_mtrl = 0x7f080091
com.mohandhass28.customer:array/hide_fingerprint_instantly_prefixes = 0x7f030005
com.mohandhass28.customer:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f080090
com.mohandhass28.customer:drawable/autofill_inline_suggestion_chip_background = 0x7f08008c
com.mohandhass28.customer:attr/cornerSizeTopLeft = 0x7f04012e
com.mohandhass28.customer:drawable/assets_whatsappimage20250514at182654_d73a5ff5 = 0x7f08008b
com.mohandhass28.customer:drawable/assets_whatsappimage20250514at182654_48c9fac1 = 0x7f08008a
com.mohandhass28.customer:drawable/assets_streetfoodsassert_images_4 = 0x7f080087
com.mohandhass28.customer:integer/m3_sys_motion_duration_100 = 0x7f0b0012
com.mohandhass28.customer:drawable/assets_streetfoodsassert_images_3 = 0x7f080086
com.mohandhass28.customer:id/tag_unhandled_key_event_manager = 0x7f0a023b
com.mohandhass28.customer:drawable/exo_styled_controls_previous = 0x7f0800f4
com.mohandhass28.customer:drawable/assets_resimages_rectangle592760 = 0x7f080083
com.mohandhass28.customer:style/Theme.Material3.Light = 0x7f13026d
com.mohandhass28.customer:drawable/assets_resimages_images_ellipse6 = 0x7f080082
com.mohandhass28.customer:drawable/node_modules_reactnavigation_elements_lib_commonjs_assets_searchicon = 0x7f08014c
com.mohandhass28.customer:drawable/assets_resimages_images_7 = 0x7f08007f
com.mohandhass28.customer:dimen/m3_btn_translation_z_hovered = 0x7f0700fe
com.mohandhass28.customer:drawable/assets_resimages_images_6 = 0x7f08007e
com.mohandhass28.customer:dimen/m3_btn_icon_btn_padding_right = 0x7f0700ed
com.mohandhass28.customer:drawable/assets_resimages_images_5 = 0x7f08007d
com.mohandhass28.customer:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f130375
com.mohandhass28.customer:drawable/assets_resimages_images_2 = 0x7f08007a
com.mohandhass28.customer:color/m3_default_color_secondary_text = 0x7f060095
com.mohandhass28.customer:dimen/abc_alert_dialog_button_bar_height = 0x7f070010
com.mohandhass28.customer:drawable/ic_mtrl_checked_circle = 0x7f080114
com.mohandhass28.customer:dimen/m3_ripple_focused_alpha = 0x7f07012e
com.mohandhass28.customer:drawable/assets_resimages_3 = 0x7f080075
com.mohandhass28.customer:attr/contentPaddingEnd = 0x7f04011a
com.mohandhass28.customer:drawable/assets_profileimgcopy_bottomsheet_whatsappincpng = 0x7f080072
com.mohandhass28.customer:drawable/assets_profileimgcopy_bottomsheet_qe = 0x7f08006e
com.mohandhass28.customer:drawable/ic_resume = 0x7f080118
com.mohandhass28.customer:drawable/assets_profileimgcopy_bootomsheet = 0x7f080069
com.mohandhass28.customer:drawable/assets_profileimg_user = 0x7f080067
com.mohandhass28.customer:attr/drawableLeftCompat = 0x7f040187
com.mohandhass28.customer:drawable/assets_profileasser_sectionicon_refer = 0x7f080066
com.mohandhass28.customer:drawable/assets_orderlanesellericon = 0x7f080064
com.mohandhass28.customer:id/mtrl_child_content_container = 0x7f0a0197
com.mohandhass28.customer:drawable/assets_images_productimage_tshirt = 0x7f080061
com.mohandhass28.customer:drawable/assets_images_cardimages_rectangle592762 = 0x7f080060
com.mohandhass28.customer:drawable/assets_icon_coffee = 0x7f08005e
com.mohandhass28.customer:drawable/assets_dailyessentialsassert_hotal_4 = 0x7f08005b
com.mohandhass28.customer:drawable/abc_list_divider_mtrl_alpha = 0x7f08002b
com.mohandhass28.customer:drawable/abc_textfield_activated_mtrl_alpha = 0x7f08004f
com.mohandhass28.customer:drawable/abc_text_select_handle_right_mtrl = 0x7f08004e
com.mohandhass28.customer:dimen/m3_navigation_rail_item_min_height = 0x7f07012a
com.mohandhass28.customer:attr/shapeAppearanceSmallComponent = 0x7f040398
com.mohandhass28.customer:drawable/abc_text_select_handle_left_mtrl = 0x7f08004c
com.mohandhass28.customer:layout/mtrl_calendar_month_navigation = 0x7f0d0064
com.mohandhass28.customer:drawable/assets_img_carouselimg = 0x7f080062
com.mohandhass28.customer:dimen/m3_badge_radius = 0x7f0700d6
com.mohandhass28.customer:drawable/abc_text_cursor_material = 0x7f08004b
com.mohandhass28.customer:drawable/assets_icon_bon = 0x7f08005c
com.mohandhass28.customer:drawable/abc_spinner_textfield_background_material = 0x7f080044
com.mohandhass28.customer:drawable/abc_spinner_mtrl_am_alpha = 0x7f080043
com.mohandhass28.customer:drawable/mtrl_ic_arrow_drop_down = 0x7f080131
com.mohandhass28.customer:attr/cropMinCropWindowWidth = 0x7f040150
com.mohandhass28.customer:drawable/exo_ic_audiotrack = 0x7f0800c0
com.mohandhass28.customer:color/mtrl_card_view_foreground = 0x7f06022d
com.mohandhass28.customer:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f08003e
com.mohandhass28.customer:drawable/common_google_signin_btn_text_light_normal = 0x7f0800a8
com.mohandhass28.customer:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f08003b
com.mohandhass28.customer:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f1303aa
com.mohandhass28.customer:id/accessibility_custom_action_12 = 0x7f0a001a
com.mohandhass28.customer:layout/material_clockface_textview = 0x7f0d004e
com.mohandhass28.customer:color/material_dynamic_neutral99 = 0x7f0601ce
com.mohandhass28.customer:drawable/abc_ratingbar_small_material = 0x7f08003a
com.mohandhass28.customer:drawable/abc_ratingbar_indicator_material = 0x7f080038
com.mohandhass28.customer:drawable/abc_list_selector_holo_light = 0x7f080035
com.mohandhass28.customer:id/matrix = 0x7f0a017e
com.mohandhass28.customer:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f07014f
com.mohandhass28.customer:drawable/assets_icon_meat = 0x7f08005f
com.mohandhass28.customer:drawable/abc_list_selector_holo_dark = 0x7f080034
com.mohandhass28.customer:drawable/abc_list_selector_background_transition_holo_dark = 0x7f080030
com.mohandhass28.customer:dimen/m3_sys_elevation_level4 = 0x7f070139
com.mohandhass28.customer:drawable/abc_list_pressed_holo_dark = 0x7f08002e
com.mohandhass28.customer:drawable/abc_list_longpressed_holo = 0x7f08002d
com.mohandhass28.customer:style/Widget.AppCompat.Button.Borderless = 0x7f13031f
com.mohandhass28.customer:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f130043
com.mohandhass28.customer:drawable/common_google_signin_btn_text_light = 0x7f0800a6
com.mohandhass28.customer:drawable/abc_list_focused_holo = 0x7f08002c
com.mohandhass28.customer:drawable/abc_list_divider_material = 0x7f08002a
com.mohandhass28.customer:drawable/abc_item_background_holo_light = 0x7f080029
com.mohandhass28.customer:style/ShapeAppearance.Material3.SmallComponent = 0x7f13018c
com.mohandhass28.customer:drawable/abc_item_background_holo_dark = 0x7f080028
com.mohandhass28.customer:color/switch_thumb_material_light = 0x7f06026c
com.mohandhass28.customer:id/accessibility_custom_action_22 = 0x7f0a0025
com.mohandhass28.customer:attr/framePosition = 0x7f0401fe
com.mohandhass28.customer:attr/actionLayout = 0x7f04000d
com.mohandhass28.customer:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f080024
com.mohandhass28.customer:style/Widget.Material3.DrawerLayout = 0x7f1303a5
com.mohandhass28.customer:drawable/abc_ic_menu_overflow_material = 0x7f080022
com.mohandhass28.customer:string/exo_controls_seek_bar_description = 0x7f12007b
com.mohandhass28.customer:id/disablePostScroll = 0x7f0a00bb
com.mohandhass28.customer:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f080021
com.mohandhass28.customer:drawable/abc_ic_go_search_api_material = 0x7f08001f
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Headline = 0x7f13001f
com.mohandhass28.customer:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f08001e
com.mohandhass28.customer:drawable/abc_edit_text_material = 0x7f08001a
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f13030a
com.mohandhass28.customer:color/m3_appbar_overlay_color = 0x7f06007d
com.mohandhass28.customer:color/background_material_dark = 0x7f06001f
com.mohandhass28.customer:drawable/abc_dialog_material_background = 0x7f080019
com.mohandhass28.customer:string/abc_search_hint = 0x7f120012
com.mohandhass28.customer:drawable/abc_cab_background_top_material = 0x7f080016
com.mohandhass28.customer:attr/thickness = 0x7f04043a
com.mohandhass28.customer:anim/mtrl_card_lowers_interpolator = 0x7f010025
com.mohandhass28.customer:attr/flow_horizontalGap = 0x7f0401e1
com.mohandhass28.customer:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f080014
com.mohandhass28.customer:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f080013
com.mohandhass28.customer:color/common_google_signin_btn_text_dark_default = 0x7f06003a
com.mohandhass28.customer:drawable/abc_btn_default_mtrl_shape = 0x7f08000e
com.mohandhass28.customer:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1301d5
com.mohandhass28.customer:attr/elevationOverlayColor = 0x7f04019a
com.mohandhass28.customer:drawable/exo_icon_fullscreen_exit = 0x7f0800d4
com.mohandhass28.customer:anim/abc_slide_out_bottom = 0x7f010008
com.mohandhass28.customer:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f08000c
com.mohandhass28.customer:drawable/abc_btn_check_material = 0x7f080009
com.mohandhass28.customer:style/Widget.AppCompat.ActionMode = 0x7f13031b
com.mohandhass28.customer:attr/statusBarScrim = 0x7f0403cd
com.mohandhass28.customer:attr/chipStrokeColor = 0x7f0400c2
com.mohandhass28.customer:drawable/abc_action_bar_item_background_material = 0x7f080007
com.mohandhass28.customer:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f080006
com.mohandhass28.customer:drawable/$avd_show_password__2 = 0x7f080005
com.mohandhass28.customer:drawable/$avd_show_password__0 = 0x7f080003
com.mohandhass28.customer:id/material_clock_period_am_button = 0x7f0a016e
com.mohandhass28.customer:color/material_cursor_color = 0x7f0601be
com.mohandhass28.customer:drawable/$avd_hide_password__2 = 0x7f080002
com.mohandhass28.customer:attr/motionStagger = 0x7f040313
com.mohandhass28.customer:attr/layout_scrollFlags = 0x7f04029e
com.mohandhass28.customer:id/accessibility_custom_action_25 = 0x7f0a0028
com.mohandhass28.customer:anim/rns_default_enter_in = 0x7f010026
com.mohandhass28.customer:dimen/tooltip_precise_anchor_threshold = 0x7f070276
com.mohandhass28.customer:drawable/node_modules_reactnavigation_elements_lib_commonjs_assets_closeicon = 0x7f08014b
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f1303c3
com.mohandhass28.customer:layout/design_navigation_menu = 0x7f0d0030
com.mohandhass28.customer:dimen/tooltip_horizontal_padding = 0x7f070273
com.mohandhass28.customer:style/Base.V26.Theme.AppCompat = 0x7f1300ab
com.mohandhass28.customer:id/accessibility_custom_action_11 = 0x7f0a0019
com.mohandhass28.customer:dimen/test_navigation_bar_item_min_width = 0x7f07026e
com.mohandhass28.customer:string/mtrl_picker_range_header_title = 0x7f120104
com.mohandhass28.customer:dimen/test_navigation_bar_item_max_width = 0x7f07026d
com.mohandhass28.customer:styleable/KeyFramesAcceleration = 0x7f140045
com.mohandhass28.customer:dimen/test_navigation_bar_icon_size = 0x7f07026c
com.mohandhass28.customer:id/exo_shuffle = 0x7f0a0100
com.mohandhass28.customer:dimen/m3_navigation_item_horizontal_padding = 0x7f07011d
com.mohandhass28.customer:dimen/test_navigation_bar_height = 0x7f07026b
com.mohandhass28.customer:dimen/test_navigation_bar_active_text_size = 0x7f070269
com.mohandhass28.customer:dimen/design_bottom_navigation_height = 0x7f070068
com.mohandhass28.customer:dimen/test_navigation_bar_active_item_min_width = 0x7f070268
com.mohandhass28.customer:dimen/test_dimen = 0x7f070265
com.mohandhass28.customer:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f13009b
com.mohandhass28.customer:attr/subtitleCentered = 0x7f0403d7
com.mohandhass28.customer:color/design_icon_tint = 0x7f060068
com.mohandhass28.customer:id/exitUntilCollapsed = 0x7f0a00d7
com.mohandhass28.customer:dimen/splashscreen_icon_mask_size_with_background = 0x7f07025f
com.mohandhass28.customer:drawable/rzp_border_bottom = 0x7f080161
com.mohandhass28.customer:color/material_grey_600 = 0x7f060206
com.mohandhass28.customer:dimen/splashscreen_icon_mask_size_no_background = 0x7f07025e
com.mohandhass28.customer:string/search_menu_title = 0x7f120123
com.mohandhass28.customer:dimen/notification_top_pad = 0x7f07025c
com.mohandhass28.customer:attr/itemPadding = 0x7f04023d
com.mohandhass28.customer:dimen/notification_right_side_padding_top = 0x7f070258
com.mohandhass28.customer:drawable/abc_text_select_handle_middle_mtrl = 0x7f08004d
com.mohandhass28.customer:dimen/notification_right_icon_size = 0x7f070257
com.mohandhass28.customer:dimen/notification_main_column_padding_top = 0x7f070255
com.mohandhass28.customer:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f13016b
com.mohandhass28.customer:id/exo_ffwd_with_amount = 0x7f0a00e9
com.mohandhass28.customer:dimen/notification_large_icon_height = 0x7f070253
com.mohandhass28.customer:attr/maskedWalletDetailsTextAppearance = 0x7f0402c6
com.mohandhass28.customer:dimen/notification_big_circle_margin = 0x7f070251
com.mohandhass28.customer:dimen/mtrl_tooltip_cornerSize = 0x7f07024a
com.mohandhass28.customer:dimen/mtrl_toolbar_default_height = 0x7f070248
com.mohandhass28.customer:dimen/mtrl_textinput_start_icon_margin_end = 0x7f070247
com.mohandhass28.customer:dimen/mtrl_textinput_end_icon_margin_start = 0x7f070245
com.mohandhass28.customer:dimen/mtrl_textinput_counter_margin_start = 0x7f070244
com.mohandhass28.customer:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f070243
com.mohandhass28.customer:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f13008f
com.mohandhass28.customer:color/mtrl_filled_background_color = 0x7f06023a
com.mohandhass28.customer:dimen/mtrl_textinput_box_corner_radius_small = 0x7f070240
com.mohandhass28.customer:string/exo_controls_custom_playback_speed = 0x7f12006b
com.mohandhass28.customer:dimen/mtrl_switch_thumb_elevation = 0x7f07023e
com.mohandhass28.customer:array/crypto_fingerprint_fallback_vendors = 0x7f030002
com.mohandhass28.customer:dimen/mtrl_snackbar_padding_horizontal = 0x7f07023d
com.mohandhass28.customer:attr/reverseLayout = 0x7f04036f
com.mohandhass28.customer:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f07023c
com.mohandhass28.customer:layout/notification_template_media_custom = 0x7f0d0081
com.mohandhass28.customer:attr/colorOnSecondary = 0x7f0400f2
com.mohandhass28.customer:dimen/mtrl_navigation_rail_icon_size = 0x7f070219
com.mohandhass28.customer:id/android_pay = 0x7f0a0059
com.mohandhass28.customer:attr/surface_type = 0x7f0403df
com.mohandhass28.customer:color/material_dynamic_primary100 = 0x7f0601de
com.mohandhass28.customer:dimen/mtrl_snackbar_margin = 0x7f07023b
com.mohandhass28.customer:style/Widget.Material3.CheckedTextView = 0x7f13038f
com.mohandhass28.customer:style/Base.V14.Theme.Material3.Dark = 0x7f130084
com.mohandhass28.customer:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f07023a
com.mohandhass28.customer:drawable/avd_hide_password = 0x7f08008d
com.mohandhass28.customer:dimen/mtrl_snackbar_background_corner_radius = 0x7f070239
com.mohandhass28.customer:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f070238
com.mohandhass28.customer:layout/mtrl_calendar_year = 0x7f0d0067
com.mohandhass28.customer:dimen/mtrl_slider_track_top = 0x7f070236
com.mohandhass28.customer:xml/standalone_badge_offset = 0x7f15000e
com.mohandhass28.customer:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f08003c
com.mohandhass28.customer:dimen/mtrl_slider_track_height = 0x7f070234
com.mohandhass28.customer:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f1302cc
com.mohandhass28.customer:dimen/mtrl_slider_label_padding = 0x7f07022f
com.mohandhass28.customer:dimen/mtrl_progress_track_thickness = 0x7f07022a
com.mohandhass28.customer:style/Theme.AppCompat.Dialog.MinWidth = 0x7f130244
com.mohandhass28.customer:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0700cc
com.mohandhass28.customer:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f070229
com.mohandhass28.customer:dimen/mtrl_progress_circular_size_small = 0x7f070225
com.mohandhass28.customer:style/Widget.Material3.CompoundButton.Switch = 0x7f1303a4
com.mohandhass28.customer:dimen/mtrl_progress_circular_size_medium = 0x7f070224
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f13043e
com.mohandhass28.customer:dimen/mtrl_progress_circular_radius = 0x7f070221
com.mohandhass28.customer:dimen/mtrl_progress_circular_inset = 0x7f07021d
com.mohandhass28.customer:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f07021b
com.mohandhass28.customer:attr/state_lifted = 0x7f0403ca
com.mohandhass28.customer:dimen/mtrl_navigation_rail_margin = 0x7f07021a
com.mohandhass28.customer:drawable/common_google_signin_btn_icon_dark_focused = 0x7f080099
com.mohandhass28.customer:drawable/abc_ic_search_api_material = 0x7f080026
com.mohandhass28.customer:layout/mtrl_calendar_month_labeled = 0x7f0d0063
com.mohandhass28.customer:dimen/mtrl_navigation_rail_icon_margin = 0x7f070218
com.mohandhass28.customer:dimen/cardview_compat_inset_shadow = 0x7f070056
com.mohandhass28.customer:id/enterAlways = 0x7f0a00d5
com.mohandhass28.customer:attr/layout_constraintRight_toRightOf = 0x7f040283
com.mohandhass28.customer:dimen/tooltip_y_offset_touch = 0x7f070279
com.mohandhass28.customer:id/navigation_bar_item_icon_view = 0x7f0a01a7
com.mohandhass28.customer:dimen/mtrl_navigation_rail_elevation = 0x7f070217
com.mohandhass28.customer:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f130173
com.mohandhass28.customer:dimen/mtrl_navigation_rail_compact_width = 0x7f070215
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f13006c
com.mohandhass28.customer:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f070197
com.mohandhass28.customer:dimen/mtrl_navigation_rail_active_text_size = 0x7f070214
com.mohandhass28.customer:id/performance = 0x7f0a01c5
com.mohandhass28.customer:attr/layout_constraintHeight_percent = 0x7f04027a
com.mohandhass28.customer:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f070213
com.mohandhass28.customer:attr/textInputFilledDenseStyle = 0x7f04042e
com.mohandhass28.customer:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f070212
com.mohandhass28.customer:dimen/design_navigation_icon_padding = 0x7f07007b
com.mohandhass28.customer:dimen/mtrl_navigation_item_icon_padding = 0x7f070210
com.mohandhass28.customer:dimen/mtrl_navigation_item_horizontal_padding = 0x7f07020f
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f130304
com.mohandhass28.customer:dimen/mtrl_navigation_elevation = 0x7f07020e
com.mohandhass28.customer:dimen/mtrl_navigation_bar_item_default_margin = 0x7f07020d
com.mohandhass28.customer:drawable/btn_radio_off_mtrl = 0x7f080093
com.mohandhass28.customer:integer/m3_sys_motion_duration_550 = 0x7f0b001d
com.mohandhass28.customer:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f07020c
com.mohandhass28.customer:color/material_dynamic_secondary95 = 0x7f0601f4
com.mohandhass28.customer:dimen/mtrl_low_ripple_pressed_alpha = 0x7f07020a
com.mohandhass28.customer:dimen/mtrl_high_ripple_pressed_alpha = 0x7f070205
com.mohandhass28.customer:drawable/common_google_signin_btn_icon_light_focused = 0x7f08009e
com.mohandhass28.customer:styleable/PropertySet = 0x7f140075
com.mohandhass28.customer:dimen/mtrl_high_ripple_hovered_alpha = 0x7f070204
com.mohandhass28.customer:style/ShapeAppearance.M3.Sys.Shape.Corner.Large.Top = 0x7f13017e
com.mohandhass28.customer:dimen/mtrl_high_ripple_default_alpha = 0x7f070202
com.mohandhass28.customer:dimen/mtrl_extended_fab_translation_z_base = 0x7f0701fb
com.mohandhass28.customer:attr/collapsedTitleGravity = 0x7f0400d9
com.mohandhass28.customer:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0701f9
com.mohandhass28.customer:attr/boxCornerRadiusBottomStart = 0x7f040078
com.mohandhass28.customer:dimen/mtrl_extended_fab_start_padding = 0x7f0701f8
com.mohandhass28.customer:attr/trackColorActive = 0x7f04046b
com.mohandhass28.customer:dimen/mtrl_extended_fab_min_height = 0x7f0701f6
com.mohandhass28.customer:dimen/mtrl_extended_fab_end_padding = 0x7f0701f2
com.mohandhass28.customer:style/Theme.Design.Light.NoActionBar = 0x7f130257
com.mohandhass28.customer:dimen/mtrl_extended_fab_elevation = 0x7f0701f1
com.mohandhass28.customer:drawable/exo_notification_play = 0x7f0800e4
com.mohandhass28.customer:layout/notification_action = 0x7f0d0075
com.mohandhass28.customer:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0701ef
com.mohandhass28.customer:id/src_atop = 0x7f0a021f
com.mohandhass28.customer:dimen/mtrl_extended_fab_bottom_padding = 0x7f0701ed
com.mohandhass28.customer:attr/titleCentered = 0x7f04044e
com.mohandhass28.customer:drawable/abc_btn_radio_material_anim = 0x7f080010
com.mohandhass28.customer:id/material_minute_text_input = 0x7f0a0174
com.mohandhass28.customer:attr/cornerFamilyBottomRight = 0x7f040126
com.mohandhass28.customer:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0701ec
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1301df
com.mohandhass28.customer:drawable/assets_profileimgcopy_bottomsheet_screenshort = 0x7f08006f
com.mohandhass28.customer:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0701ea
com.mohandhass28.customer:dimen/mtrl_chip_text_size = 0x7f0701e9
com.mohandhass28.customer:xml/image_share_filepaths = 0x7f150004
com.mohandhass28.customer:dimen/mtrl_card_dragged_z = 0x7f0701e5
com.mohandhass28.customer:dimen/mtrl_tooltip_padding = 0x7f07024d
com.mohandhass28.customer:attr/windowSplashScreenBackground = 0x7f0404a2
com.mohandhass28.customer:dimen/mtrl_card_corner_radius = 0x7f0701e4
com.mohandhass28.customer:dimen/mtrl_card_checked_icon_size = 0x7f0701e3
com.mohandhass28.customer:dimen/mtrl_calendar_year_height = 0x7f0701de
com.mohandhass28.customer:attr/logo = 0x7f0402b6
com.mohandhass28.customer:dimen/mtrl_calendar_year_corner = 0x7f0701dd
com.mohandhass28.customer:dimen/mtrl_calendar_title_baseline_to_top = 0x7f0701db
com.mohandhass28.customer:dimen/mtrl_calendar_text_input_padding_top = 0x7f0701da
com.mohandhass28.customer:attr/applyMotionScene = 0x7f040038
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f06018b
com.mohandhass28.customer:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f0701d8
com.mohandhass28.customer:dimen/abc_text_size_title_material_toolbar = 0x7f070050
com.mohandhass28.customer:dimen/mtrl_calendar_navigation_top_padding = 0x7f0701d4
com.mohandhass28.customer:styleable/SimpleDraweeView = 0x7f140080
com.mohandhass28.customer:color/m3_dark_highlighted_text = 0x7f060091
com.mohandhass28.customer:color/m3_switch_thumb_tint = 0x7f060144
com.mohandhass28.customer:dimen/mtrl_calendar_navigation_height = 0x7f0701d3
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f130301
com.mohandhass28.customer:attr/tint = 0x7f04044b
com.mohandhass28.customer:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f070129
com.mohandhass28.customer:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f0701cd
com.mohandhass28.customer:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f0701c6
com.mohandhass28.customer:drawable/abc_ic_clear_material = 0x7f08001d
com.mohandhass28.customer:attr/windowFixedWidthMajor = 0x7f04049b
com.mohandhass28.customer:color/material_divider_color = 0x7f0601c1
com.mohandhass28.customer:dimen/mtrl_calendar_header_content_padding = 0x7f0701c5
com.mohandhass28.customer:dimen/mtrl_calendar_day_width = 0x7f0701c2
com.mohandhass28.customer:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f13007b
com.mohandhass28.customer:dimen/mtrl_calendar_day_vertical_padding = 0x7f0701c1
com.mohandhass28.customer:attr/cropMultiTouchEnabled = 0x7f040151
com.mohandhass28.customer:dimen/mtrl_calendar_day_today_stroke = 0x7f0701c0
com.mohandhass28.customer:dimen/mtrl_calendar_day_horizontal_padding = 0x7f0701bf
com.mohandhass28.customer:dimen/mtrl_calendar_day_height = 0x7f0701be
com.mohandhass28.customer:color/m3_ref_palette_error99 = 0x7f0600fb
com.mohandhass28.customer:drawable/exo_notification_small_icon = 0x7f0800e7
com.mohandhass28.customer:color/material_dynamic_tertiary30 = 0x7f0601fa
com.mohandhass28.customer:dimen/mtrl_calendar_content_padding = 0x7f0701bc
com.mohandhass28.customer:attr/isMaterialTheme = 0x7f040232
com.mohandhass28.customer:dimen/mtrl_calendar_bottom_padding = 0x7f0701bb
com.mohandhass28.customer:dimen/mtrl_calendar_action_padding = 0x7f0701ba
com.mohandhass28.customer:attr/actionBarTheme = 0x7f040009
com.mohandhass28.customer:attr/layout_constraintHorizontal_chainStyle = 0x7f04027c
com.mohandhass28.customer:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f0701b8
com.mohandhass28.customer:attr/logoAdjustViewBounds = 0x7f0402b7
com.mohandhass28.customer:dimen/mtrl_btn_text_size = 0x7f0701b6
com.mohandhass28.customer:id/date_picker_actions = 0x7f0a00ac
com.mohandhass28.customer:dimen/mtrl_card_checked_icon_margin = 0x7f0701e2
com.mohandhass28.customer:dimen/mtrl_fab_elevation = 0x7f0701fe
com.mohandhass28.customer:dimen/mtrl_btn_text_btn_padding_left = 0x7f0701b4
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f1302a2
com.mohandhass28.customer:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f130208
com.mohandhass28.customer:id/mtrl_calendar_year_selector_frame = 0x7f0a0195
com.mohandhass28.customer:id/jumpToStart = 0x7f0a015a
com.mohandhass28.customer:color/m3_sys_color_light_on_background = 0x7f060192
com.mohandhass28.customer:dimen/mtrl_btn_padding_top = 0x7f0701af
com.mohandhass28.customer:dimen/mtrl_btn_padding_bottom = 0x7f0701ac
com.mohandhass28.customer:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f130012
com.mohandhass28.customer:dimen/mtrl_btn_inset = 0x7f0701a9
com.mohandhass28.customer:dimen/mtrl_btn_letter_spacing = 0x7f0701aa
com.mohandhass28.customer:attr/dragScale = 0x7f040182
com.mohandhass28.customer:color/wallet_bright_foreground_holo_dark = 0x7f060277
com.mohandhass28.customer:dimen/mtrl_btn_elevation = 0x7f0701a4
com.mohandhass28.customer:dimen/mtrl_btn_disabled_elevation = 0x7f0701a2
com.mohandhass28.customer:layout/amu_webview = 0x7f0d001e
com.mohandhass28.customer:attr/behavior_saveFlags = 0x7f04006b
com.mohandhass28.customer:id/cos = 0x7f0a00a3
com.mohandhass28.customer:dimen/mtrl_bottomappbar_height = 0x7f07019f
com.mohandhass28.customer:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f07019a
com.mohandhass28.customer:dimen/mtrl_badge_text_size = 0x7f070196
com.mohandhass28.customer:dimen/mtrl_badge_radius = 0x7f070194
com.mohandhass28.customer:id/action_bar_spinner = 0x7f0a0042
com.mohandhass28.customer:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f070193
com.mohandhass28.customer:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1300e4
com.mohandhass28.customer:dimen/mtrl_badge_horizontal_edge_offset = 0x7f070192
com.mohandhass28.customer:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f070191
com.mohandhass28.customer:dimen/mtrl_alert_dialog_background_inset_top = 0x7f070190
com.mohandhass28.customer:dimen/mtrl_tooltip_minWidth = 0x7f07024c
com.mohandhass28.customer:dimen/mtrl_alert_dialog_background_inset_end = 0x7f07018e
com.mohandhass28.customer:attr/layout_constraintTag = 0x7f040286
com.mohandhass28.customer:attr/subtitleTextAppearance = 0x7f0403d8
com.mohandhass28.customer:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f07018d
com.mohandhass28.customer:style/TestThemeWithLineHeightDisabled = 0x7f1301b9
com.mohandhass28.customer:attr/cardUseCompatPadding = 0x7f0400a0
com.mohandhass28.customer:attr/hide_on_touch = 0x7f04020f
com.mohandhass28.customer:dimen/material_time_picker_minimum_screen_width = 0x7f07018b
com.mohandhass28.customer:id/endToStart = 0x7f0a00d2
com.mohandhass28.customer:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f130320
com.mohandhass28.customer:dimen/material_text_view_test_line_height_override = 0x7f070186
com.mohandhass28.customer:styleable/WalletFragmentOptions = 0x7f14009d
com.mohandhass28.customer:style/TextAppearance.Design.Snackbar.Message = 0x7f1301fc
com.mohandhass28.customer:color/dim_foreground_material_light = 0x7f06006d
com.mohandhass28.customer:dimen/material_text_view_test_line_height = 0x7f070185
com.mohandhass28.customer:attr/colorButtonNormal = 0x7f0400e4
com.mohandhass28.customer:dimen/material_text_size_sp = 0x7f070184
com.mohandhass28.customer:animator/fragment_fade_exit = 0x7f020006
com.mohandhass28.customer:dimen/material_input_text_to_prefix_suffix_padding = 0x7f070182
com.mohandhass28.customer:string/exo_track_bitrate = 0x7f12008d
com.mohandhass28.customer:dimen/material_helper_text_font_1_3_padding_top = 0x7f070181
com.mohandhass28.customer:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f070180
com.mohandhass28.customer:dimen/material_helper_text_default_padding_top = 0x7f07017f
com.mohandhass28.customer:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f130088
com.mohandhass28.customer:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f07017e
com.mohandhass28.customer:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f07017d
com.mohandhass28.customer:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f07017b
com.mohandhass28.customer:dimen/abc_text_size_display_4_material = 0x7f070046
com.mohandhass28.customer:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f07017a
com.mohandhass28.customer:id/autofill_inline_suggestion_subtitle = 0x7f0a0069
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Display3 = 0x7f13001d
com.mohandhass28.customer:dimen/material_divider_thickness = 0x7f070174
com.mohandhass28.customer:dimen/material_clock_period_toggle_width = 0x7f07016f
com.mohandhass28.customer:style/Base.Widget.AppCompat.Spinner = 0x7f1300ec
com.mohandhass28.customer:attr/forceApplySystemWindowInsetTop = 0x7f0401fa
com.mohandhass28.customer:dimen/m3_fab_border_width = 0x7f070115
com.mohandhass28.customer:id/classic = 0x7f0a0093
com.mohandhass28.customer:color/m3_sys_color_dark_primary = 0x7f060158
com.mohandhass28.customer:dimen/material_clock_period_toggle_height = 0x7f07016d
com.mohandhass28.customer:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f13018b
com.mohandhass28.customer:dimen/material_clock_number_text_size = 0x7f07016c
com.mohandhass28.customer:dimen/material_clock_number_text_padding = 0x7f07016b
com.mohandhass28.customer:drawable/assets_profileimgcopy_bottomsheet_facebookpng = 0x7f08006b
com.mohandhass28.customer:dimen/material_clock_hand_stroke_width = 0x7f07016a
com.mohandhass28.customer:dimen/design_bottom_navigation_active_item_min_width = 0x7f070065
com.mohandhass28.customer:dimen/material_clock_hand_padding = 0x7f070169
com.mohandhass28.customer:anim/abc_popup_exit = 0x7f010004
com.mohandhass28.customer:attr/ratingBarStyleSmall = 0x7f040365
com.mohandhass28.customer:dimen/material_clock_hand_center_dot_radius = 0x7f070168
com.mohandhass28.customer:dimen/material_clock_face_margin_top = 0x7f070167
com.mohandhass28.customer:dimen/material_clock_display_padding = 0x7f070166
com.mohandhass28.customer:style/Base.Theme.SplashScreen.Light = 0x7f130073
com.mohandhass28.customer:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f070162
com.mohandhass28.customer:style/Widget.AppCompat.ActivityChooserView = 0x7f13031c
com.mohandhass28.customer:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f070161
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f13006d
com.mohandhass28.customer:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f07015f
com.mohandhass28.customer:style/Widget.Design.CollapsingToolbar = 0x7f130367
com.mohandhass28.customer:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f130174
com.mohandhass28.customer:style/Base.ThemeOverlay.AppCompat.Light = 0x7f13007a
com.mohandhass28.customer:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f07015d
com.mohandhass28.customer:attr/drawableTintMode = 0x7f04018c
com.mohandhass28.customer:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f07015c
com.mohandhass28.customer:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f070158
com.mohandhass28.customer:drawable/abc_cab_background_internal_bg = 0x7f080015
com.mohandhass28.customer:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f070157
com.mohandhass28.customer:style/Widget.Material3.CardView.Outlined = 0x7f13038e
com.mohandhass28.customer:string/mtrl_exceed_max_badge_number_content_description = 0x7f1200f0
com.mohandhass28.customer:drawable/abc_btn_check_material_anim = 0x7f08000a
com.mohandhass28.customer:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f070152
com.mohandhass28.customer:anim/abc_tooltip_enter = 0x7f01000a
com.mohandhass28.customer:drawable/exo_ic_forward = 0x7f0800c5
com.mohandhass28.customer:dimen/material_timepicker_dialog_buttons_margin_top = 0x7f07018c
com.mohandhass28.customer:dimen/cardview_default_elevation = 0x7f070057
com.mohandhass28.customer:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f070151
com.mohandhass28.customer:bool/enable_system_foreground_service_default = 0x7f050003
com.mohandhass28.customer:drawable/btn_radio_on_mtrl = 0x7f080095
com.mohandhass28.customer:dimen/mtrl_card_spacing = 0x7f0701e7
com.mohandhass28.customer:style/Widget.AppCompat.SearchView.ActionBar = 0x7f130351
com.mohandhass28.customer:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f070150
com.mohandhass28.customer:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f07014e
com.mohandhass28.customer:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f070148
com.mohandhass28.customer:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f070147
com.mohandhass28.customer:dimen/m3_snackbar_margin = 0x7f070134
com.mohandhass28.customer:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f070146
com.mohandhass28.customer:drawable/abc_list_selector_disabled_holo_light = 0x7f080033
com.mohandhass28.customer:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1302d2
com.mohandhass28.customer:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f070145
com.mohandhass28.customer:style/Theme.Material3.DayNight.NoActionBar = 0x7f130269
com.mohandhass28.customer:attr/layout_editor_absoluteY = 0x7f040293
com.mohandhass28.customer:drawable/tooltip_frame_dark = 0x7f08016c
com.mohandhass28.customer:style/TextAppearance.AppCompat.Headline = 0x7f1301c3
com.mohandhass28.customer:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f070144
com.mohandhass28.customer:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f0701d7
com.mohandhass28.customer:id/adjust_width = 0x7f0a0053
com.mohandhass28.customer:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f130335
com.mohandhass28.customer:style/TextAppearance.AppCompat = 0x7f1301ba
com.mohandhass28.customer:color/m3_ref_palette_dynamic_tertiary90 = 0x7f0600ec
com.mohandhass28.customer:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f070143
com.mohandhass28.customer:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f070142
com.mohandhass28.customer:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f130373
com.mohandhass28.customer:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f070140
com.mohandhass28.customer:color/wallet_hint_foreground_holo_dark = 0x7f06027d
com.mohandhass28.customer:drawable/node_modules_exporouter_assets_forward = 0x7f08013c
com.mohandhass28.customer:color/exo_error_message_background_color = 0x7f060074
com.mohandhass28.customer:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f07013e
com.mohandhass28.customer:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f0600a3
com.mohandhass28.customer:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f07013d
com.mohandhass28.customer:dimen/m3_timepicker_display_stroke_width = 0x7f070163
com.mohandhass28.customer:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f07013c
com.mohandhass28.customer:string/exo_track_selection_title_video = 0x7f120098
com.mohandhass28.customer:attr/activityChooserViewStyle = 0x7f040024
com.mohandhass28.customer:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f07013b
com.mohandhass28.customer:dimen/m3_sys_elevation_level3 = 0x7f070138
com.mohandhass28.customer:styleable/AspectRatioFrameLayout = 0x7f140013
com.mohandhass28.customer:dimen/material_cursor_inset_top = 0x7f070172
com.mohandhass28.customer:drawable/ic_rotate_left_24 = 0x7f080119
com.mohandhass28.customer:dimen/m3_sys_elevation_level2 = 0x7f070137
com.mohandhass28.customer:attr/constraintSet = 0x7f04010b
com.mohandhass28.customer:attr/actionBarPopupTheme = 0x7f040002
com.mohandhass28.customer:dimen/m3_sys_elevation_level0 = 0x7f070135
com.mohandhass28.customer:dimen/m3_snackbar_action_text_color_alpha = 0x7f070133
com.mohandhass28.customer:dimen/m3_slider_thumb_elevation = 0x7f070132
com.mohandhass28.customer:style/ResetEditText = 0x7f130165
com.mohandhass28.customer:attr/closeIconVisible = 0x7f0400d4
com.mohandhass28.customer:dimen/m3_ripple_selectable_pressed_alpha = 0x7f070131
com.mohandhass28.customer:id/grayscale = 0x7f0a0135
com.mohandhass28.customer:dimen/m3_ripple_pressed_alpha = 0x7f070130
com.mohandhass28.customer:attr/counterMaxLength = 0x7f040131
com.mohandhass28.customer:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f07014b
com.mohandhass28.customer:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0701fd
com.mohandhass28.customer:dimen/m3_ripple_hovered_alpha = 0x7f07012f
com.mohandhass28.customer:drawable/assets_profileimgcopy_bottomsheet_send = 0x7f080071
com.mohandhass28.customer:dimen/m3_navigation_rail_item_padding_bottom = 0x7f07012b
com.mohandhass28.customer:id/accessibility_custom_action_7 = 0x7f0a0033
com.mohandhass28.customer:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f070127
com.mohandhass28.customer:drawable/node_modules_exporouter_assets_error = 0x7f08013a
com.mohandhass28.customer:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f070125
com.mohandhass28.customer:anim/catalyst_push_up_out = 0x7f01001b
com.mohandhass28.customer:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f070124
com.mohandhass28.customer:dimen/m3_navigation_item_shape_inset_start = 0x7f070121
com.mohandhass28.customer:dimen/m3_navigation_item_shape_inset_end = 0x7f070120
com.mohandhass28.customer:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1300d0
com.mohandhass28.customer:dimen/m3_fab_translation_z_hovered_focused = 0x7f070117
com.mohandhass28.customer:dimen/m3_fab_corner_size = 0x7f070116
com.mohandhass28.customer:attr/fontProviderPackage = 0x7f0401f4
com.mohandhass28.customer:dimen/m3_extended_fab_start_padding = 0x7f070113
com.mohandhass28.customer:string/fab_transformation_scrim_behavior = 0x7f1200a3
com.mohandhass28.customer:dimen/m3_extended_fab_min_height = 0x7f070112
com.mohandhass28.customer:dimen/m3_extended_fab_bottom_padding = 0x7f07010f
com.mohandhass28.customer:string/expo_splash_screen_status_bar_translucent = 0x7f1200a0
com.mohandhass28.customer:id/ic_flip_24_horizontally = 0x7f0a0145
com.mohandhass28.customer:attr/show_fastforward_button = 0x7f0403a3
com.mohandhass28.customer:dimen/m3_chip_icon_size = 0x7f07010c
com.mohandhass28.customer:dimen/m3_chip_hovered_translation_z = 0x7f07010b
com.mohandhass28.customer:dimen/browser_actions_context_menu_min_padding = 0x7f070055
com.mohandhass28.customer:dimen/m3_chip_dragged_translation_z = 0x7f070109
com.mohandhass28.customer:id/browser_actions_menu_item_icon = 0x7f0a0074
com.mohandhass28.customer:style/TextAppearance.Material3.BodyMedium = 0x7f130211
com.mohandhass28.customer:attr/actionProviderClass = 0x7f040021
com.mohandhass28.customer:dimen/m3_chip_corner_size = 0x7f070107
com.mohandhass28.customer:id/dragDown = 0x7f0a00c0
com.mohandhass28.customer:dimen/m3_card_elevated_dragged_z = 0x7f070100
com.mohandhass28.customer:color/material_dynamic_neutral50 = 0x7f0601c8
com.mohandhass28.customer:dimen/m3_card_dragged_z = 0x7f0700ff
com.mohandhass28.customer:id/chip3 = 0x7f0a008f
com.mohandhass28.customer:attr/placeholderTextAppearance = 0x7f040348
com.mohandhass28.customer:dimen/m3_navigation_drawer_layout_corner_size = 0x7f07011c
com.mohandhass28.customer:attr/subheaderInsetStart = 0x7f0403d3
com.mohandhass28.customer:dimen/m3_btn_translation_z_base = 0x7f0700fd
com.mohandhass28.customer:string/tablist_description = 0x7f120134
com.mohandhass28.customer:drawable/exo_ic_default_album_image = 0x7f0800c4
com.mohandhass28.customer:drawable/abc_btn_radio_material = 0x7f08000f
com.mohandhass28.customer:dimen/mtrl_navigation_rail_text_size = 0x7f07021c
com.mohandhass28.customer:dimen/mtrl_large_touch_target = 0x7f070206
com.mohandhass28.customer:styleable/ForegroundLinearLayout = 0x7f14003a
com.mohandhass28.customer:dimen/m3_btn_text_btn_padding_left = 0x7f0700fb
com.mohandhass28.customer:dimen/m3_btn_text_btn_icon_padding_left = 0x7f0700f9
com.mohandhass28.customer:styleable/MaterialCalendar = 0x7f140056
com.mohandhass28.customer:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f13045e
com.mohandhass28.customer:dimen/m3_btn_stroke_size = 0x7f0700f8
com.mohandhass28.customer:dimen/hint_pressed_alpha_material_light = 0x7f0700c2
com.mohandhass28.customer:dimen/m3_btn_padding_right = 0x7f0700f6
com.mohandhass28.customer:dimen/mtrl_btn_max_width = 0x7f0701ab
com.mohandhass28.customer:dimen/m3_btn_padding_left = 0x7f0700f5
com.mohandhass28.customer:string/gcm_defaultSenderId = 0x7f1200b0
com.mohandhass28.customer:dimen/m3_btn_inset = 0x7f0700f2
com.mohandhass28.customer:attr/closeIconEnabled = 0x7f0400cf
com.mohandhass28.customer:dimen/m3_btn_icon_only_min_width = 0x7f0700f1
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f06016a
com.mohandhass28.customer:dimen/m3_btn_icon_only_icon_padding = 0x7f0700f0
com.mohandhass28.customer:style/Widget.Material3.Button.TextButton = 0x7f130383
com.mohandhass28.customer:color/m3_timepicker_button_background_color = 0x7f0601b0
com.mohandhass28.customer:dimen/m3_btn_icon_only_default_size = 0x7f0700ef
com.mohandhass28.customer:anim/abc_slide_in_top = 0x7f010007
com.mohandhass28.customer:color/m3_ref_palette_dynamic_tertiary95 = 0x7f0600ed
com.mohandhass28.customer:dimen/m3_btn_icon_only_default_padding = 0x7f0700ee
com.mohandhass28.customer:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f130045
com.mohandhass28.customer:dimen/m3_btn_elevation = 0x7f0700eb
com.mohandhass28.customer:dimen/m3_btn_elevated_btn_elevation = 0x7f0700ea
com.mohandhass28.customer:integer/m3_sys_motion_duration_1000 = 0x7f0b0013
com.mohandhass28.customer:dimen/m3_btn_disabled_elevation = 0x7f0700e8
com.mohandhass28.customer:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0c0000
com.mohandhass28.customer:attr/cameraZoom = 0x7f040099
com.mohandhass28.customer:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0700e3
com.mohandhass28.customer:dimen/m3_bottom_sheet_modal_elevation = 0x7f0700e2
com.mohandhass28.customer:drawable/node_modules_reactnavigation_elements_lib_commonjs_assets_backiconmask = 0x7f080149
com.mohandhass28.customer:id/exo_fullscreen = 0x7f0a00ea
com.mohandhass28.customer:dimen/m3_bottom_sheet_elevation = 0x7f0700e1
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f130036
com.mohandhass28.customer:attr/cropInitialCropWindowPaddingRatio = 0x7f040149
com.mohandhass28.customer:dimen/m3_bottom_nav_min_height = 0x7f0700e0
com.mohandhass28.customer:attr/fabCradleMargin = 0x7f0401c3
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0600bd
com.mohandhass28.customer:attr/show_shuffle_button = 0x7f0403a7
com.mohandhass28.customer:dimen/m3_badge_with_text_vertical_offset = 0x7f0700da
com.mohandhass28.customer:drawable/exo_styled_controls_next = 0x7f0800ef
com.mohandhass28.customer:dimen/m3_badge_with_text_radius = 0x7f0700d9
com.mohandhass28.customer:dimen/m3_badge_with_text_horizontal_offset = 0x7f0700d8
com.mohandhass28.customer:dimen/m3_badge_horizontal_offset = 0x7f0700d5
com.mohandhass28.customer:style/Widget.Material3.Button.Icon = 0x7f13037f
com.mohandhass28.customer:style/Widget.AppCompat.Button.Colored = 0x7f130322
com.mohandhass28.customer:attr/show_next_button = 0x7f0403a4
com.mohandhass28.customer:dimen/m3_appbar_size_large = 0x7f0700d3
com.mohandhass28.customer:drawable/ic_alert = 0x7f080104
com.mohandhass28.customer:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0700d1
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1301d8
com.mohandhass28.customer:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f070200
com.mohandhass28.customer:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0700d0
com.mohandhass28.customer:id/check_list = 0x7f0a0089
com.mohandhass28.customer:drawable/abc_list_selector_background_transition_holo_light = 0x7f080031
com.mohandhass28.customer:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0700ce
com.mohandhass28.customer:dimen/m3_alert_dialog_icon_size = 0x7f0700cb
com.mohandhass28.customer:color/m3_sys_color_light_on_surface = 0x7f060199
com.mohandhass28.customer:dimen/m3_alert_dialog_icon_margin = 0x7f0700ca
com.mohandhass28.customer:dimen/m3_alert_dialog_elevation = 0x7f0700c9
com.mohandhass28.customer:dimen/m3_appbar_size_compact = 0x7f0700d2
com.mohandhass28.customer:dimen/m3_alert_dialog_corner_size = 0x7f0700c8
com.mohandhass28.customer:style/Base.V26.Theme.AppCompat.Light = 0x7f1300ac
com.mohandhass28.customer:dimen/m3_alert_dialog_action_top_padding = 0x7f0700c7
com.mohandhass28.customer:dimen/abc_dialog_corner_radius_material = 0x7f07001b
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0600bf
com.mohandhass28.customer:dimen/m3_alert_dialog_action_bottom_padding = 0x7f0700c6
com.mohandhass28.customer:attr/played_ad_marker_color = 0x7f04034b
com.mohandhass28.customer:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f0700c3
com.mohandhass28.customer:drawable/assets_icon_chips = 0x7f08005d
com.mohandhass28.customer:drawable/abc_textfield_search_material = 0x7f080053
com.mohandhass28.customer:dimen/hint_pressed_alpha_material_dark = 0x7f0700c1
com.mohandhass28.customer:id/textStart = 0x7f0a0249
com.mohandhass28.customer:dimen/hint_alpha_material_dark = 0x7f0700bf
com.mohandhass28.customer:dimen/highlight_alpha_material_dark = 0x7f0700bd
com.mohandhass28.customer:style/Base.V21.Theme.MaterialComponents = 0x7f13009c
com.mohandhass28.customer:attr/bottomSheetStyle = 0x7f040073
com.mohandhass28.customer:attr/cropTouchRadius = 0x7f040159
com.mohandhass28.customer:id/accessibility_state = 0x7f0a003a
com.mohandhass28.customer:drawable/ic_launcher_background = 0x7f080110
com.mohandhass28.customer:dimen/design_bottom_navigation_label_padding = 0x7f07006c
com.mohandhass28.customer:dimen/design_fab_elevation = 0x7f070074
com.mohandhass28.customer:dimen/fastscroll_minimum_range = 0x7f0700ba
com.mohandhass28.customer:dimen/fastscroll_margin = 0x7f0700b9
com.mohandhass28.customer:attr/drawableEndCompat = 0x7f040186
com.mohandhass28.customer:dimen/mtrl_textinput_box_stroke_width_default = 0x7f070242
com.mohandhass28.customer:string/selected = 0x7f120124
com.mohandhass28.customer:dimen/exo_styled_progress_touch_target_height = 0x7f0700b7
com.mohandhass28.customer:dimen/fastscroll_default_thickness = 0x7f0700b8
com.mohandhass28.customer:dimen/exo_styled_progress_dragged_thumb_size = 0x7f0700b3
com.mohandhass28.customer:dimen/exo_styled_progress_bar_height = 0x7f0700b2
com.mohandhass28.customer:id/mtrl_calendar_selection_frame = 0x7f0a0193
com.mohandhass28.customer:dimen/exo_styled_bottom_bar_time_padding = 0x7f0700af
com.mohandhass28.customer:styleable/Slider = 0x7f140081
com.mohandhass28.customer:attr/deriveConstraintsFrom = 0x7f040174
com.mohandhass28.customer:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f07019c
com.mohandhass28.customer:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f0701d9
com.mohandhass28.customer:drawable/exo_notification_fastforward = 0x7f0800e1
com.mohandhass28.customer:dimen/exo_styled_bottom_bar_height = 0x7f0700ad
com.mohandhass28.customer:attr/layout_goneMarginTop = 0x7f040299
com.mohandhass28.customer:attr/startIconContentDescription = 0x7f0403c1
com.mohandhass28.customer:dimen/exo_small_icon_horizontal_margin = 0x7f0700a9
com.mohandhass28.customer:dimen/exo_settings_text_height = 0x7f0700a7
com.mohandhass28.customer:animator/linear_indeterminate_line2_tail_interpolator = 0x7f02000c
com.mohandhass28.customer:drawable/assets_profileimgcopy_bottomsheet_screenshortlong = 0x7f080070
com.mohandhass28.customer:dimen/splashscreen_icon_mask_stroke_with_background = 0x7f070261
com.mohandhass28.customer:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f070154
com.mohandhass28.customer:dimen/mtrl_progress_circular_inset_extra_small = 0x7f07021e
com.mohandhass28.customer:dimen/exo_settings_sub_text_size = 0x7f0700a6
com.mohandhass28.customer:dimen/exo_settings_offset = 0x7f0700a5
com.mohandhass28.customer:dimen/exo_settings_main_text_size = 0x7f0700a4
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f130308
com.mohandhass28.customer:attr/numericModifiers = 0x7f040323
com.mohandhass28.customer:attr/listPopupWindowStyle = 0x7f0402ad
com.mohandhass28.customer:dimen/mtrl_calendar_landscape_header_width = 0x7f0701ce
com.mohandhass28.customer:style/CalendarDatePickerStyle = 0x7f130118
com.mohandhass28.customer:color/m3_sys_color_light_on_error = 0x7f060193
com.mohandhass28.customer:dimen/exo_settings_icon_size = 0x7f0700a3
com.mohandhass28.customer:id/inward = 0x7f0a0154
com.mohandhass28.customer:dimen/exo_media_button_height = 0x7f07009f
com.mohandhass28.customer:dimen/exo_icon_text_size = 0x7f07009e
com.mohandhass28.customer:dimen/exo_error_message_text_size = 0x7f070099
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral50 = 0x7f0600b4
com.mohandhass28.customer:drawable/abc_ic_voice_search_api_material = 0x7f080027
com.mohandhass28.customer:dimen/exo_error_message_text_padding_horizontal = 0x7f070097
com.mohandhass28.customer:drawable/exo_notification_next = 0x7f0800e2
com.mohandhass28.customer:dimen/exo_error_message_height = 0x7f070095
com.mohandhass28.customer:styleable/PopupWindow = 0x7f140072
com.mohandhass28.customer:dimen/hint_alpha_material_light = 0x7f0700c0
com.mohandhass28.customer:attr/trackTint = 0x7f040470
com.mohandhass28.customer:dimen/disabled_alpha_material_dark = 0x7f070093
com.mohandhass28.customer:dimen/design_textinput_caption_translate_y = 0x7f070092
com.mohandhass28.customer:color/m3_ref_palette_primary20 = 0x7f060119
com.mohandhass28.customer:dimen/design_tab_text_size = 0x7f070090
com.mohandhass28.customer:dimen/design_tab_scrollable_min_width = 0x7f07008f
com.mohandhass28.customer:dimen/design_snackbar_text_size = 0x7f07008d
com.mohandhass28.customer:dimen/design_snackbar_padding_vertical_2lines = 0x7f07008c
com.mohandhass28.customer:dimen/design_snackbar_padding_vertical = 0x7f07008b
com.mohandhass28.customer:dimen/design_snackbar_background_corner_radius = 0x7f070085
com.mohandhass28.customer:string/material_timepicker_am = 0x7f1200e4
com.mohandhass28.customer:dimen/design_snackbar_action_inline_max_width = 0x7f070083
com.mohandhass28.customer:dimen/design_navigation_max_width = 0x7f070080
com.mohandhass28.customer:dimen/design_navigation_item_vertical_padding = 0x7f07007f
com.mohandhass28.customer:attr/thumbElevation = 0x7f04043c
com.mohandhass28.customer:drawable/abc_textfield_default_mtrl_alpha = 0x7f080050
com.mohandhass28.customer:style/ShapeAppearanceOverlay.Material3.TextField.Filled = 0x7f13019d
com.mohandhass28.customer:dimen/design_navigation_item_icon_padding = 0x7f07007e
com.mohandhass28.customer:drawable/abc_control_background_material = 0x7f080018
com.mohandhass28.customer:color/material_timepicker_button_background = 0x7f06021e
com.mohandhass28.customer:dimen/mtrl_calendar_header_selection_line_height = 0x7f0701ca
com.mohandhass28.customer:styleable/FloatingActionButton_Behavior_Layout = 0x7f140036
com.mohandhass28.customer:dimen/material_emphasis_disabled = 0x7f070175
com.mohandhass28.customer:dimen/design_navigation_item_horizontal_padding = 0x7f07007d
com.mohandhass28.customer:dimen/design_navigation_icon_size = 0x7f07007c
com.mohandhass28.customer:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f130047
com.mohandhass28.customer:string/password_toggle_content_description = 0x7f120114
com.mohandhass28.customer:id/navigation_header_container = 0x7f0a01ab
com.mohandhass28.customer:dimen/design_fab_translation_z_hovered_focused = 0x7f070078
com.mohandhass28.customer:style/Theme.MaterialComponents.CompactMenu = 0x7f130277
com.mohandhass28.customer:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f130200
com.mohandhass28.customer:dimen/design_fab_size_normal = 0x7f070077
com.mohandhass28.customer:id/SHOW_PATH = 0x7f0a000c
com.mohandhass28.customer:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f13040f
com.mohandhass28.customer:drawable/exo_controls_repeat_off = 0x7f0800b9
com.mohandhass28.customer:dimen/design_fab_border_width = 0x7f070073
com.mohandhass28.customer:dimen/design_bottom_sheet_modal_elevation = 0x7f070071
com.mohandhass28.customer:dimen/design_bottom_navigation_shadow_height = 0x7f07006e
com.mohandhass28.customer:layout/test_action_chip = 0x7f0d0091
com.mohandhass28.customer:dimen/design_bottom_navigation_item_min_width = 0x7f07006b
com.mohandhass28.customer:dimen/design_bottom_navigation_item_max_width = 0x7f07006a
com.mohandhass28.customer:string/material_hour_suffix = 0x7f1200da
com.mohandhass28.customer:dimen/design_bottom_navigation_icon_size = 0x7f070069
com.mohandhass28.customer:drawable/notify_panel_notification_icon_bg = 0x7f080159
com.mohandhass28.customer:color/catalyst_redbox_background = 0x7f060035
com.mohandhass28.customer:dimen/mtrl_calendar_month_horizontal_padding = 0x7f0701d0
com.mohandhass28.customer:dimen/design_bottom_navigation_elevation = 0x7f070067
com.mohandhass28.customer:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f130461
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f060162
com.mohandhass28.customer:dimen/design_bottom_navigation_active_text_size = 0x7f070066
com.mohandhass28.customer:string/material_motion_easing_linear = 0x7f1200e0
com.mohandhass28.customer:dimen/compat_notification_large_icon_max_width = 0x7f070060
com.mohandhass28.customer:id/accessibility_custom_action_23 = 0x7f0a0026
com.mohandhass28.customer:attr/navigationRailStyle = 0x7f04031d
com.mohandhass28.customer:drawable/$avd_hide_password__0 = 0x7f080000
com.mohandhass28.customer:dimen/compat_control_corner_material = 0x7f07005e
com.mohandhass28.customer:dimen/compat_button_padding_vertical_material = 0x7f07005d
com.mohandhass28.customer:dimen/compat_button_inset_vertical_material = 0x7f07005b
com.mohandhass28.customer:dimen/compat_button_inset_horizontal_material = 0x7f07005a
com.mohandhass28.customer:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f070149
com.mohandhass28.customer:string/app_name = 0x7f12001d
com.mohandhass28.customer:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f08012c
com.mohandhass28.customer:id/normal = 0x7f0a01af
com.mohandhass28.customer:attr/dialogCornerRadius = 0x7f040175
com.mohandhass28.customer:dimen/browser_actions_context_menu_max_width = 0x7f070054
com.mohandhass28.customer:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f130284
com.mohandhass28.customer:dimen/autofill_inline_suggestion_icon_size = 0x7f070053
com.mohandhass28.customer:drawable/abc_seekbar_tick_mark_material = 0x7f080041
com.mohandhass28.customer:style/ThemeOverlay.Material3.BottomAppBar = 0x7f1302bc
com.mohandhass28.customer:dimen/abc_text_size_menu_material = 0x7f07004b
com.mohandhass28.customer:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f1300fb
com.mohandhass28.customer:id/staticPostLayout = 0x7f0a0228
com.mohandhass28.customer:dimen/abc_text_size_menu_header_material = 0x7f07004a
com.mohandhass28.customer:styleable/CustomAttribute = 0x7f14002e
com.mohandhass28.customer:attr/indicatorSize = 0x7f04022c
com.mohandhass28.customer:drawable/exo_ic_speed = 0x7f0800ce
com.mohandhass28.customer:attr/uiMapToolbar = 0x7f04047c
com.mohandhass28.customer:dimen/abc_text_size_headline_material = 0x7f070047
com.mohandhass28.customer:dimen/abc_text_size_display_2_material = 0x7f070044
com.mohandhass28.customer:attr/collapsingToolbarLayoutMediumStyle = 0x7f0400df
com.mohandhass28.customer:dimen/abc_text_size_display_1_material = 0x7f070043
com.mohandhass28.customer:integer/mtrl_btn_anim_delay_ms = 0x7f0b002b
com.mohandhass28.customer:dimen/abc_text_size_caption_material = 0x7f070042
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0600c1
com.mohandhass28.customer:dimen/notification_subtext_size = 0x7f07025b
com.mohandhass28.customer:attr/fontProviderSystemFontFamily = 0x7f0401f6
com.mohandhass28.customer:dimen/abc_text_size_button_material = 0x7f070041
com.mohandhass28.customer:id/ignore = 0x7f0a014d
com.mohandhass28.customer:id/home = 0x7f0a0140
com.mohandhass28.customer:attr/cropCornerCircleFillColor = 0x7f040141
com.mohandhass28.customer:dimen/abc_text_size_body_1_material = 0x7f07003f
com.mohandhass28.customer:attr/region_heightMoreThan = 0x7f040368
com.mohandhass28.customer:dimen/exo_styled_progress_layout_height = 0x7f0700b5
com.mohandhass28.customer:dimen/abc_switch_padding = 0x7f07003e
com.mohandhass28.customer:dimen/abc_star_medium = 0x7f07003c
com.mohandhass28.customer:attr/scrubber_disabled_size = 0x7f040388
com.mohandhass28.customer:dimen/abc_star_big = 0x7f07003b
com.mohandhass28.customer:color/bright_foreground_inverse_material_dark = 0x7f060024
com.mohandhass28.customer:drawable/ic_call_decline = 0x7f080109
com.mohandhass28.customer:style/Widget.MaterialComponents.TextView = 0x7f130464
com.mohandhass28.customer:dimen/abc_seekbar_track_background_height_material = 0x7f070038
com.mohandhass28.customer:dimen/abc_search_view_preferred_height = 0x7f070036
com.mohandhass28.customer:dimen/abc_progress_bar_height_material = 0x7f070035
com.mohandhass28.customer:attr/initialActivityCount = 0x7f04022d
com.mohandhass28.customer:dimen/abc_list_item_height_material = 0x7f070031
com.mohandhass28.customer:dimen/abc_floating_window_z = 0x7f07002f
com.mohandhass28.customer:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome = 0x7f110005
com.mohandhass28.customer:dimen/abc_edit_text_inset_horizontal_material = 0x7f07002d
com.mohandhass28.customer:dimen/mtrl_extended_fab_corner_radius = 0x7f0701ee
com.mohandhass28.customer:dimen/abc_dropdownitem_text_padding_right = 0x7f07002b
com.mohandhass28.customer:string/wallet_buy_button_place_holder = 0x7f12013a
com.mohandhass28.customer:dimen/exo_error_message_margin_bottom = 0x7f070096
com.mohandhass28.customer:dimen/exo_error_message_text_padding_vertical = 0x7f070098
com.mohandhass28.customer:dimen/abc_dropdownitem_icon_width = 0x7f070029
com.mohandhass28.customer:style/Widget.Material3.FloatingActionButton.Surface = 0x7f1303b4
com.mohandhass28.customer:string/exo_controls_cc_enabled_description = 0x7f12006a
com.mohandhass28.customer:dimen/abc_disabled_alpha_material_light = 0x7f070028
com.mohandhass28.customer:string/abc_action_bar_up_description = 0x7f120001
com.mohandhass28.customer:dimen/abc_dialog_title_divider_material = 0x7f070026
com.mohandhass28.customer:dimen/abc_disabled_alpha_material_dark = 0x7f070027
com.mohandhass28.customer:id/circle_center = 0x7f0a0092
com.mohandhass28.customer:attr/uiScrollGestures = 0x7f04047e
com.mohandhass28.customer:dimen/abc_dialog_padding_material = 0x7f070024
com.mohandhass28.customer:style/TextAppearance.AppCompat.Caption = 0x7f1301be
com.mohandhass28.customer:dimen/abc_dialog_min_width_minor = 0x7f070023
com.mohandhass28.customer:dimen/abc_dialog_min_width_major = 0x7f070022
com.mohandhass28.customer:dimen/abc_dialog_fixed_width_major = 0x7f07001e
com.mohandhass28.customer:color/m3_sys_color_dark_on_primary = 0x7f06014f
com.mohandhass28.customer:dimen/abc_dialog_fixed_height_major = 0x7f07001c
com.mohandhass28.customer:animator/mtrl_card_state_list_anim = 0x7f020015
com.mohandhass28.customer:dimen/mtrl_alert_dialog_background_inset_start = 0x7f07018f
com.mohandhass28.customer:dimen/abc_control_padding_material = 0x7f07001a
com.mohandhass28.customer:drawable/assets_resimages_images_4 = 0x7f08007c
com.mohandhass28.customer:dimen/abc_config_prefDialogWidth = 0x7f070017
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f1302e9
com.mohandhass28.customer:dimen/m3_btn_dialog_btn_spacing = 0x7f0700e7
com.mohandhass28.customer:dimen/abc_cascading_menus_min_smallest_width = 0x7f070016
com.mohandhass28.customer:attr/tabIndicatorFullWidth = 0x7f0403ee
com.mohandhass28.customer:dimen/abc_button_padding_vertical_material = 0x7f070015
com.mohandhass28.customer:dimen/abc_button_inset_vertical_material = 0x7f070013
com.mohandhass28.customer:attr/actionModeTheme = 0x7f04001d
com.mohandhass28.customer:id/center = 0x7f0a0082
com.mohandhass28.customer:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f07000c
com.mohandhass28.customer:dimen/notification_action_text_size = 0x7f070250
com.mohandhass28.customer:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b
com.mohandhass28.customer:dimen/abc_action_bar_stacked_tab_max_width = 0x7f07000a
com.mohandhass28.customer:dimen/mtrl_shape_corner_size_large_component = 0x7f07022b
com.mohandhass28.customer:dimen/abc_action_bar_stacked_max_height = 0x7f070009
com.mohandhass28.customer:id/fitXY = 0x7f0a011f
com.mohandhass28.customer:dimen/abc_action_bar_overflow_padding_start_material = 0x7f070008
com.mohandhass28.customer:attr/swipeRefreshLayoutProgressSpinnerBackgroundColor = 0x7f0403e0
com.mohandhass28.customer:dimen/abc_action_bar_overflow_padding_end_material = 0x7f070007
com.mohandhass28.customer:id/scroll = 0x7f0a01f5
com.mohandhass28.customer:dimen/abc_text_size_display_3_material = 0x7f070045
com.mohandhass28.customer:dimen/abc_action_bar_elevation_material = 0x7f070005
com.mohandhass28.customer:dimen/abc_action_bar_default_padding_start_material = 0x7f070004
com.mohandhass28.customer:dimen/abc_action_bar_default_padding_end_material = 0x7f070003
com.mohandhass28.customer:dimen/abc_action_bar_content_inset_with_nav = 0x7f070001
com.mohandhass28.customer:style/Widget.Compat.NotificationActionText = 0x7f130363
com.mohandhass28.customer:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f130148
com.mohandhass28.customer:color/wallet_primary_text_holo_light = 0x7f060281
com.mohandhass28.customer:style/TextAppearance.Material3.BodySmall = 0x7f130212
com.mohandhass28.customer:dimen/abc_text_size_subtitle_material_toolbar = 0x7f07004e
com.mohandhass28.customer:string/mtrl_picker_a11y_prev_month = 0x7f1200f3
com.mohandhass28.customer:string/catalyst_change_bundle_location = 0x7f120028
com.mohandhass28.customer:id/percent = 0x7f0a01c4
com.mohandhass28.customer:dimen/abc_control_corner_material = 0x7f070018
com.mohandhass28.customer:color/wallet_holo_blue_light = 0x7f06027f
com.mohandhass28.customer:style/ThemeOverlay.AppCompat = 0x7f1302ab
com.mohandhass28.customer:dimen/clock_face_margin_start = 0x7f070059
com.mohandhass28.customer:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f1302c9
com.mohandhass28.customer:style/Theme = 0x7f130237
com.mohandhass28.customer:color/wallet_hint_foreground_holo_light = 0x7f06027e
com.mohandhass28.customer:id/asConfigured = 0x7f0a0061
com.mohandhass28.customer:color/m3_dynamic_highlighted_text = 0x7f06009d
com.mohandhass28.customer:dimen/m3_card_elevated_elevation = 0x7f070101
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.Overline = 0x7f13022c
com.mohandhass28.customer:color/wallet_highlighted_text_holo_dark = 0x7f06027b
com.mohandhass28.customer:styleable/Constraint = 0x7f140027
com.mohandhass28.customer:color/wallet_dim_foreground_disabled_holo_dark = 0x7f060279
com.mohandhass28.customer:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f130056
com.mohandhass28.customer:dimen/mtrl_slider_thumb_elevation = 0x7f070232
com.mohandhass28.customer:color/m3_sys_color_light_inverse_primary = 0x7f060190
com.mohandhass28.customer:color/wallet_bright_foreground_holo_light = 0x7f060278
com.mohandhass28.customer:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f13028e
com.mohandhass28.customer:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f1301a1
com.mohandhass28.customer:color/vector_tint_theme_color = 0x7f060275
com.mohandhass28.customer:attr/dayStyle = 0x7f04016c
com.mohandhass28.customer:drawable/mtrl_ic_error = 0x7f080134
com.mohandhass28.customer:color/tooltip_background_dark = 0x7f060272
com.mohandhass28.customer:style/Theme.MaterialComponents.DayNight = 0x7f130278
com.mohandhass28.customer:dimen/m3_sys_elevation_level5 = 0x7f07013a
com.mohandhass28.customer:attr/tabIconTintMode = 0x7f0403e9
com.mohandhass28.customer:color/test_mtrl_calendar_day_selected = 0x7f060271
com.mohandhass28.customer:anim/rns_standard_accelerate_interpolator = 0x7f010040
com.mohandhass28.customer:color/test_mtrl_calendar_day = 0x7f060270
com.mohandhass28.customer:integer/mtrl_view_gone = 0x7f0b0034
com.mohandhass28.customer:attr/flow_wrapMode = 0x7f0401ed
com.mohandhass28.customer:color/test_color = 0x7f06026f
com.mohandhass28.customer:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f130374
com.mohandhass28.customer:string/copy_toast_msg = 0x7f12005d
com.mohandhass28.customer:color/switch_thumb_normal_material_dark = 0x7f06026d
com.mohandhass28.customer:id/exo_content_frame = 0x7f0a00e0
com.mohandhass28.customer:drawable/assets_dailyessentialsassert_hotal_2 = 0x7f080059
com.mohandhass28.customer:color/secondary_text_disabled_material_dark = 0x7f060266
com.mohandhass28.customer:color/secondary_text_default_material_light = 0x7f060265
com.mohandhass28.customer:id/enterAlwaysCollapsed = 0x7f0a00d6
com.mohandhass28.customer:color/secondary_text_default_material_dark = 0x7f060264
com.mohandhass28.customer:attr/latLngBoundsNorthEastLongitude = 0x7f04025b
com.mohandhass28.customer:color/radiobutton_themeable_attribute_color = 0x7f060261
com.mohandhass28.customer:id/end = 0x7f0a00d1
com.mohandhass28.customer:attr/useViewLifecycle = 0x7f040486
com.mohandhass28.customer:color/primary_text_disabled_material_light = 0x7f060260
com.mohandhass28.customer:drawable/exo_icon_shuffle_on = 0x7f0800de
com.mohandhass28.customer:color/primary_text_default_material_dark = 0x7f06025d
com.mohandhass28.customer:color/primary_material_light = 0x7f06025c
com.mohandhass28.customer:string/material_timepicker_select_time = 0x7f1200e9
com.mohandhass28.customer:id/monochrome = 0x7f0a0185
com.mohandhass28.customer:attr/collapsedTitleTextAppearance = 0x7f0400da
com.mohandhass28.customer:color/primary_material_dark = 0x7f06025b
com.mohandhass28.customer:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f1303dd
com.mohandhass28.customer:color/mtrl_textinput_hovered_box_stroke_color = 0x7f060255
com.mohandhass28.customer:color/mtrl_textinput_default_box_stroke_color = 0x7f060251
com.mohandhass28.customer:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1300a2
com.mohandhass28.customer:dimen/m3_btn_icon_btn_padding_left = 0x7f0700ec
com.mohandhass28.customer:attr/startIconDrawable = 0x7f0403c2
com.mohandhass28.customer:color/mtrl_tabs_ripple_color = 0x7f06024f
com.mohandhass28.customer:color/mtrl_tabs_icon_color_selector_colored = 0x7f06024d
com.mohandhass28.customer:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f13039d
com.mohandhass28.customer:id/CTRL = 0x7f0a0003
com.mohandhass28.customer:dimen/design_navigation_elevation = 0x7f07007a
com.mohandhass28.customer:color/mtrl_outlined_icon_tint = 0x7f060247
com.mohandhass28.customer:color/abc_tint_default = 0x7f060014
com.mohandhass28.customer:color/mtrl_on_surface_ripple_color = 0x7f060246
com.mohandhass28.customer:drawable/common_google_signin_btn_text_dark_normal_background = 0x7f0800a4
com.mohandhass28.customer:drawable/assets_cardimage_cardimage = 0x7f080057
com.mohandhass28.customer:attr/preserveIconSpacing = 0x7f040356
com.mohandhass28.customer:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f060245
com.mohandhass28.customer:color/design_default_color_on_background = 0x7f060055
com.mohandhass28.customer:color/mtrl_navigation_item_background_color = 0x7f060242
com.mohandhass28.customer:attr/brightness = 0x7f04007f
com.mohandhass28.customer:color/mtrl_navigation_bar_ripple_color = 0x7f060241
com.mohandhass28.customer:dimen/fingerprint_icon_size = 0x7f0700bb
com.mohandhass28.customer:dimen/mtrl_extended_fab_icon_size = 0x7f0701f4
com.mohandhass28.customer:color/mtrl_navigation_bar_item_tint = 0x7f060240
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.Bridge = 0x7f13005f
com.mohandhass28.customer:color/mtrl_indicator_text_color = 0x7f06023d
com.mohandhass28.customer:color/material_dynamic_neutral70 = 0x7f0601ca
com.mohandhass28.customer:color/mtrl_filled_stroke_color = 0x7f06023c
com.mohandhass28.customer:color/mtrl_fab_icon_text_color_selector = 0x7f060238
com.mohandhass28.customer:color/mtrl_error = 0x7f060236
com.mohandhass28.customer:color/mtrl_choice_chip_ripple_color = 0x7f060234
com.mohandhass28.customer:color/mtrl_choice_chip_background_color = 0x7f060233
com.mohandhass28.customer:attr/dragDirection = 0x7f040181
com.mohandhass28.customer:color/mtrl_chip_surface_color = 0x7f060231
com.mohandhass28.customer:color/mtrl_card_view_ripple = 0x7f06022e
com.mohandhass28.customer:color/mtrl_calendar_selected_range = 0x7f06022c
com.mohandhass28.customer:color/mtrl_btn_transparent_bg_color = 0x7f06022a
com.mohandhass28.customer:dimen/mtrl_tooltip_arrowSize = 0x7f070249
com.mohandhass28.customer:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f130462
com.mohandhass28.customer:dimen/m3_divider_heavy_thickness = 0x7f07010e
com.mohandhass28.customer:color/mtrl_btn_text_color_selector = 0x7f060229
com.mohandhass28.customer:string/expo_splash_screen_resize_mode = 0x7f12009f
com.mohandhass28.customer:dimen/mtrl_calendar_header_divider_thickness = 0x7f0701c7
com.mohandhass28.customer:color/mtrl_btn_text_color_disabled = 0x7f060228
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f130432
com.mohandhass28.customer:color/mtrl_btn_text_btn_ripple_color = 0x7f060227
com.mohandhass28.customer:dimen/material_time_picker_minimum_screen_height = 0x7f07018a
com.mohandhass28.customer:attr/bottomInsetScrimEnabled = 0x7f040070
com.mohandhass28.customer:drawable/ic_fullscreen_32dp = 0x7f08010d
com.mohandhass28.customer:string/material_clock_display_divider = 0x7f1200d7
com.mohandhass28.customer:color/mtrl_btn_text_btn_bg_color_selector = 0x7f060226
com.mohandhass28.customer:style/Base.Widget.MaterialComponents.TextView = 0x7f130112
com.mohandhass28.customer:attr/fastScrollVerticalThumbDrawable = 0x7f0401ce
com.mohandhass28.customer:color/mtrl_btn_bg_color_selector = 0x7f060223
com.mohandhass28.customer:layout/abc_list_menu_item_checkbox = 0x7f0d000e
com.mohandhass28.customer:color/material_timepicker_clockface = 0x7f060221
com.mohandhass28.customer:dimen/disabled_alpha_material_light = 0x7f070094
com.mohandhass28.customer:id/exo_ad_overlay = 0x7f0a00d8
com.mohandhass28.customer:color/material_timepicker_clock_text_color = 0x7f060220
com.mohandhass28.customer:id/floating = 0x7f0a0124
com.mohandhass28.customer:drawable/$avd_hide_password__1 = 0x7f080001
com.mohandhass28.customer:attr/clockHandColor = 0x7f0400cb
com.mohandhass28.customer:color/material_timepicker_button_stroke = 0x7f06021f
com.mohandhass28.customer:id/rzp_innerbox = 0x7f0a01ea
com.mohandhass28.customer:attr/extendedFloatingActionButtonStyle = 0x7f0401bd
com.mohandhass28.customer:attr/textEndPadding = 0x7f04042d
com.mohandhass28.customer:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f0701cf
com.mohandhass28.customer:id/google_wallet_grayscale = 0x7f0a0131
com.mohandhass28.customer:color/m3_chip_background_color = 0x7f06008b
com.mohandhass28.customer:id/elastic = 0x7f0a00d0
com.mohandhass28.customer:styleable/DrawerArrowToggle = 0x7f140031
com.mohandhass28.customer:color/material_slider_inactive_track_color = 0x7f06021c
com.mohandhass28.customer:color/material_slider_halo_color = 0x7f06021a
com.mohandhass28.customer:color/material_dynamic_neutral_variant50 = 0x7f0601d5
com.mohandhass28.customer:color/material_slider_active_track_color = 0x7f060219
com.mohandhass28.customer:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f070198
com.mohandhass28.customer:attr/linearProgressIndicatorStyle = 0x7f0402a5
com.mohandhass28.customer:color/material_on_surface_stroke = 0x7f060217
com.mohandhass28.customer:color/material_on_primary_disabled = 0x7f060211
com.mohandhass28.customer:color/material_on_background_emphasis_medium = 0x7f060210
com.mohandhass28.customer:color/material_on_background_disabled = 0x7f06020e
com.mohandhass28.customer:color/material_harmonized_color_on_error = 0x7f06020c
com.mohandhass28.customer:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f130264
com.mohandhass28.customer:color/m3_button_ripple_color = 0x7f060083
com.mohandhass28.customer:color/material_harmonized_color_error = 0x7f06020a
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f13002c
com.mohandhass28.customer:layout/abc_action_mode_close_item_material = 0x7f0d0005
com.mohandhass28.customer:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0700dd
com.mohandhass28.customer:dimen/splashscreen_icon_mask_stroke_no_background = 0x7f070260
com.mohandhass28.customer:color/material_grey_900 = 0x7f060209
com.mohandhass28.customer:color/material_grey_850 = 0x7f060208
com.mohandhass28.customer:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f130411
com.mohandhass28.customer:color/error_color_material_light = 0x7f06006f
com.mohandhass28.customer:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f080129
com.mohandhass28.customer:layout/notification_template_big_media_narrow_custom = 0x7f0d007c
com.mohandhass28.customer:color/material_grey_800 = 0x7f060207
com.mohandhass28.customer:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f130412
com.mohandhass28.customer:attr/autofillInlineSuggestionStartIconStyle = 0x7f040047
com.mohandhass28.customer:color/material_grey_300 = 0x7f060204
com.mohandhass28.customer:attr/checkMarkCompat = 0x7f0400a4
com.mohandhass28.customer:color/material_dynamic_tertiary80 = 0x7f0601ff
com.mohandhass28.customer:color/material_dynamic_tertiary70 = 0x7f0601fe
com.mohandhass28.customer:color/material_dynamic_tertiary60 = 0x7f0601fd
com.mohandhass28.customer:dimen/material_clock_size = 0x7f070170
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f13029a
com.mohandhass28.customer:color/material_dynamic_tertiary50 = 0x7f0601fc
com.mohandhass28.customer:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f13023e
com.mohandhass28.customer:drawable/ripple_effect = 0x7f08015d
com.mohandhass28.customer:color/material_dynamic_tertiary20 = 0x7f0601f9
com.mohandhass28.customer:dimen/abc_select_dialog_padding_start_material = 0x7f07003a
com.mohandhass28.customer:id/snackbar_action = 0x7f0a0212
com.mohandhass28.customer:color/material_dynamic_neutral10 = 0x7f0601c3
com.mohandhass28.customer:color/material_dynamic_tertiary100 = 0x7f0601f8
com.mohandhass28.customer:color/material_dynamic_secondary99 = 0x7f0601f5
com.mohandhass28.customer:color/material_dynamic_secondary80 = 0x7f0601f2
com.mohandhass28.customer:id/cancel_action = 0x7f0a007f
com.mohandhass28.customer:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f130427
com.mohandhass28.customer:attr/checkMarkTintMode = 0x7f0400a6
com.mohandhass28.customer:color/material_dynamic_secondary70 = 0x7f0601f1
com.mohandhass28.customer:color/material_dynamic_secondary50 = 0x7f0601ef
com.mohandhass28.customer:color/material_dynamic_secondary40 = 0x7f0601ee
com.mohandhass28.customer:style/Theme.SplashScreen.IconBackground = 0x7f1302aa
com.mohandhass28.customer:dimen/action_bar_size = 0x7f070051
com.mohandhass28.customer:color/material_dynamic_secondary10 = 0x7f0601ea
com.mohandhass28.customer:dimen/mtrl_low_ripple_hovered_alpha = 0x7f070209
com.mohandhass28.customer:attr/itemIconSize = 0x7f040239
com.mohandhass28.customer:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f070159
com.mohandhass28.customer:id/textinput_suffix_text = 0x7f0a0253
com.mohandhass28.customer:attr/uiCompass = 0x7f04047b
com.mohandhass28.customer:color/material_dynamic_secondary0 = 0x7f0601e9
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f060175
com.mohandhass28.customer:id/exo_prev = 0x7f0a00f8
com.mohandhass28.customer:color/material_dynamic_primary99 = 0x7f0601e8
com.mohandhass28.customer:styleable/NavigationBarView = 0x7f14006b
com.mohandhass28.customer:color/material_dynamic_primary10 = 0x7f0601dd
com.mohandhass28.customer:dimen/m3_large_fab_max_image_size = 0x7f070119
com.mohandhass28.customer:color/material_dynamic_primary95 = 0x7f0601e7
com.mohandhass28.customer:dimen/abc_dialog_fixed_height_minor = 0x7f07001d
com.mohandhass28.customer:color/material_dynamic_primary90 = 0x7f0601e6
com.mohandhass28.customer:color/material_dynamic_primary80 = 0x7f0601e5
com.mohandhass28.customer:id/view_tag_native_id = 0x7f0a0272
com.mohandhass28.customer:dimen/material_textinput_default_width = 0x7f070187
com.mohandhass28.customer:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f130388
com.mohandhass28.customer:dimen/splashscreen_icon_size_with_background = 0x7f070264
com.mohandhass28.customer:color/tooltip_background_light = 0x7f060273
com.mohandhass28.customer:style/Base.Widget.MaterialComponents.Slider = 0x7f13010e
com.mohandhass28.customer:interpolator/mtrl_linear_out_slow_in = 0x7f0c000a
com.mohandhass28.customer:color/material_dynamic_primary70 = 0x7f0601e4
com.mohandhass28.customer:color/material_dynamic_primary50 = 0x7f0601e2
com.mohandhass28.customer:color/material_dynamic_primary30 = 0x7f0601e0
com.mohandhass28.customer:color/material_dynamic_primary20 = 0x7f0601df
com.mohandhass28.customer:color/material_dynamic_neutral_variant90 = 0x7f0601d9
com.mohandhass28.customer:id/postLayout = 0x7f0a01cb
com.mohandhass28.customer:color/material_dynamic_neutral_variant80 = 0x7f0601d8
com.mohandhass28.customer:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f070160
com.mohandhass28.customer:id/accessibility_custom_action_20 = 0x7f0a0023
com.mohandhass28.customer:color/material_dynamic_neutral_variant30 = 0x7f0601d3
com.mohandhass28.customer:attr/contentPaddingLeft = 0x7f04011b
com.mohandhass28.customer:attr/layoutManager = 0x7f040261
com.mohandhass28.customer:color/material_dynamic_neutral_variant100 = 0x7f0601d1
com.mohandhass28.customer:color/material_dynamic_neutral_variant10 = 0x7f0601d0
com.mohandhass28.customer:color/material_dynamic_neutral_variant0 = 0x7f0601cf
com.mohandhass28.customer:dimen/abc_control_inset_material = 0x7f070019
com.mohandhass28.customer:color/m3_calendar_item_stroke_color = 0x7f060086
com.mohandhass28.customer:color/material_dynamic_neutral90 = 0x7f0601cc
com.mohandhass28.customer:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1302bb
com.mohandhass28.customer:color/material_dynamic_neutral80 = 0x7f0601cb
com.mohandhass28.customer:color/material_dynamic_neutral40 = 0x7f0601c7
com.mohandhass28.customer:style/Widget.MaterialComponents.Chip.Action = 0x7f13041a
com.mohandhass28.customer:string/m3_sys_motion_easing_standard = 0x7f1200d4
com.mohandhass28.customer:id/exo_center_controls = 0x7f0a00de
com.mohandhass28.customer:color/material_dynamic_neutral30 = 0x7f0601c6
com.mohandhass28.customer:id/noScroll = 0x7f0a01ad
com.mohandhass28.customer:attr/cornerRadius = 0x7f040129
com.mohandhass28.customer:color/m3_ref_palette_secondary95 = 0x7f06012e
com.mohandhass28.customer:color/material_dynamic_neutral100 = 0x7f0601c4
com.mohandhass28.customer:dimen/m3_navigation_item_shape_inset_bottom = 0x7f07011f
com.mohandhass28.customer:color/material_deep_teal_500 = 0x7f0601c0
com.mohandhass28.customer:color/material_dynamic_neutral_variant40 = 0x7f0601d4
com.mohandhass28.customer:attr/scrubber_dragged_size = 0x7f040389
com.mohandhass28.customer:color/material_deep_teal_200 = 0x7f0601bf
com.mohandhass28.customer:style/EmptyTheme = 0x7f130120
com.mohandhass28.customer:color/m3_ref_palette_black = 0x7f0600ad
com.mohandhass28.customer:color/material_blue_grey_900 = 0x7f0601bc
com.mohandhass28.customer:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f0601b8
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1301d7
com.mohandhass28.customer:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f08000b
com.mohandhass28.customer:color/m3_timepicker_display_background_color = 0x7f0601b4
com.mohandhass28.customer:attr/tickColorActive = 0x7f040444
com.mohandhass28.customer:color/m3_timepicker_button_text_color = 0x7f0601b2
com.mohandhass28.customer:attr/drawPath = 0x7f040184
com.mohandhass28.customer:color/m3_textfield_stroke_color = 0x7f0601af
com.mohandhass28.customer:id/ll_loader = 0x7f0a0166
com.mohandhass28.customer:dimen/m3_card_hovered_z = 0x7f070104
com.mohandhass28.customer:color/m3_textfield_label_color = 0x7f0601ae
com.mohandhass28.customer:color/m3_textfield_input_text_color = 0x7f0601ad
com.mohandhass28.customer:styleable/ChipGroup = 0x7f14001f
com.mohandhass28.customer:color/m3_textfield_filled_background_color = 0x7f0601ab
com.mohandhass28.customer:drawable/test_level_drawable = 0x7f08016b
com.mohandhass28.customer:color/m3_text_button_foreground_color_selector = 0x7f0601a9
com.mohandhass28.customer:attr/failureImageScaleType = 0x7f0401ca
com.mohandhass28.customer:color/m3_ref_palette_secondary100 = 0x7f060125
com.mohandhass28.customer:color/m3_tabs_ripple_color = 0x7f0601a7
com.mohandhass28.customer:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f1300ff
com.mohandhass28.customer:string/catalyst_debug_error = 0x7f12002b
com.mohandhass28.customer:attr/seekBarStyle = 0x7f04038f
com.mohandhass28.customer:color/m3_tabs_icon_color = 0x7f0601a6
com.mohandhass28.customer:attr/flow_firstHorizontalBias = 0x7f0401db
com.mohandhass28.customer:dimen/m3_extended_fab_top_padding = 0x7f070114
com.mohandhass28.customer:attr/flow_firstVerticalBias = 0x7f0401dd
com.mohandhass28.customer:color/m3_sys_color_light_tertiary_container = 0x7f0601a5
com.mohandhass28.customer:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f130330
com.mohandhass28.customer:style/ThemeOverlay.Material3.Button.TextButton = 0x7f1302c0
com.mohandhass28.customer:attr/layout_constraintTop_toBottomOf = 0x7f040288
com.mohandhass28.customer:color/m3_sys_color_light_tertiary = 0x7f0601a4
com.mohandhass28.customer:drawable/exo_styled_controls_overflow_show = 0x7f0800f1
com.mohandhass28.customer:color/m3_sys_color_light_surface_variant = 0x7f0601a3
com.mohandhass28.customer:dimen/mtrl_slider_thumb_radius = 0x7f070233
com.mohandhass28.customer:color/m3_sys_color_light_secondary = 0x7f0601a0
com.mohandhass28.customer:color/m3_sys_color_light_primary_container = 0x7f06019f
com.mohandhass28.customer:color/bright_foreground_disabled_material_dark = 0x7f060022
com.mohandhass28.customer:color/material_dynamic_neutral95 = 0x7f0601cd
com.mohandhass28.customer:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f130167
com.mohandhass28.customer:attr/contentPaddingTop = 0x7f04011e
com.mohandhass28.customer:id/exo_overflow_hide = 0x7f0a00f0
com.mohandhass28.customer:color/m3_sys_color_light_primary = 0x7f06019e
com.mohandhass28.customer:dimen/appcompat_dialog_background_inset = 0x7f070052
com.mohandhass28.customer:attr/flow_verticalBias = 0x7f0401ea
com.mohandhass28.customer:color/m3_sys_color_light_on_tertiary_container = 0x7f06019c
com.mohandhass28.customer:color/m3_sys_color_light_on_tertiary = 0x7f06019b
com.mohandhass28.customer:string/catalyst_reload_error = 0x7f12003d
com.mohandhass28.customer:attr/statusBarForeground = 0x7f0403cc
com.mohandhass28.customer:color/m3_sys_color_light_on_secondary_container = 0x7f060198
com.mohandhass28.customer:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0701f3
com.mohandhass28.customer:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f070153
com.mohandhass28.customer:string/rn_tab_description = 0x7f120121
com.mohandhass28.customer:attr/itemRippleColor = 0x7f040240
com.mohandhass28.customer:color/m3_sys_color_light_on_secondary = 0x7f060197
com.mohandhass28.customer:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f130207
com.mohandhass28.customer:color/m3_sys_color_light_on_primary_container = 0x7f060196
com.mohandhass28.customer:attr/colorControlNormal = 0x7f0400e8
com.mohandhass28.customer:attr/toolbarId = 0x7f04045b
com.mohandhass28.customer:color/m3_sys_color_light_on_primary = 0x7f060195
com.mohandhass28.customer:color/m3_sys_color_light_inverse_surface = 0x7f060191
com.mohandhass28.customer:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f130337
com.mohandhass28.customer:color/m3_sys_color_light_secondary_container = 0x7f0601a1
com.mohandhass28.customer:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f1302c7
com.mohandhass28.customer:attr/cardMaxElevation = 0x7f04009e
com.mohandhass28.customer:color/m3_sys_color_light_inverse_on_surface = 0x7f06018f
com.mohandhass28.customer:attr/simpleItems = 0x7f0403ae
com.mohandhass28.customer:color/mtrl_popupmenu_overlay_color = 0x7f060249
com.mohandhass28.customer:color/m3_sys_color_light_error_container = 0x7f06018e
com.mohandhass28.customer:color/mtrl_tabs_icon_color_selector = 0x7f06024c
com.mohandhass28.customer:attr/counterEnabled = 0x7f040130
com.mohandhass28.customer:drawable/rzp_green_button = 0x7f080162
com.mohandhass28.customer:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f130079
com.mohandhass28.customer:color/m3_timepicker_button_ripple_color = 0x7f0601b1
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_tertiary = 0x7f06018a
com.mohandhass28.customer:attr/customThemeStyle = 0x7f040169
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_surface_variant = 0x7f060189
com.mohandhass28.customer:style/Base.V7.Widget.AppCompat.EditText = 0x7f1300b6
com.mohandhass28.customer:string/google_crash_reporting_api_key = 0x7f1200b6
com.mohandhass28.customer:id/counterclockwise = 0x7f0a00a4
com.mohandhass28.customer:color/m3_tonal_button_ripple_color_selector = 0x7f0601ba
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_surface = 0x7f060188
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_secondary_container = 0x7f060187
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_secondary = 0x7f060186
com.mohandhass28.customer:id/iv_check_mark = 0x7f0a0158
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f060181
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_on_secondary = 0x7f06017d
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f06017c
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_on_primary = 0x7f06017b
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_on_background = 0x7f06017a
com.mohandhass28.customer:string/catalyst_copy_button = 0x7f120029
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f060179
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f060177
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_background = 0x7f060176
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1301e9
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_tertiary = 0x7f060174
com.mohandhass28.customer:id/arc = 0x7f0a0060
com.mohandhass28.customer:string/common_google_play_services_unsupported_text = 0x7f120053
com.mohandhass28.customer:layout/material_textinput_timepicker = 0x7f0d0051
com.mohandhass28.customer:id/month_navigation_fragment_toggle = 0x7f0a0188
com.mohandhass28.customer:color/ripple_material_light = 0x7f060263
com.mohandhass28.customer:attr/autofillInlineSuggestionChip = 0x7f040045
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f060173
com.mohandhass28.customer:drawable/exo_styled_controls_settings = 0x7f0800f9
com.mohandhass28.customer:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0700cd
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_secondary = 0x7f060170
com.mohandhass28.customer:string/catalyst_hot_reloading_stop = 0x7f120035
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_primary = 0x7f06016e
com.mohandhass28.customer:attr/homeLayout = 0x7f040215
com.mohandhass28.customer:attr/layout_constraintRight_toLeftOf = 0x7f040282
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_outline = 0x7f06016d
com.mohandhass28.customer:id/fixed = 0x7f0a0120
com.mohandhass28.customer:drawable/abc_btn_colored_material = 0x7f08000d
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f06016b
com.mohandhass28.customer:color/design_dark_default_color_primary_variant = 0x7f06004f
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_on_surface = 0x7f060169
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f060163
com.mohandhass28.customer:drawable/design_ic_visibility = 0x7f0800ad
com.mohandhass28.customer:color/m3_sys_color_dark_tertiary_container = 0x7f06015f
com.mohandhass28.customer:id/browser_actions_menu_item_text = 0x7f0a0075
com.mohandhass28.customer:color/m3_sys_color_dark_tertiary = 0x7f06015e
com.mohandhass28.customer:color/material_dynamic_tertiary95 = 0x7f060201
com.mohandhass28.customer:color/m3_sys_color_dark_surface_variant = 0x7f06015d
com.mohandhass28.customer:attr/telltales_tailColor = 0x7f040402
com.mohandhass28.customer:color/m3_ref_palette_dynamic_primary70 = 0x7f0600d0
com.mohandhass28.customer:color/m3_sys_color_dark_secondary_container = 0x7f06015b
com.mohandhass28.customer:color/m3_sys_color_dark_primary_container = 0x7f060159
com.mohandhass28.customer:color/m3_sys_color_dark_outline = 0x7f060157
com.mohandhass28.customer:drawable/notification_bg_low = 0x7f08014f
com.mohandhass28.customer:color/mtrl_navigation_bar_colored_ripple_color = 0x7f06023f
com.mohandhass28.customer:string/abc_menu_sym_shortcut_label = 0x7f120010
com.mohandhass28.customer:color/m3_sys_color_dark_on_tertiary_container = 0x7f060156
com.mohandhass28.customer:color/m3_sys_color_dark_on_tertiary = 0x7f060155
com.mohandhass28.customer:dimen/m3_bottom_nav_item_padding_top = 0x7f0700df
com.mohandhass28.customer:color/m3_sys_color_dark_on_surface = 0x7f060153
com.mohandhass28.customer:styleable/BottomAppBar = 0x7f140017
com.mohandhass28.customer:id/accessibility_custom_action_9 = 0x7f0a0035
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f130042
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f060166
com.mohandhass28.customer:color/m3_sys_color_dark_on_primary_container = 0x7f060150
com.mohandhass28.customer:color/m3_sys_color_dark_on_background = 0x7f06014c
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f130029
com.mohandhass28.customer:color/m3_sys_color_dark_inverse_surface = 0x7f06014b
com.mohandhass28.customer:style/Theme.Material3.Dark.Dialog = 0x7f13025e
com.mohandhass28.customer:color/m3_sys_color_dark_inverse_on_surface = 0x7f060149
com.mohandhass28.customer:drawable/abc_vector_test = 0x7f080054
com.mohandhass28.customer:color/m3_sys_color_dark_error_container = 0x7f060148
com.mohandhass28.customer:color/m3_sys_color_dark_background = 0x7f060146
com.mohandhass28.customer:dimen/mtrl_calendar_year_vertical_padding = 0x7f0701e0
com.mohandhass28.customer:color/m3_switch_track_tint = 0x7f060145
com.mohandhass28.customer:string/mtrl_picker_invalid_range = 0x7f1200fe
com.mohandhass28.customer:dimen/m3_card_elevation = 0x7f070103
com.mohandhass28.customer:color/m3_slider_inactive_track_color = 0x7f060142
com.mohandhass28.customer:id/spread_inside = 0x7f0a021d
com.mohandhass28.customer:attr/elevation = 0x7f040198
com.mohandhass28.customer:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0700db
com.mohandhass28.customer:color/m3_slider_halo_color = 0x7f060141
com.mohandhass28.customer:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1303d5
com.mohandhass28.customer:drawable/design_password_eye = 0x7f0800af
com.mohandhass28.customer:attr/colorSurface = 0x7f040104
com.mohandhass28.customer:color/m3_slider_active_track_color = 0x7f060140
com.mohandhass28.customer:color/m3_selection_control_ripple_color_selector = 0x7f06013f
com.mohandhass28.customer:color/m3_ref_palette_white = 0x7f06013d
com.mohandhass28.customer:color/m3_ref_palette_tertiary90 = 0x7f06013a
com.mohandhass28.customer:color/m3_ref_palette_tertiary20 = 0x7f060133
com.mohandhass28.customer:style/Platform.MaterialComponents = 0x7f130159
com.mohandhass28.customer:color/material_dynamic_tertiary0 = 0x7f0601f6
com.mohandhass28.customer:drawable/notification_tile_bg = 0x7f080158
com.mohandhass28.customer:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f07019e
com.mohandhass28.customer:color/m3_ref_palette_tertiary0 = 0x7f060130
com.mohandhass28.customer:id/exo_progress = 0x7f0a00f9
com.mohandhass28.customer:color/m3_ref_palette_secondary99 = 0x7f06012f
com.mohandhass28.customer:string/call_notification_decline_action = 0x7f120023
com.mohandhass28.customer:string/abc_action_mode_done = 0x7f120003
com.mohandhass28.customer:drawable/ic_m3_chip_check = 0x7f080111
com.mohandhass28.customer:color/m3_ref_palette_secondary90 = 0x7f06012d
com.mohandhass28.customer:color/m3_ref_palette_secondary80 = 0x7f06012c
com.mohandhass28.customer:attr/transitionEasing = 0x7f040473
com.mohandhass28.customer:color/material_slider_thumb_color = 0x7f06021d
com.mohandhass28.customer:dimen/design_snackbar_max_width = 0x7f070088
com.mohandhass28.customer:color/m3_ref_palette_secondary60 = 0x7f06012a
com.mohandhass28.customer:id/accessibility_custom_action_16 = 0x7f0a001e
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0600c3
com.mohandhass28.customer:color/m3_slider_thumb_color = 0x7f060143
com.mohandhass28.customer:color/m3_ref_palette_secondary50 = 0x7f060129
com.mohandhass28.customer:color/m3_ref_palette_secondary30 = 0x7f060127
com.mohandhass28.customer:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f080036
com.mohandhass28.customer:attr/arcMode = 0x7f040039
com.mohandhass28.customer:color/background_floating_material_light = 0x7f06001e
com.mohandhass28.customer:dimen/mtrl_fab_min_touch_target = 0x7f0701ff
com.mohandhass28.customer:color/error_color_material_dark = 0x7f06006e
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f130309
com.mohandhass28.customer:color/m3_ref_palette_primary99 = 0x7f060122
com.mohandhass28.customer:bool/isTablet = 0x7f050005
com.mohandhass28.customer:color/m3_ref_palette_primary90 = 0x7f060120
com.mohandhass28.customer:color/m3_ref_palette_primary30 = 0x7f06011a
com.mohandhass28.customer:style/ExoStyledControls.Button.Bottom.FullScreen = 0x7f13012e
com.mohandhass28.customer:drawable/common_google_signin_btn_text_light_focused = 0x7f0800a7
com.mohandhass28.customer:style/Widget.MaterialComponents.FloatingActionButton = 0x7f130429
com.mohandhass28.customer:color/design_bottom_navigation_shadow_color = 0x7f060044
com.mohandhass28.customer:color/m3_ref_palette_primary10 = 0x7f060117
com.mohandhass28.customer:anim/rns_slide_out_to_right = 0x7f01003f
com.mohandhass28.customer:xml/standalone_badge_gravity_top_start = 0x7f15000d
com.mohandhass28.customer:attr/cropSnapRadius = 0x7f040158
com.mohandhass28.customer:color/m3_ref_palette_primary0 = 0x7f060116
com.mohandhass28.customer:dimen/material_emphasis_high_type = 0x7f070177
com.mohandhass28.customer:color/material_on_background_emphasis_high_type = 0x7f06020f
com.mohandhass28.customer:attr/barrierDirection = 0x7f040060
com.mohandhass28.customer:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f130172
com.mohandhass28.customer:color/m3_ref_palette_neutral_variant90 = 0x7f060113
com.mohandhass28.customer:color/m3_ref_palette_neutral_variant70 = 0x7f060111
com.mohandhass28.customer:string/mtrl_picker_toggle_to_day_selection = 0x7f12010e
com.mohandhass28.customer:attr/dayTodayStyle = 0x7f04016d
com.mohandhass28.customer:color/switch_thumb_disabled_material_light = 0x7f06026a
com.mohandhass28.customer:color/m3_ref_palette_neutral_variant50 = 0x7f06010f
com.mohandhass28.customer:style/Widget.AppCompat.Button = 0x7f13031e
com.mohandhass28.customer:id/image = 0x7f0a014f
com.mohandhass28.customer:color/m3_ref_palette_neutral_variant40 = 0x7f06010e
com.mohandhass28.customer:color/m3_button_ripple_color_selector = 0x7f060084
com.mohandhass28.customer:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f07019b
com.mohandhass28.customer:dimen/abc_text_size_medium_material = 0x7f070049
com.mohandhass28.customer:color/m3_ref_palette_neutral_variant20 = 0x7f06010c
com.mohandhass28.customer:string/toolbar_description = 0x7f120137
com.mohandhass28.customer:color/m3_ref_palette_neutral99 = 0x7f060108
com.mohandhass28.customer:color/material_dynamic_primary60 = 0x7f0601e3
com.mohandhass28.customer:attr/daySelectedStyle = 0x7f04016b
com.mohandhass28.customer:color/m3_ref_palette_neutral95 = 0x7f060107
com.mohandhass28.customer:id/consume_window_insets_tag = 0x7f0a009d
com.mohandhass28.customer:string/exo_download_downloading = 0x7f120085
com.mohandhass28.customer:color/m3_ref_palette_neutral90 = 0x7f060106
com.mohandhass28.customer:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f1303f7
com.mohandhass28.customer:color/m3_ref_palette_neutral60 = 0x7f060103
com.mohandhass28.customer:color/m3_sys_color_dark_surface = 0x7f06015c
com.mohandhass28.customer:dimen/design_navigation_separator_vertical_padding = 0x7f070082
com.mohandhass28.customer:color/m3_ref_palette_neutral40 = 0x7f060101
com.mohandhass28.customer:style/Widget.Design.TextInputLayout = 0x7f13036e
com.mohandhass28.customer:dimen/exo_icon_padding_bottom = 0x7f07009c
com.mohandhass28.customer:attr/contentInsetEnd = 0x7f040112
com.mohandhass28.customer:color/m3_ref_palette_error80 = 0x7f0600f8
com.mohandhass28.customer:color/m3_ref_palette_error70 = 0x7f0600f7
com.mohandhass28.customer:drawable/assets_resimages_images_9 = 0x7f080081
com.mohandhass28.customer:style/TextAppearance.Test.UsesSP = 0x7f130233
com.mohandhass28.customer:id/navigation_bar_item_large_label_view = 0x7f0a01a9
com.mohandhass28.customer:attr/cropFlipHorizontally = 0x7f040144
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f130060
com.mohandhass28.customer:attr/flow_lastVerticalStyle = 0x7f0401e6
com.mohandhass28.customer:color/m3_ref_palette_error30 = 0x7f0600f3
com.mohandhass28.customer:attr/layout_constraintEnd_toEndOf = 0x7f040272
com.mohandhass28.customer:drawable/node_modules_exporouter_assets_file = 0x7f08013b
com.mohandhass28.customer:color/m3_ref_palette_error20 = 0x7f0600f2
com.mohandhass28.customer:attr/spinBars = 0x7f0403b8
com.mohandhass28.customer:attr/fontProviderFetchTimeout = 0x7f0401f3
com.mohandhass28.customer:color/m3_ref_palette_error0 = 0x7f0600ef
com.mohandhass28.customer:id/android_pay_dark = 0x7f0a005a
com.mohandhass28.customer:integer/mtrl_card_anim_delay_ms = 0x7f0b0030
com.mohandhass28.customer:drawable/abc_tab_indicator_mtrl_alpha = 0x7f08004a
com.mohandhass28.customer:color/m3_ref_palette_dynamic_tertiary99 = 0x7f0600ee
com.mohandhass28.customer:style/Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f1301af
com.mohandhass28.customer:color/accent_material_dark = 0x7f060019
com.mohandhass28.customer:color/m3_ref_palette_dynamic_tertiary80 = 0x7f0600eb
com.mohandhass28.customer:styleable/CircularProgressIndicator = 0x7f140020
com.mohandhass28.customer:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f13028d
com.mohandhass28.customer:color/m3_ref_palette_dynamic_tertiary70 = 0x7f0600ea
com.mohandhass28.customer:color/m3_ref_palette_dynamic_tertiary50 = 0x7f0600e8
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f060182
com.mohandhass28.customer:id/donate_with_google = 0x7f0a00bf
com.mohandhass28.customer:string/mtrl_picker_toggle_to_year_selection = 0x7f120110
com.mohandhass28.customer:color/abc_secondary_text_material_dark = 0x7f060011
com.mohandhass28.customer:attr/scrimAnimationDuration = 0x7f040384
com.mohandhass28.customer:color/design_dark_default_color_on_surface = 0x7f06004c
com.mohandhass28.customer:color/m3_ref_palette_dynamic_tertiary40 = 0x7f0600e7
com.mohandhass28.customer:id/clip_vertical = 0x7f0a0096
com.mohandhass28.customer:id/all = 0x7f0a0056
com.mohandhass28.customer:styleable/ShapeableImageView = 0x7f14007e
com.mohandhass28.customer:color/m3_ref_palette_dynamic_tertiary100 = 0x7f0600e4
com.mohandhass28.customer:color/m3_ref_palette_dynamic_secondary70 = 0x7f0600dd
com.mohandhass28.customer:attr/badgeStyle = 0x7f040058
com.mohandhass28.customer:anim/catalyst_push_up_in = 0x7f01001a
com.mohandhass28.customer:color/m3_ref_palette_dynamic_secondary60 = 0x7f0600dc
com.mohandhass28.customer:dimen/mtrl_calendar_header_height = 0x7f0701c8
com.mohandhass28.customer:color/m3_ref_palette_dynamic_secondary10 = 0x7f0600d6
com.mohandhass28.customer:drawable/notification_template_icon_bg = 0x7f080156
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1302ed
com.mohandhass28.customer:color/m3_ref_palette_dynamic_primary99 = 0x7f0600d4
com.mohandhass28.customer:id/material = 0x7f0a016a
com.mohandhass28.customer:attr/itemShapeInsetTop = 0x7f040247
com.mohandhass28.customer:attr/menuGravity = 0x7f0402f7
com.mohandhass28.customer:attr/region_widthMoreThan = 0x7f04036a
com.mohandhass28.customer:color/m3_ref_palette_dynamic_primary95 = 0x7f0600d3
com.mohandhass28.customer:attr/layout_anchor = 0x7f040262
com.mohandhass28.customer:color/splashscreen_background = 0x7f060268
com.mohandhass28.customer:attr/liftOnScrollTargetViewId = 0x7f0402a1
com.mohandhass28.customer:color/m3_ref_palette_dynamic_primary20 = 0x7f0600cb
com.mohandhass28.customer:dimen/tooltip_corner_radius = 0x7f070272
com.mohandhass28.customer:dimen/m3_chip_checked_hovered_translation_z = 0x7f070106
com.mohandhass28.customer:styleable/ActionMode = 0x7f140004
com.mohandhass28.customer:attr/actionModeBackground = 0x7f040010
com.mohandhass28.customer:attr/percentWidth = 0x7f040340
com.mohandhass28.customer:color/m3_ref_palette_dynamic_primary10 = 0x7f0600c9
com.mohandhass28.customer:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f13036a
com.mohandhass28.customer:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f07014c
com.mohandhass28.customer:drawable/fingerprint_dialog_fp_icon = 0x7f080101
com.mohandhass28.customer:font/roboto_medium_numbers = 0x7f090000
com.mohandhass28.customer:style/Platform.AppCompat = 0x7f130157
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0600bb
com.mohandhass28.customer:color/abc_search_url_text_pressed = 0x7f06000f
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral99 = 0x7f0600ba
com.mohandhass28.customer:dimen/abc_button_padding_horizontal_material = 0x7f070014
com.mohandhass28.customer:style/Widget.AppCompat.Spinner.DropDown = 0x7f130355
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral80 = 0x7f0600b7
com.mohandhass28.customer:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f070155
com.mohandhass28.customer:color/design_default_color_on_secondary = 0x7f060058
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral70 = 0x7f0600b6
com.mohandhass28.customer:id/material_timepicker_cancel_button = 0x7f0a0177
com.mohandhass28.customer:color/m3_timepicker_clock_text_color = 0x7f0601b3
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral60 = 0x7f0600b5
com.mohandhass28.customer:attr/materialCardViewElevatedStyle = 0x7f0402df
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral40 = 0x7f0600b3
com.mohandhass28.customer:attr/colorPrimary = 0x7f0400fa
com.mohandhass28.customer:color/m3_popupmenu_overlay_color = 0x7f0600aa
com.mohandhass28.customer:animator/design_fab_show_motion_spec = 0x7f020002
com.mohandhass28.customer:color/m3_navigation_item_icon_tint = 0x7f0600a7
com.mohandhass28.customer:color/mtrl_fab_bg_color_selector = 0x7f060237
com.mohandhass28.customer:id/skip_previous_button = 0x7f0a0210
com.mohandhass28.customer:color/m3_ref_palette_neutral100 = 0x7f0600fe
com.mohandhass28.customer:color/m3_navigation_item_background_color = 0x7f0600a6
com.mohandhass28.customer:layout/custom_dialog = 0x7f0d0024
com.mohandhass28.customer:color/m3_navigation_bar_ripple_color_selector = 0x7f0600a5
com.mohandhass28.customer:attr/cornerFamilyBottomLeft = 0x7f040125
com.mohandhass28.customer:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f0600a4
com.mohandhass28.customer:color/m3_dynamic_dark_primary_text_disable_only = 0x7f06009a
com.mohandhass28.customer:drawable/exo_ic_check = 0x7f0800c1
com.mohandhass28.customer:integer/mtrl_calendar_selection_text_lines = 0x7f0b002e
com.mohandhass28.customer:id/dropdown_noneditable = 0x7f0a00c8
com.mohandhass28.customer:color/m3_default_color_primary_text = 0x7f060094
com.mohandhass28.customer:color/m3_dark_hint_foreground = 0x7f060092
com.mohandhass28.customer:dimen/m3_appbar_scrim_height_trigger = 0x7f0700cf
com.mohandhass28.customer:color/design_dark_default_color_primary_dark = 0x7f06004e
com.mohandhass28.customer:color/m3_dark_default_color_secondary_text = 0x7f060090
com.mohandhass28.customer:string/exo_track_selection_title_text = 0x7f120097
com.mohandhass28.customer:id/dropdown_editable = 0x7f0a00c6
com.mohandhass28.customer:dimen/notification_content_margin_start = 0x7f070252
com.mohandhass28.customer:style/Widget.MaterialComponents.ShapeableImageView = 0x7f130450
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f1303bd
com.mohandhass28.customer:color/m3_chip_text_color = 0x7f06008e
com.mohandhass28.customer:color/m3_sys_color_light_on_surface_variant = 0x7f06019a
com.mohandhass28.customer:dimen/mtrl_min_touch_target_size = 0x7f07020b
com.mohandhass28.customer:attr/suffixText = 0x7f0403db
com.mohandhass28.customer:color/m3_ref_palette_error40 = 0x7f0600f4
com.mohandhass28.customer:string/not_selected = 0x7f120113
com.mohandhass28.customer:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f08012d
com.mohandhass28.customer:dimen/m3_extended_fab_end_padding = 0x7f070110
com.mohandhass28.customer:style/Theme.Design.BottomSheetDialog = 0x7f130254
com.mohandhass28.customer:color/material_dynamic_neutral_variant70 = 0x7f0601d7
com.mohandhass28.customer:attr/switchTextAppearance = 0x7f0403e4
com.mohandhass28.customer:color/m3_chip_ripple_color = 0x7f06008c
com.mohandhass28.customer:dimen/mtrl_btn_focused_z = 0x7f0701a5
com.mohandhass28.customer:dimen/exo_settings_height = 0x7f0700a2
com.mohandhass28.customer:attr/cropShowProgressBar = 0x7f040157
com.mohandhass28.customer:color/m3_button_outline_color_selector = 0x7f060082
com.mohandhass28.customer:color/m3_button_foreground_color_selector = 0x7f060081
com.mohandhass28.customer:color/m3_ref_palette_dynamic_secondary99 = 0x7f0600e1
com.mohandhass28.customer:string/exo_controls_overflow_show_description = 0x7f120072
com.mohandhass28.customer:color/m3_assist_chip_icon_tint_color = 0x7f06007e
com.mohandhass28.customer:id/spread = 0x7f0a021c
com.mohandhass28.customer:color/highlighted_text_material_light = 0x7f06007b
com.mohandhass28.customer:id/search_plate = 0x7f0a0201
com.mohandhass28.customer:attr/bottomAppBarStyle = 0x7f04006f
com.mohandhass28.customer:attr/listItemLayout = 0x7f0402aa
com.mohandhass28.customer:color/highlighted_text_material_dark = 0x7f06007a
com.mohandhass28.customer:id/accessibility_links = 0x7f0a0038
com.mohandhass28.customer:color/foreground_material_dark = 0x7f060078
com.mohandhass28.customer:attr/uiZoomControls = 0x7f040481
com.mohandhass28.customer:attr/fastScrollEnabled = 0x7f0401cb
com.mohandhass28.customer:color/exo_edit_mode_background_color = 0x7f060073
com.mohandhass28.customer:attr/tabIndicatorHeight = 0x7f0403f0
com.mohandhass28.customer:integer/default_icon_animation_duration = 0x7f0b0006
com.mohandhass28.customer:color/exo_bottom_bar_background = 0x7f060072
com.mohandhass28.customer:attr/chipSpacingHorizontal = 0x7f0400be
com.mohandhass28.customer:color/exo_black_opacity_60 = 0x7f060070
com.mohandhass28.customer:style/ShapeAppearance.MaterialComponents.Test = 0x7f130192
com.mohandhass28.customer:attr/fontStyle = 0x7f0401f7
com.mohandhass28.customer:attr/behavior_autoShrink = 0x7f040063
com.mohandhass28.customer:drawable/common_google_signin_btn_icon_light = 0x7f08009d
com.mohandhass28.customer:attr/auto_show = 0x7f040044
com.mohandhass28.customer:id/chip = 0x7f0a008c
com.mohandhass28.customer:color/m3_ref_palette_dynamic_primary0 = 0x7f0600c8
com.mohandhass28.customer:styleable/Motion = 0x7f140065
com.mohandhass28.customer:styleable/KeyCycle = 0x7f140043
com.mohandhass28.customer:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f13007c
com.mohandhass28.customer:color/dim_foreground_material_dark = 0x7f06006c
com.mohandhass28.customer:color/design_fab_stroke_top_outer_color = 0x7f060067
com.mohandhass28.customer:attr/marginTopSystemWindowInsets = 0x7f0402bf
com.mohandhass28.customer:color/design_fab_stroke_end_inner_color = 0x7f060064
com.mohandhass28.customer:string/exo_controls_pause_description = 0x7f120073
com.mohandhass28.customer:attr/insetForeground = 0x7f04022e
com.mohandhass28.customer:attr/actionBarSplitStyle = 0x7f040004
com.mohandhass28.customer:attr/maxActionInlineWidth = 0x7f0402ed
com.mohandhass28.customer:color/design_default_color_secondary_variant = 0x7f06005e
com.mohandhass28.customer:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1300c5
com.mohandhass28.customer:attr/ratingBarStyleIndicator = 0x7f040364
com.mohandhass28.customer:color/design_default_color_primary_variant = 0x7f06005c
com.mohandhass28.customer:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f130183
com.mohandhass28.customer:color/design_default_color_on_surface = 0x7f060059
com.mohandhass28.customer:id/media_actions = 0x7f0a017f
com.mohandhass28.customer:dimen/test_navigation_bar_active_item_max_width = 0x7f070267
com.mohandhass28.customer:color/m3_highlighted_text = 0x7f0600a1
com.mohandhass28.customer:color/button_material_light = 0x7f06002d
com.mohandhass28.customer:drawable/material_ic_clear_black_24dp = 0x7f080127
com.mohandhass28.customer:style/Widget.Material3.Tooltip = 0x7f1303f6
com.mohandhass28.customer:color/design_default_color_background = 0x7f060053
com.mohandhass28.customer:color/m3_ref_palette_neutral_variant100 = 0x7f06010b
com.mohandhass28.customer:attr/haloRadius = 0x7f040203
com.mohandhass28.customer:attr/bar_gravity = 0x7f04005d
com.mohandhass28.customer:attr/popupWindowStyle = 0x7f040351
com.mohandhass28.customer:color/design_dark_default_color_surface = 0x7f060052
com.mohandhass28.customer:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f0701d6
com.mohandhass28.customer:drawable/m3_tabs_rounded_line_indicator = 0x7f080123
com.mohandhass28.customer:id/accessibility_custom_action_31 = 0x7f0a002f
com.mohandhass28.customer:attr/errorIconDrawable = 0x7f0401aa
com.mohandhass28.customer:attr/shapeAppearanceLargeComponent = 0x7f040395
com.mohandhass28.customer:drawable/assets_profileimgcopy_bannerimg = 0x7f080068
com.mohandhass28.customer:attr/buyButtonHeight = 0x7f040090
com.mohandhass28.customer:color/material_dynamic_secondary30 = 0x7f0601ed
com.mohandhass28.customer:styleable/MenuItem = 0x7f140062
com.mohandhass28.customer:color/design_dark_default_color_primary = 0x7f06004d
com.mohandhass28.customer:drawable/exo_ic_chevron_right = 0x7f0800c3
com.mohandhass28.customer:color/design_dark_default_color_on_error = 0x7f060049
com.mohandhass28.customer:drawable/exo_icon_circular_play = 0x7f0800d1
com.mohandhass28.customer:color/design_dark_default_color_error = 0x7f060047
com.mohandhass28.customer:id/tag_window_insets_animation_callback = 0x7f0a023d
com.mohandhass28.customer:attr/startIconTint = 0x7f0403c3
com.mohandhass28.customer:layout/abc_search_dropdown_item_icons_2line = 0x7f0d0018
com.mohandhass28.customer:id/group_divider = 0x7f0a0136
com.mohandhass28.customer:color/common_google_signin_btn_text_light_pressed = 0x7f060042
com.mohandhass28.customer:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1302d1
com.mohandhass28.customer:attr/editTextBackground = 0x7f040195
com.mohandhass28.customer:color/m3_ref_palette_dynamic_primary80 = 0x7f0600d1
com.mohandhass28.customer:color/common_google_signin_btn_text_light = 0x7f06003e
com.mohandhass28.customer:color/material_slider_inactive_tick_marks_color = 0x7f06021b
com.mohandhass28.customer:color/common_google_signin_btn_text_dark_focused = 0x7f06003c
com.mohandhass28.customer:color/primary_dark_material_light = 0x7f06025a
com.mohandhass28.customer:color/material_blue_grey_800 = 0x7f0601bb
com.mohandhass28.customer:attr/dialogTheme = 0x7f040177
com.mohandhass28.customer:color/checkbox_themeable_attribute_color = 0x7f060036
com.mohandhass28.customer:attr/titleMarginStart = 0x7f040454
com.mohandhass28.customer:color/catalyst_logbox_background = 0x7f060034
com.mohandhass28.customer:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f13046b
com.mohandhass28.customer:drawable/node_modules_reactnativeelementdropdown_src_assets_down = 0x7f080141
com.mohandhass28.customer:color/cardview_shadow_end_color = 0x7f060032
com.mohandhass28.customer:color/m3_timepicker_display_text_color = 0x7f0601b7
com.mohandhass28.customer:color/cardview_dark_background = 0x7f060030
com.mohandhass28.customer:attr/trackHeight = 0x7f04046e
com.mohandhass28.customer:color/call_notification_answer_color = 0x7f06002e
com.mohandhass28.customer:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f130100
com.mohandhass28.customer:drawable/exo_icon_repeat_all = 0x7f0800d9
com.mohandhass28.customer:dimen/design_fab_image_size = 0x7f070075
com.mohandhass28.customer:attr/cropAspectRatioX = 0x7f040136
com.mohandhass28.customer:color/browser_actions_bg_grey = 0x7f060028
com.mohandhass28.customer:attr/motionEasingEmphasized = 0x7f04030c
com.mohandhass28.customer:color/bright_foreground_material_light = 0x7f060027
com.mohandhass28.customer:styleable/SignInButton = 0x7f14007f
com.mohandhass28.customer:style/Theme.Catalyst.LogBox = 0x7f130251
com.mohandhass28.customer:color/bright_foreground_inverse_material_light = 0x7f060025
com.mohandhass28.customer:dimen/notification_large_icon_width = 0x7f070254
com.mohandhass28.customer:attr/roundTopLeft = 0x7f040379
com.mohandhass28.customer:color/background_material_light = 0x7f060020
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0600c7
com.mohandhass28.customer:attr/backgroundSplit = 0x7f040052
com.mohandhass28.customer:id/accessibility_custom_action_29 = 0x7f0a002c
com.mohandhass28.customer:attr/prefixTextColor = 0x7f040355
com.mohandhass28.customer:attr/shrinkMotionSpec = 0x7f0403ab
com.mohandhass28.customer:attr/layout_constraintWidth_default = 0x7f04028d
com.mohandhass28.customer:dimen/design_snackbar_min_width = 0x7f070089
com.mohandhass28.customer:dimen/design_snackbar_extra_spacing_horizontal = 0x7f070087
com.mohandhass28.customer:attr/controlBackground = 0x7f040121
com.mohandhass28.customer:color/mtrl_fab_ripple_color = 0x7f060239
com.mohandhass28.customer:color/abc_tint_switch_track = 0x7f060018
com.mohandhass28.customer:color/material_grey_100 = 0x7f060203
com.mohandhass28.customer:anim/abc_fade_in = 0x7f010000
com.mohandhass28.customer:dimen/notification_top_pad_large_text = 0x7f07025d
com.mohandhass28.customer:dimen/tooltip_margin = 0x7f070274
com.mohandhass28.customer:attr/flow_padding = 0x7f0401e8
com.mohandhass28.customer:color/abc_tint_spinner = 0x7f060017
com.mohandhass28.customer:style/Base.Widget.AppCompat.SearchView = 0x7f1300e8
com.mohandhass28.customer:drawable/exo_styled_controls_fullscreen_exit = 0x7f0800ee
com.mohandhass28.customer:attr/itemFillColor = 0x7f040235
com.mohandhass28.customer:attr/goIcon = 0x7f040201
com.mohandhass28.customer:color/abc_tint_btn_checkable = 0x7f060013
com.mohandhass28.customer:color/abc_primary_text_disable_only_material_light = 0x7f06000a
com.mohandhass28.customer:attr/roundBottomRight = 0x7f040375
com.mohandhass28.customer:style/WalletFragmentDefaultDetailsTextAppearance = 0x7f130311
com.mohandhass28.customer:attr/warmth = 0x7f040490
com.mohandhass28.customer:color/abc_hint_foreground_material_light = 0x7f060008
com.mohandhass28.customer:color/m3_ref_palette_dynamic_primary100 = 0x7f0600ca
com.mohandhass28.customer:dimen/design_bottom_sheet_peek_height_min = 0x7f070072
com.mohandhass28.customer:color/abc_decor_view_status_guard_light = 0x7f060006
com.mohandhass28.customer:attr/flow_verticalGap = 0x7f0401eb
com.mohandhass28.customer:color/abc_btn_colored_text_material = 0x7f060003
com.mohandhass28.customer:color/m3_ref_palette_error10 = 0x7f0600f0
com.mohandhass28.customer:color/design_box_stroke_color = 0x7f060045
com.mohandhass28.customer:attr/itemShapeAppearance = 0x7f040241
com.mohandhass28.customer:color/abc_background_cache_hint_selector_material_dark = 0x7f060000
com.mohandhass28.customer:style/TextAppearance.Compat.Notification.Title.Media = 0x7f1301f3
com.mohandhass28.customer:color/m3_ref_palette_neutral_variant80 = 0x7f060112
com.mohandhass28.customer:drawable/m3_tabs_transparent_background = 0x7f080124
com.mohandhass28.customer:attr/iconTintMode = 0x7f040220
com.mohandhass28.customer:color/m3_ref_palette_tertiary95 = 0x7f06013b
com.mohandhass28.customer:bool/mtrl_btn_textappearance_all_caps = 0x7f050006
com.mohandhass28.customer:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f07014a
com.mohandhass28.customer:bool/enable_system_alarm_service_default = 0x7f050002
com.mohandhass28.customer:bool/abc_config_actionMenuItemAllCaps = 0x7f050001
com.mohandhass28.customer:attr/yearSelectedStyle = 0x7f0404a5
com.mohandhass28.customer:attr/windowTransitionStyle = 0x7f0404a4
com.mohandhass28.customer:dimen/abc_text_size_body_2_material = 0x7f070040
com.mohandhass28.customer:animator/fragment_close_enter = 0x7f020003
com.mohandhass28.customer:attr/flow_horizontalBias = 0x7f0401e0
com.mohandhass28.customer:attr/windowSplashScreenIconBackgroundColor = 0x7f0404a3
com.mohandhass28.customer:dimen/splashscreen_icon_size_no_background = 0x7f070263
com.mohandhass28.customer:attr/windowSplashScreenAnimationDuration = 0x7f0404a1
com.mohandhass28.customer:anim/rns_slide_out_to_left = 0x7f01003e
com.mohandhass28.customer:drawable/common_google_signin_btn_text_dark = 0x7f0800a1
com.mohandhass28.customer:attr/windowSplashScreenAnimatedIcon = 0x7f0404a0
com.mohandhass28.customer:drawable/amu_bubble_shadow = 0x7f080056
com.mohandhass28.customer:style/ExoStyledControls.Button.Center.PlayPause = 0x7f130139
com.mohandhass28.customer:attr/windowNoTitle = 0x7f04049f
com.mohandhass28.customer:id/action_bar_activity_content = 0x7f0a003f
com.mohandhass28.customer:attr/windowMinWidthMajor = 0x7f04049d
com.mohandhass28.customer:color/m3_ref_palette_dynamic_secondary100 = 0x7f0600d7
com.mohandhass28.customer:id/material_hour_text_input = 0x7f0a0171
com.mohandhass28.customer:drawable/design_snackbar_background = 0x7f0800b0
com.mohandhass28.customer:attr/windowFixedHeightMinor = 0x7f04049a
com.mohandhass28.customer:color/design_fab_stroke_end_outer_color = 0x7f060065
com.mohandhass28.customer:id/parent_matrix = 0x7f0a01bf
com.mohandhass28.customer:id/action_mode_bar_stub = 0x7f0a004c
com.mohandhass28.customer:attr/windowFixedHeightMajor = 0x7f040499
com.mohandhass28.customer:attr/submitBackground = 0x7f0403d5
com.mohandhass28.customer:attr/windowActionBar = 0x7f040496
com.mohandhass28.customer:attr/waveOffset = 0x7f040492
com.mohandhass28.customer:attr/waveDecay = 0x7f040491
com.mohandhass28.customer:style/WalletFragmentDefaultDetailsHeaderTextAppearance = 0x7f130310
com.mohandhass28.customer:attr/expandedTitleMargin = 0x7f0401b3
com.mohandhass28.customer:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f070227
com.mohandhass28.customer:attr/materialCalendarDayOfWeekLabel = 0x7f0402d1
com.mohandhass28.customer:attr/verticalOffsetWithText = 0x7f04048b
com.mohandhass28.customer:attr/motionEasingLinear = 0x7f04030d
com.mohandhass28.customer:attr/cropMaxCropResultWidthPX = 0x7f04014b
com.mohandhass28.customer:style/Widget.AppCompat.ListView.DropDown = 0x7f130346
com.mohandhass28.customer:integer/m3_sys_motion_duration_600 = 0x7f0b001e
com.mohandhass28.customer:attr/progressBarImageScaleType = 0x7f04035b
com.mohandhass28.customer:attr/windowMinWidthMinor = 0x7f04049e
com.mohandhass28.customer:dimen/test_navigation_bar_shadow_height = 0x7f070270
com.mohandhass28.customer:attr/showTitle = 0x7f0403a1
com.mohandhass28.customer:attr/values = 0x7f040489
com.mohandhass28.customer:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0401d5
com.mohandhass28.customer:attr/use_controller = 0x7f040488
com.mohandhass28.customer:dimen/design_bottom_navigation_margin = 0x7f07006d
com.mohandhass28.customer:color/mtrl_text_btn_text_color_selector = 0x7f060250
com.mohandhass28.customer:attr/use_artwork = 0x7f040487
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1301e8
com.mohandhass28.customer:dimen/design_bottom_navigation_text_size = 0x7f07006f
com.mohandhass28.customer:color/material_on_surface_disabled = 0x7f060214
com.mohandhass28.customer:drawable/assets_streetfoodsassert_images_2 = 0x7f080085
com.mohandhass28.customer:attr/uiTiltGestures = 0x7f040480
com.mohandhass28.customer:dimen/test_navigation_bar_label_padding = 0x7f07026f
com.mohandhass28.customer:color/browser_actions_divider_color = 0x7f060029
com.mohandhass28.customer:color/material_dynamic_neutral60 = 0x7f0601c9
com.mohandhass28.customer:color/m3_ref_palette_tertiary80 = 0x7f060139
com.mohandhass28.customer:attr/uiScrollGesturesDuringRotateOrZoom = 0x7f04047f
com.mohandhass28.customer:string/abc_searchview_description_submit = 0x7f120016
com.mohandhass28.customer:color/m3_dark_primary_text_disable_only = 0x7f060093
com.mohandhass28.customer:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0700e4
com.mohandhass28.customer:attr/listChoiceIndicatorSingleAnimated = 0x7f0402a8
com.mohandhass28.customer:drawable/exo_notification_rewind = 0x7f0800e6
com.mohandhass28.customer:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f070006
com.mohandhass28.customer:id/exo_rew_with_amount = 0x7f0a00fd
com.mohandhass28.customer:attr/cropAspectRatioY = 0x7f040137
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f060161
com.mohandhass28.customer:attr/uiRotateGestures = 0x7f04047d
com.mohandhass28.customer:attr/yearStyle = 0x7f0404a6
com.mohandhass28.customer:attr/ttcIndex = 0x7f04047a
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f06017e
com.mohandhass28.customer:string/mtrl_picker_invalid_format_use = 0x7f1200fd
com.mohandhass28.customer:attr/cornerSizeBottomRight = 0x7f04012d
com.mohandhass28.customer:color/m3_sys_color_dark_secondary = 0x7f06015a
com.mohandhass28.customer:attr/itemHorizontalTranslationEnabled = 0x7f040237
com.mohandhass28.customer:attr/triggerReceiver = 0x7f040478
com.mohandhass28.customer:color/m3_dynamic_dark_hint_foreground = 0x7f060099
com.mohandhass28.customer:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f130321
com.mohandhass28.customer:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f130234
com.mohandhass28.customer:attr/controller_layout_id = 0x7f040122
com.mohandhass28.customer:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f130454
com.mohandhass28.customer:attr/transitionShapeAppearance = 0x7f040476
com.mohandhass28.customer:drawable/exo_icon_rewind = 0x7f0800dc
com.mohandhass28.customer:attr/transitionPathRotate = 0x7f040475
com.mohandhass28.customer:dimen/m3_extended_fab_icon_padding = 0x7f070111
com.mohandhass28.customer:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f130085
com.mohandhass28.customer:color/dim_foreground_disabled_material_dark = 0x7f06006a
com.mohandhass28.customer:attr/transitionFlags = 0x7f040474
com.mohandhass28.customer:attr/transitionDisable = 0x7f040472
com.mohandhass28.customer:styleable/LinearProgressIndicator = 0x7f14004d
com.mohandhass28.customer:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f080011
com.mohandhass28.customer:bool/workmanager_test_configuration = 0x7f050007
com.mohandhass28.customer:attr/trackThickness = 0x7f04046f
com.mohandhass28.customer:attr/cameraBearing = 0x7f040093
com.mohandhass28.customer:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f130340
com.mohandhass28.customer:attr/cropAutoZoomEnabled = 0x7f040138
com.mohandhass28.customer:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f1303e7
com.mohandhass28.customer:style/Widget.AppCompat.CompoundButton.Switch = 0x7f130328
com.mohandhass28.customer:attr/trackColor = 0x7f04046a
com.mohandhass28.customer:style/Widget.Autofill = 0x7f13035c
com.mohandhass28.customer:animator/linear_indeterminate_line1_head_interpolator = 0x7f020009
com.mohandhass28.customer:attr/track = 0x7f040469
com.mohandhass28.customer:id/chronometer = 0x7f0a0091
com.mohandhass28.customer:attr/boxBackgroundMode = 0x7f040075
com.mohandhass28.customer:id/list_item = 0x7f0a0165
com.mohandhass28.customer:drawable/assets_sidemenuassert_rectangle592762 = 0x7f080084
com.mohandhass28.customer:attr/touch_target_height = 0x7f040468
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f130068
com.mohandhass28.customer:color/design_default_color_primary_dark = 0x7f06005b
com.mohandhass28.customer:drawable/abc_seekbar_track_material = 0x7f080042
com.mohandhass28.customer:color/m3_ref_palette_error50 = 0x7f0600f5
com.mohandhass28.customer:color/notification_action_color_filter = 0x7f060256
com.mohandhass28.customer:attr/touchRegionId = 0x7f040467
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0600c6
com.mohandhass28.customer:string/catalyst_debug_open_disabled = 0x7f12002d
com.mohandhass28.customer:attr/topInsetScrimEnabled = 0x7f040464
com.mohandhass28.customer:attr/tooltipText = 0x7f040463
com.mohandhass28.customer:attr/textAppearanceBodyMedium = 0x7f040409
com.mohandhass28.customer:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f1300fc
com.mohandhass28.customer:attr/tooltipFrameBackground = 0x7f040461
com.mohandhass28.customer:attr/tooltipForegroundColor = 0x7f040460
com.mohandhass28.customer:dimen/m3_large_fab_size = 0x7f07011a
com.mohandhass28.customer:attr/layout_constraintLeft_creator = 0x7f04027e
com.mohandhass28.customer:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f13040b
com.mohandhass28.customer:attr/toolbarTextColorStyle = 0x7f04045f
com.mohandhass28.customer:id/dark = 0x7f0a00ab
com.mohandhass28.customer:attr/backgroundOverlayColorAlpha = 0x7f040051
com.mohandhass28.customer:attr/minWidth = 0x7f0402fc
com.mohandhass28.customer:attr/toolbarSurfaceStyle = 0x7f04045e
com.mohandhass28.customer:attr/cameraTargetLng = 0x7f040097
com.mohandhass28.customer:drawable/exo_styled_controls_repeat_off = 0x7f0800f6
com.mohandhass28.customer:attr/colorOnBackground = 0x7f0400eb
com.mohandhass28.customer:attr/toolbarNavigationButtonStyle = 0x7f04045c
com.mohandhass28.customer:attr/tabStyle = 0x7f0403fd
com.mohandhass28.customer:string/mtrl_badge_numberless_content_description = 0x7f1200ee
com.mohandhass28.customer:attr/titleTextStyle = 0x7f04045a
com.mohandhass28.customer:attr/titleTextColor = 0x7f040459
com.mohandhass28.customer:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f130177
com.mohandhass28.customer:color/material_dynamic_neutral_variant20 = 0x7f0601d2
com.mohandhass28.customer:attr/titleMarginTop = 0x7f040455
com.mohandhass28.customer:color/ripple_material_dark = 0x7f060262
com.mohandhass28.customer:attr/hintEnabled = 0x7f040211
com.mohandhass28.customer:attr/titleCollapseMode = 0x7f04044f
com.mohandhass28.customer:color/material_harmonized_color_on_error_container = 0x7f06020d
com.mohandhass28.customer:attr/tintMode = 0x7f04044c
com.mohandhass28.customer:attr/deltaPolarRadius = 0x7f040173
com.mohandhass28.customer:drawable/exo_styled_controls_repeat_all = 0x7f0800f5
com.mohandhass28.customer:attr/fontVariationSettings = 0x7f0401f8
com.mohandhass28.customer:attr/motionProgress = 0x7f040312
com.mohandhass28.customer:color/m3_ref_palette_error95 = 0x7f0600fa
com.mohandhass28.customer:attr/tickMark = 0x7f040446
com.mohandhass28.customer:attr/titleTextAppearance = 0x7f040458
com.mohandhass28.customer:drawable/abc_star_half_black_48dp = 0x7f080046
com.mohandhass28.customer:attr/thumbTintMode = 0x7f040442
com.mohandhass28.customer:attr/behavior_hideable = 0x7f040068
com.mohandhass28.customer:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f130458
com.mohandhass28.customer:attr/alertDialogCenterButtons = 0x7f04002b
com.mohandhass28.customer:attr/thumbStrokeWidth = 0x7f04043f
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_background = 0x7f060160
com.mohandhass28.customer:drawable/ic_rotate_right_24 = 0x7f08011a
com.mohandhass28.customer:animator/design_fab_hide_motion_spec = 0x7f020001
com.mohandhass28.customer:attr/textStartPadding = 0x7f040437
com.mohandhass28.customer:attr/motion_postLayoutCollision = 0x7f040315
com.mohandhass28.customer:style/TextAppearance.AppCompat.Tooltip = 0x7f1301d6
com.mohandhass28.customer:drawable/exo_controls_previous = 0x7f0800b7
com.mohandhass28.customer:styleable/ActivityChooserView = 0x7f140005
com.mohandhass28.customer:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1302d0
com.mohandhass28.customer:attr/cropGuidelines = 0x7f040146
com.mohandhass28.customer:bool/abc_action_bar_embed_tabs = 0x7f050000
com.mohandhass28.customer:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f1303f8
com.mohandhass28.customer:attr/clickAction = 0x7f0400c9
com.mohandhass28.customer:attr/textInputStyle = 0x7f040435
com.mohandhass28.customer:attr/textInputOutlinedStyle = 0x7f040434
com.mohandhass28.customer:style/FloatingDialogTheme = 0x7f130141
com.mohandhass28.customer:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020014
com.mohandhass28.customer:attr/textInputOutlinedDenseStyle = 0x7f040432
com.mohandhass28.customer:color/abc_btn_colored_borderless_text_material = 0x7f060002
com.mohandhass28.customer:id/sawtooth = 0x7f0a01f2
com.mohandhass28.customer:attr/textInputLayoutFocusedRectEnabled = 0x7f040431
com.mohandhass28.customer:attr/textInputFilledStyle = 0x7f040430
com.mohandhass28.customer:id/rn_redbox_stack = 0x7f0a01e6
com.mohandhass28.customer:attr/tabIndicatorAnimationMode = 0x7f0403ec
com.mohandhass28.customer:style/Base.V22.Theme.AppCompat.Light = 0x7f1300a4
com.mohandhass28.customer:attr/textInputFilledExposedDropdownMenuStyle = 0x7f04042f
com.mohandhass28.customer:id/cropImageView = 0x7f0a00a5
com.mohandhass28.customer:dimen/tooltip_precise_anchor_extra_offset = 0x7f070275
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f13003d
com.mohandhass28.customer:attr/backgroundInsetEnd = 0x7f04004e
com.mohandhass28.customer:styleable/MaterialCalendarItem = 0x7f140057
com.mohandhass28.customer:color/m3_sys_color_dark_inverse_primary = 0x7f06014a
com.mohandhass28.customer:style/ThemeOverlay.AppCompat.DayNight = 0x7f1302af
com.mohandhass28.customer:attr/snackbarButtonStyle = 0x7f0403b4
com.mohandhass28.customer:attr/textAppearanceLabelLarge = 0x7f040419
com.mohandhass28.customer:attr/textAppearanceTitleSmall = 0x7f04042a
com.mohandhass28.customer:dimen/tooltip_y_offset_non_touch = 0x7f070278
com.mohandhass28.customer:style/ThemeOverlay.AppCompat.ActionBar = 0x7f1302ac
com.mohandhass28.customer:anim/rns_ios_from_right_background_open = 0x7f010033
com.mohandhass28.customer:attr/boxCornerRadiusBottomEnd = 0x7f040077
com.mohandhass28.customer:attr/textAppearanceTitleMedium = 0x7f040429
com.mohandhass28.customer:dimen/m3_navigation_item_shape_inset_top = 0x7f070122
com.mohandhass28.customer:color/iconBackground = 0x7f06007c
com.mohandhass28.customer:attr/textAppearanceOverline = 0x7f040421
com.mohandhass28.customer:color/m3_chip_stroke_color = 0x7f06008d
com.mohandhass28.customer:attr/materialCardViewFilledStyle = 0x7f0402e0
com.mohandhass28.customer:layout/design_layout_snackbar = 0x7f0d0027
com.mohandhass28.customer:attr/textAppearancePopupMenuHeader = 0x7f040422
com.mohandhass28.customer:dimen/abc_action_button_min_width_material = 0x7f07000e
com.mohandhass28.customer:attr/textAppearanceListItemSmall = 0x7f040420
com.mohandhass28.customer:id/focusCrop = 0x7f0a0125
com.mohandhass28.customer:attr/textAppearanceListItemSecondary = 0x7f04041f
com.mohandhass28.customer:interpolator/mtrl_fast_out_slow_in = 0x7f0c0008
com.mohandhass28.customer:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0701f5
com.mohandhass28.customer:styleable/TextAppearance = 0x7f140090
com.mohandhass28.customer:string/call_notification_ongoing_text = 0x7f120026
com.mohandhass28.customer:attr/cropScaleType = 0x7f040153
com.mohandhass28.customer:integer/m3_sys_motion_duration_450 = 0x7f0b001a
com.mohandhass28.customer:attr/textAppearanceListItem = 0x7f04041e
com.mohandhass28.customer:string/call_notification_answer_action = 0x7f120021
com.mohandhass28.customer:id/compose_view_saveable_id_tag = 0x7f0a009a
com.mohandhass28.customer:color/m3_ref_palette_neutral70 = 0x7f060104
com.mohandhass28.customer:id/rewind_button = 0x7f0a01d9
com.mohandhass28.customer:attr/clockNumberTextColor = 0x7f0400cd
com.mohandhass28.customer:attr/elevationOverlayEnabled = 0x7f04019b
com.mohandhass28.customer:id/invisible = 0x7f0a0153
com.mohandhass28.customer:color/m3_ref_palette_tertiary99 = 0x7f06013c
com.mohandhass28.customer:attr/textAppearanceLineHeightEnabled = 0x7f04041d
com.mohandhass28.customer:color/m3_ref_palette_tertiary50 = 0x7f060136
com.mohandhass28.customer:style/Theme.AppCompat.DialogWhenLarge = 0x7f130245
com.mohandhass28.customer:attr/textAppearanceLabelMedium = 0x7f04041a
com.mohandhass28.customer:dimen/design_tab_max_width = 0x7f07008e
com.mohandhass28.customer:attr/dividerVertical = 0x7f040180
com.mohandhass28.customer:color/abc_primary_text_material_dark = 0x7f06000b
com.mohandhass28.customer:color/androidx_core_secondary_text_default_material_light = 0x7f06001c
com.mohandhass28.customer:drawable/abc_switch_track_mtrl_alpha = 0x7f080048
com.mohandhass28.customer:attr/textAppearanceHeadlineSmall = 0x7f040418
com.mohandhass28.customer:attr/textAppearanceHeadlineLarge = 0x7f040416
com.mohandhass28.customer:attr/materialCalendarStyle = 0x7f0402dc
com.mohandhass28.customer:attr/motionEasingDecelerated = 0x7f04030b
com.mohandhass28.customer:dimen/mtrl_btn_z = 0x7f0701b7
com.mohandhass28.customer:attr/textAppearanceHeadline6 = 0x7f040415
com.mohandhass28.customer:attr/textAppearanceHeadline1 = 0x7f040410
com.mohandhass28.customer:color/m3_sys_color_dark_error = 0x7f060147
com.mohandhass28.customer:styleable/Badge = 0x7f140015
com.mohandhass28.customer:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f08012e
com.mohandhass28.customer:attr/textAppearanceDisplaySmall = 0x7f04040f
com.mohandhass28.customer:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f130170
com.mohandhass28.customer:attr/font = 0x7f0401ee
com.mohandhass28.customer:integer/mtrl_view_invisible = 0x7f0b0035
com.mohandhass28.customer:dimen/design_bottom_sheet_elevation = 0x7f070070
com.mohandhass28.customer:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f13018f
com.mohandhass28.customer:attr/drawerArrowStyle = 0x7f04018e
com.mohandhass28.customer:attr/textAppearanceDisplayLarge = 0x7f04040d
com.mohandhass28.customer:attr/minSeparation = 0x7f0402fa
com.mohandhass28.customer:attr/tabUnboundedRipple = 0x7f040400
com.mohandhass28.customer:attr/textAppearanceCaption = 0x7f04040c
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.Body2 = 0x7f130222
com.mohandhass28.customer:style/Base.V21.Theme.AppCompat.Dialog = 0x7f130099
com.mohandhass28.customer:color/colorPrimaryDark = 0x7f060038
com.mohandhass28.customer:attr/shapeAppearance = 0x7f040394
com.mohandhass28.customer:style/TextAppearance.Material3.HeadlineSmall = 0x7f130218
com.mohandhass28.customer:attr/textAppearanceButton = 0x7f04040b
com.mohandhass28.customer:dimen/design_fab_size_mini = 0x7f070076
com.mohandhass28.customer:attr/textAllCaps = 0x7f040405
com.mohandhass28.customer:attr/buyButtonText = 0x7f040091
com.mohandhass28.customer:id/exo_main_text = 0x7f0a00ec
com.mohandhass28.customer:color/m3_navigation_item_ripple_color = 0x7f0600a8
com.mohandhass28.customer:attr/visibilityMode = 0x7f04048e
com.mohandhass28.customer:attr/telltales_velocityMode = 0x7f040404
com.mohandhass28.customer:style/MaterialAlertDialog.Material3.Body.Text = 0x7f130144
com.mohandhass28.customer:anim/rns_slide_in_from_bottom = 0x7f01003a
com.mohandhass28.customer:attr/telltales_tailScale = 0x7f040403
com.mohandhass28.customer:string/common_google_play_services_install_text = 0x7f12004e
com.mohandhass28.customer:dimen/mtrl_btn_hovered_z = 0x7f0701a6
com.mohandhass28.customer:string/call_notification_screening_text = 0x7f120027
com.mohandhass28.customer:attr/textAppearanceHeadline4 = 0x7f040413
com.mohandhass28.customer:attr/isMaterial3Theme = 0x7f040231
com.mohandhass28.customer:attr/targetId = 0x7f040401
com.mohandhass28.customer:color/material_dynamic_primary40 = 0x7f0601e1
com.mohandhass28.customer:color/m3_ref_palette_neutral10 = 0x7f0600fd
com.mohandhass28.customer:string/exo_controls_playback_speed = 0x7f120075
com.mohandhass28.customer:attr/expanded = 0x7f0401b0
com.mohandhass28.customer:style/Widget.Material3.Chip.Input = 0x7f130394
com.mohandhass28.customer:attr/floatingActionButtonLargePrimaryStyle = 0x7f0401d1
com.mohandhass28.customer:attr/lineSpacing = 0x7f0402a4
com.mohandhass28.customer:attr/tabPaddingTop = 0x7f0403f9
com.mohandhass28.customer:attr/errorIconTint = 0x7f0401ab
com.mohandhass28.customer:dimen/m3_chip_disabled_translation_z = 0x7f070108
com.mohandhass28.customer:color/switch_thumb_material_dark = 0x7f06026b
com.mohandhass28.customer:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f1303af
com.mohandhass28.customer:attr/tabPaddingStart = 0x7f0403f8
com.mohandhass28.customer:dimen/mtrl_calendar_month_vertical_padding = 0x7f0701d1
com.mohandhass28.customer:color/m3_ref_palette_tertiary30 = 0x7f060134
com.mohandhass28.customer:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f130449
com.mohandhass28.customer:attr/tabPaddingBottom = 0x7f0403f6
com.mohandhass28.customer:attr/useMaterialThemeColors = 0x7f040485
com.mohandhass28.customer:string/catalyst_settings = 0x7f120040
com.mohandhass28.customer:attr/tabMode = 0x7f0403f4
com.mohandhass28.customer:id/CropProgressBar = 0x7f0a0005
com.mohandhass28.customer:attr/tabInlineLabel = 0x7f0403f1
com.mohandhass28.customer:attr/limitBoundsTo = 0x7f0402a2
com.mohandhass28.customer:id/expanded_menu = 0x7f0a010a
com.mohandhass28.customer:attr/suffixTextAppearance = 0x7f0403dc
com.mohandhass28.customer:attr/tabIndicatorColor = 0x7f0403ed
com.mohandhass28.customer:styleable/StyledPlayerControlView = 0x7f140089
com.mohandhass28.customer:attr/tabIndicatorGravity = 0x7f0403ef
com.mohandhass28.customer:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0401be
com.mohandhass28.customer:attr/viewAspectRatio = 0x7f04048c
com.mohandhass28.customer:attr/endIconContentDescription = 0x7f04019f
com.mohandhass28.customer:styleable/KeyFramesVelocity = 0x7f140046
com.mohandhass28.customer:color/background_floating_material_dark = 0x7f06001d
com.mohandhass28.customer:attr/autofillInlineSuggestionTitle = 0x7f040049
com.mohandhass28.customer:attr/tabIndicatorAnimationDuration = 0x7f0403eb
com.mohandhass28.customer:color/m3_ref_palette_tertiary100 = 0x7f060132
com.mohandhass28.customer:attr/tabIndicator = 0x7f0403ea
com.mohandhass28.customer:attr/tabIconTint = 0x7f0403e8
com.mohandhass28.customer:attr/hideOnScroll = 0x7f04020d
com.mohandhass28.customer:color/m3_timepicker_display_stroke_color = 0x7f0601b6
com.mohandhass28.customer:attr/collapsingToolbarLayoutStyle = 0x7f0400e0
com.mohandhass28.customer:attr/switchPadding = 0x7f0403e2
com.mohandhass28.customer:attr/backgroundImage = 0x7f04004c
com.mohandhass28.customer:drawable/assets_profileimgcopy_bottomsheet_more_horiz = 0x7f08006c
com.mohandhass28.customer:style/TextAppearance.AppCompat.Subhead = 0x7f1301d2
com.mohandhass28.customer:dimen/material_bottom_sheet_max_width = 0x7f070165
com.mohandhass28.customer:attr/switchMinWidth = 0x7f0403e1
com.mohandhass28.customer:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f080020
com.mohandhass28.customer:dimen/abc_dialog_fixed_width_minor = 0x7f07001f
com.mohandhass28.customer:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f0700c4
com.mohandhass28.customer:attr/expandedTitleMarginBottom = 0x7f0401b4
com.mohandhass28.customer:attr/isLightTheme = 0x7f040230
com.mohandhass28.customer:attr/fontFamily = 0x7f0401ef
com.mohandhass28.customer:attr/cropGuidelinesColor = 0x7f040147
com.mohandhass28.customer:attr/subtitleTextStyle = 0x7f0403da
com.mohandhass28.customer:dimen/exo_styled_minimal_controls_margin_bottom = 0x7f0700b1
com.mohandhass28.customer:color/vector_tint_color = 0x7f060274
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral10 = 0x7f0600af
com.mohandhass28.customer:id/accessibility_custom_action_17 = 0x7f0a001f
com.mohandhass28.customer:string/character_counter_pattern = 0x7f120044
com.mohandhass28.customer:attr/actionBarTabTextStyle = 0x7f040008
com.mohandhass28.customer:attr/subtitle = 0x7f0403d6
com.mohandhass28.customer:style/Theme.Design.Light.BottomSheetDialog = 0x7f130256
com.mohandhass28.customer:dimen/mtrl_calendar_header_text_padding = 0x7f0701cb
com.mohandhass28.customer:attr/subheaderTextAppearance = 0x7f0403d4
com.mohandhass28.customer:attr/subheaderInsetEnd = 0x7f0403d2
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0600be
com.mohandhass28.customer:attr/extendMotionSpec = 0x7f0401ba
com.mohandhass28.customer:drawable/assets_dailyessentialsassert_hotal_1 = 0x7f080058
com.mohandhass28.customer:attr/subheaderColor = 0x7f0403d1
com.mohandhass28.customer:id/centerCrop = 0x7f0a0083
com.mohandhass28.customer:color/m3_ref_palette_neutral80 = 0x7f060105
com.mohandhass28.customer:attr/subMenuArrow = 0x7f0403d0
com.mohandhass28.customer:attr/wavePeriod = 0x7f040493
com.mohandhass28.customer:attr/strokeWidth = 0x7f0403cf
com.mohandhass28.customer:string/catalyst_perf_monitor_stop = 0x7f12003a
com.mohandhass28.customer:attr/actionModeFindDrawable = 0x7f040016
com.mohandhass28.customer:attr/strokeColor = 0x7f0403ce
com.mohandhass28.customer:dimen/exo_small_icon_padding_vertical = 0x7f0700ab
com.mohandhass28.customer:attr/textAppearanceLabelSmall = 0x7f04041b
com.mohandhass28.customer:layout/exo_styled_sub_settings_list_item = 0x7f0d003e
com.mohandhass28.customer:layout/exo_styled_player_control_view = 0x7f0d003a
com.mohandhass28.customer:attr/enforceTextAppearance = 0x7f0401a5
com.mohandhass28.customer:attr/state_liftable = 0x7f0403c9
com.mohandhass28.customer:color/wallet_link_text_light = 0x7f060280
com.mohandhass28.customer:style/Base.V22.Theme.AppCompat = 0x7f1300a3
com.mohandhass28.customer:integer/exo_media_button_opacity_percentage_disabled = 0x7f0b0009
com.mohandhass28.customer:attr/itemSpacing = 0x7f040248
com.mohandhass28.customer:id/browser_actions_menu_items = 0x7f0a0076
com.mohandhass28.customer:attr/layout_constraintCircleAngle = 0x7f04026f
com.mohandhass28.customer:attr/state_dragged = 0x7f0403c8
com.mohandhass28.customer:attr/state_collapsible = 0x7f0403c7
com.mohandhass28.customer:attr/layout_constraintStart_toEndOf = 0x7f040284
com.mohandhass28.customer:attr/state_above_anchor = 0x7f0403c5
com.mohandhass28.customer:dimen/m3_chip_elevated_elevation = 0x7f07010a
com.mohandhass28.customer:color/design_fab_shadow_mid_color = 0x7f060062
com.mohandhass28.customer:layout/mtrl_calendar_vertical = 0x7f0d0066
com.mohandhass28.customer:id/filter = 0x7f0a0114
com.mohandhass28.customer:color/m3_ref_palette_error60 = 0x7f0600f6
com.mohandhass28.customer:attr/emojiCompatEnabled = 0x7f04019c
com.mohandhass28.customer:attr/startIconCheckable = 0x7f0403c0
com.mohandhass28.customer:anim/rns_default_enter_out = 0x7f010027
com.mohandhass28.customer:style/TestStyleWithLineHeightAppearance = 0x7f1301b5
com.mohandhass28.customer:attr/checkedIconEnabled = 0x7f0400ab
com.mohandhass28.customer:attr/staggered = 0x7f0403bf
com.mohandhass28.customer:attr/alpha = 0x7f04002f
com.mohandhass28.customer:attr/latLngBoundsSouthWestLongitude = 0x7f04025d
com.mohandhass28.customer:array/assume_strong_biometrics_models = 0x7f030000
com.mohandhass28.customer:attr/layout = 0x7f04025e
com.mohandhass28.customer:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f13019a
com.mohandhass28.customer:attr/srcCompat = 0x7f0403bd
com.mohandhass28.customer:dimen/exo_styled_progress_enabled_thumb_size = 0x7f0700b4
com.mohandhass28.customer:anim/rns_ios_from_left_foreground_open = 0x7f010031
com.mohandhass28.customer:style/TextAppearance.Design.Tab = 0x7f1301fe
com.mohandhass28.customer:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1300b4
com.mohandhass28.customer:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1300b1
com.mohandhass28.customer:attr/materialCalendarHeaderSelection = 0x7f0402d7
com.mohandhass28.customer:attr/rangeFillColor = 0x7f040362
com.mohandhass28.customer:attr/mock_labelColor = 0x7f040300
com.mohandhass28.customer:attr/spinnerStyle = 0x7f0403ba
com.mohandhass28.customer:drawable/abc_tab_indicator_material = 0x7f080049
com.mohandhass28.customer:color/m3_sys_color_light_on_error_container = 0x7f060194
com.mohandhass28.customer:style/Theme.AutofillInlineSuggestion = 0x7f13024f
com.mohandhass28.customer:layout/abc_alert_dialog_title_material = 0x7f0d000a
com.mohandhass28.customer:attr/spinnerDropDownItemStyle = 0x7f0403b9
com.mohandhass28.customer:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1300d8
com.mohandhass28.customer:id/view_tree_view_model_store_owner = 0x7f0a0276
com.mohandhass28.customer:attr/percentX = 0x7f040341
com.mohandhass28.customer:drawable/ic_m3_chip_checked_circle = 0x7f080112
com.mohandhass28.customer:attr/tabContentStart = 0x7f0403e6
com.mohandhass28.customer:id/icon = 0x7f0a0149
com.mohandhass28.customer:attr/divider = 0x7f040179
com.mohandhass28.customer:attr/snackbarTextViewStyle = 0x7f0403b6
com.mohandhass28.customer:color/m3_ref_palette_secondary20 = 0x7f060126
com.mohandhass28.customer:style/AlertDialog.AppCompat = 0x7f130000
com.mohandhass28.customer:color/abc_decor_view_status_guard = 0x7f060005
com.mohandhass28.customer:attr/sliderStyle = 0x7f0403b3
com.mohandhass28.customer:layout/dev_loading_view = 0x7f0d0034
com.mohandhass28.customer:attr/singleChoiceItemLayout = 0x7f0403af
com.mohandhass28.customer:attr/contrast = 0x7f040120
com.mohandhass28.customer:color/common_google_signin_btn_text_dark_pressed = 0x7f06003d
com.mohandhass28.customer:attr/boxCornerRadiusTopStart = 0x7f04007a
com.mohandhass28.customer:styleable/CollapsingToolbarLayout_Layout = 0x7f140024
com.mohandhass28.customer:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f130282
com.mohandhass28.customer:attr/tickMarkTintMode = 0x7f040448
com.mohandhass28.customer:attr/simpleItemLayout = 0x7f0403ad
com.mohandhass28.customer:layout/text_view_with_theme_line_height = 0x7f0d009f
com.mohandhass28.customer:attr/chipIconVisible = 0x7f0400ba
com.mohandhass28.customer:attr/shutter_background_color = 0x7f0403ac
com.mohandhass28.customer:attr/time_bar_min_update_interval = 0x7f04044a
com.mohandhass28.customer:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f130191
com.mohandhass28.customer:style/Base.Theme.AppCompat.CompactMenu = 0x7f13004b
com.mohandhass28.customer:dimen/notification_action_icon_size = 0x7f07024f
com.mohandhass28.customer:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f13019f
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f13003b
com.mohandhass28.customer:color/abc_hint_foreground_material_dark = 0x7f060007
com.mohandhass28.customer:layout/notification_template_icon_group = 0x7f0d007e
com.mohandhass28.customer:dimen/exo_styled_controls_padding = 0x7f0700b0
com.mohandhass28.customer:attr/show_subtitle_button = 0x7f0403a8
com.mohandhass28.customer:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f080051
com.mohandhass28.customer:color/mtrl_tabs_colored_ripple_color = 0x7f06024b
com.mohandhass28.customer:attr/buttonBarNeutralButtonStyle = 0x7f040083
com.mohandhass28.customer:attr/colorSwitchThumbNormal = 0x7f040107
com.mohandhass28.customer:style/ExoStyledControls.Button.Bottom.CC = 0x7f13012d
com.mohandhass28.customer:color/m3_card_stroke_color = 0x7f060089
com.mohandhass28.customer:attr/showText = 0x7f0403a0
com.mohandhass28.customer:dimen/exo_small_icon_height = 0x7f0700a8
com.mohandhass28.customer:dimen/m3_btn_text_btn_padding_right = 0x7f0700fc
com.mohandhass28.customer:attr/itemPaddingBottom = 0x7f04023e
com.mohandhass28.customer:attr/colorOnContainer = 0x7f0400ec
com.mohandhass28.customer:attr/showPaths = 0x7f04039f
com.mohandhass28.customer:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f130281
com.mohandhass28.customer:id/buy_now = 0x7f0a007c
com.mohandhass28.customer:drawable/abc_star_black_48dp = 0x7f080045
com.mohandhass28.customer:attr/maxCharacterCount = 0x7f0402ef
com.mohandhass28.customer:color/accent_material_light = 0x7f06001a
com.mohandhass28.customer:attr/showMotionSpec = 0x7f04039e
com.mohandhass28.customer:style/ThemeOverlay.Material3.Button = 0x7f1302be
com.mohandhass28.customer:color/m3_ref_palette_dynamic_tertiary0 = 0x7f0600e2
com.mohandhass28.customer:attr/showDelay = 0x7f04039c
com.mohandhass28.customer:color/material_on_primary_emphasis_medium = 0x7f060213
com.mohandhass28.customer:attr/selectorSize = 0x7f040393
com.mohandhass28.customer:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f130372
com.mohandhass28.customer:drawable/abc_cab_background_top_mtrl_alpha = 0x7f080017
com.mohandhass28.customer:style/redboxButton = 0x7f130478
com.mohandhass28.customer:dimen/m3_appbar_size_medium = 0x7f0700d4
com.mohandhass28.customer:attr/selectionRequired = 0x7f040392
com.mohandhass28.customer:attr/selectableItemBackgroundBorderless = 0x7f040391
com.mohandhass28.customer:attr/listChoiceIndicatorMultipleAnimated = 0x7f0402a7
com.mohandhass28.customer:attr/searchIcon = 0x7f04038d
com.mohandhass28.customer:style/Base.Theme.Material3.Dark = 0x7f130058
com.mohandhass28.customer:id/center_horizontal = 0x7f0a0085
com.mohandhass28.customer:color/material_dynamic_primary0 = 0x7f0601dc
com.mohandhass28.customer:color/m3_ref_palette_neutral_variant99 = 0x7f060115
com.mohandhass28.customer:style/Theme.Material3.DayNight = 0x7f130263
com.mohandhass28.customer:dimen/mtrl_calendar_year_horizontal_padding = 0x7f0701df
com.mohandhass28.customer:attr/dropdownListPreferredItemHeight = 0x7f040192
com.mohandhass28.customer:attr/scopeUris = 0x7f040383
com.mohandhass28.customer:id/chain = 0x7f0a0087
com.mohandhass28.customer:string/mtrl_picker_day_of_week_column_header = 0x7f1200fa
com.mohandhass28.customer:attr/roundingBorderColor = 0x7f04037e
com.mohandhass28.customer:attr/roundedCornerRadius = 0x7f04037d
com.mohandhass28.customer:string/catalyst_dev_menu_sub_header = 0x7f12002f
com.mohandhass28.customer:color/primary_text_default_material_light = 0x7f06025e
com.mohandhass28.customer:attr/helperTextTextColor = 0x7f040209
com.mohandhass28.customer:attr/itemTextAppearanceInactive = 0x7f04024d
com.mohandhass28.customer:attr/roundWithOverlayColor = 0x7f04037c
com.mohandhass28.customer:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f07024e
com.mohandhass28.customer:attr/roundTopStart = 0x7f04037b
com.mohandhass28.customer:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f130283
com.mohandhass28.customer:attr/textAppearanceSearchResultSubtitle = 0x7f040423
com.mohandhass28.customer:attr/motionPathRotate = 0x7f040311
com.mohandhass28.customer:attr/roundPercent = 0x7f040377
com.mohandhass28.customer:style/ThemeOverlay.Material3.Chip = 0x7f1302c3
com.mohandhass28.customer:attr/roundBottomStart = 0x7f040376
com.mohandhass28.customer:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f130415
com.mohandhass28.customer:attr/roundBottomLeft = 0x7f040374
com.mohandhass28.customer:drawable/exo_styled_controls_pause = 0x7f0800f2
com.mohandhass28.customer:color/m3_ref_palette_dynamic_secondary95 = 0x7f0600e0
com.mohandhass28.customer:dimen/mtrl_slider_label_square_side = 0x7f070231
com.mohandhass28.customer:style/Widget.AppCompat.ImageButton = 0x7f13032c
com.mohandhass28.customer:attr/expandedTitleGravity = 0x7f0401b2
com.mohandhass28.customer:dimen/m3_timepicker_window_elevation = 0x7f070164
com.mohandhass28.customer:attr/toolbarStyle = 0x7f04045d
com.mohandhass28.customer:style/amu_ClusterIcon.TextAppearance = 0x7f130477
com.mohandhass28.customer:attr/layout_anchorGravity = 0x7f040263
com.mohandhass28.customer:attr/showAsAction = 0x7f04039b
com.mohandhass28.customer:id/autoComplete = 0x7f0a0064
com.mohandhass28.customer:attr/roundAsCircle = 0x7f040372
com.mohandhass28.customer:layout/notification_media_cancel_action = 0x7f0d0078
com.mohandhass28.customer:attr/dropDownListViewStyle = 0x7f040191
com.mohandhass28.customer:attr/round = 0x7f040371
com.mohandhass28.customer:id/flip = 0x7f0a0123
com.mohandhass28.customer:attr/textLocale = 0x7f040436
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_surface = 0x7f060172
com.mohandhass28.customer:attr/listPreferredItemHeightSmall = 0x7f0402b0
com.mohandhass28.customer:attr/counterOverflowTextAppearance = 0x7f040132
com.mohandhass28.customer:color/material_on_primary_emphasis_high_type = 0x7f060212
com.mohandhass28.customer:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f13033b
com.mohandhass28.customer:attr/customStringValue = 0x7f040168
com.mohandhass28.customer:string/catalyst_inspector_toggle = 0x7f120036
com.mohandhass28.customer:color/m3_ref_palette_dynamic_secondary0 = 0x7f0600d5
com.mohandhass28.customer:attr/checkedIconSize = 0x7f0400ae
com.mohandhass28.customer:style/ShapeAppearance.Material3.Corner.Large = 0x7f130185
com.mohandhass28.customer:attr/retryImageScaleType = 0x7f04036e
com.mohandhass28.customer:id/mtrl_motion_snapshot_view = 0x7f0a0199
com.mohandhass28.customer:attr/resize_mode = 0x7f04036c
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f13022f
com.mohandhass28.customer:color/material_on_surface_emphasis_medium = 0x7f060216
com.mohandhass28.customer:attr/repeat_toggle_modes = 0x7f04036b
com.mohandhass28.customer:id/action_text = 0x7f0a004e
com.mohandhass28.customer:color/cardview_shadow_start_color = 0x7f060033
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f130037
com.mohandhass28.customer:attr/fontProviderFetchStrategy = 0x7f0401f2
com.mohandhass28.customer:attr/textAppearanceBodyLarge = 0x7f040408
com.mohandhass28.customer:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_fontawesome5_solid = 0x7f110008
com.mohandhass28.customer:attr/region_heightLessThan = 0x7f040367
com.mohandhass28.customer:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f13019b
com.mohandhass28.customer:string/material_timepicker_pm = 0x7f1200e8
com.mohandhass28.customer:layout/notification_template_big_media_custom = 0x7f0d007a
com.mohandhass28.customer:attr/recyclerViewStyle = 0x7f040366
com.mohandhass28.customer:string/template_percent = 0x7f120135
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0600c4
com.mohandhass28.customer:styleable/ViewBackgroundHelper = 0x7f14009a
com.mohandhass28.customer:color/material_dynamic_neutral0 = 0x7f0601c2
com.mohandhass28.customer:id/gone = 0x7f0a012e
com.mohandhass28.customer:attr/ratingBarStyle = 0x7f040363
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.Headline5 = 0x7f13022a
com.mohandhass28.customer:drawable/ic_flip_24 = 0x7f08010c
com.mohandhass28.customer:attr/queryBackground = 0x7f04035e
com.mohandhass28.customer:color/material_dynamic_tertiary10 = 0x7f0601f7
com.mohandhass28.customer:attr/radioButtonStyle = 0x7f040361
com.mohandhass28.customer:attr/mock_showLabel = 0x7f040302
com.mohandhass28.customer:id/status_bar_latest_event_content = 0x7f0a0229
com.mohandhass28.customer:attr/progressBarPadding = 0x7f04035c
com.mohandhass28.customer:attr/passwordToggleTintMode = 0x7f04033c
com.mohandhass28.customer:attr/textColorAlertDialogListItem = 0x7f04042b
com.mohandhass28.customer:attr/progressBarImage = 0x7f04035a
com.mohandhass28.customer:attr/mock_labelBackgroundColor = 0x7f0402ff
com.mohandhass28.customer:bool/enable_system_job_service_default = 0x7f050004
com.mohandhass28.customer:color/m3_ref_palette_primary70 = 0x7f06011e
com.mohandhass28.customer:attr/tickMarkTint = 0x7f040447
com.mohandhass28.customer:attr/imageAspectRatioAdjust = 0x7f040223
com.mohandhass28.customer:attr/queryHint = 0x7f04035f
com.mohandhass28.customer:attr/pressedTranslationZ = 0x7f040358
com.mohandhass28.customer:attr/prefixTextAppearance = 0x7f040354
com.mohandhass28.customer:attr/touchAnchorId = 0x7f040465
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f060171
com.mohandhass28.customer:attr/popupTheme = 0x7f040350
com.mohandhass28.customer:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f130176
com.mohandhass28.customer:dimen/notification_small_icon_size_as_large = 0x7f07025a
com.mohandhass28.customer:color/m3_ref_palette_neutral50 = 0x7f060102
com.mohandhass28.customer:id/decor_content_parent = 0x7f0a00af
com.mohandhass28.customer:attr/tabGravity = 0x7f0403e7
com.mohandhass28.customer:style/Widget.MaterialComponents.Tooltip = 0x7f130473
com.mohandhass28.customer:color/design_dark_default_color_background = 0x7f060046
com.mohandhass28.customer:attr/cropFlipVertically = 0x7f040145
com.mohandhass28.customer:attr/popupMenuStyle = 0x7f04034f
com.mohandhass28.customer:dimen/mtrl_btn_disabled_z = 0x7f0701a3
com.mohandhass28.customer:attr/popupMenuBackground = 0x7f04034e
com.mohandhass28.customer:attr/player_layout_id = 0x7f04034d
com.mohandhass28.customer:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001a
com.mohandhass28.customer:attr/checkboxStyle = 0x7f0400a7
com.mohandhass28.customer:attr/dayInvalidStyle = 0x7f04016a
com.mohandhass28.customer:attr/placeholder_emptyVisibility = 0x7f04034a
com.mohandhass28.customer:drawable/paused_in_debugger_dialog_background = 0x7f08015b
com.mohandhass28.customer:attr/placeholderTextColor = 0x7f040349
com.mohandhass28.customer:dimen/m3_menu_elevation = 0x7f07011b
com.mohandhass28.customer:attr/chipIconEnabled = 0x7f0400b7
com.mohandhass28.customer:attr/onNegativeCross = 0x7f040326
com.mohandhass28.customer:attr/roundingBorderPadding = 0x7f04037f
com.mohandhass28.customer:attr/panelMenuListWidth = 0x7f040337
com.mohandhass28.customer:style/Base.V14.Theme.MaterialComponents.Light = 0x7f13008e
com.mohandhass28.customer:color/design_dark_default_color_secondary_variant = 0x7f060051
com.mohandhass28.customer:dimen/m3_navigation_rail_item_padding_top = 0x7f07012c
com.mohandhass28.customer:attr/placeholderText = 0x7f040347
com.mohandhass28.customer:drawable/node_modules_reactnativeratings_dist_images_airbnbstarselected = 0x7f080143
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral0 = 0x7f0600ae
com.mohandhass28.customer:attr/placeholderImageScaleType = 0x7f040346
com.mohandhass28.customer:attr/placeholderImage = 0x7f040345
com.mohandhass28.customer:dimen/mtrl_navigation_item_icon_size = 0x7f070211
com.mohandhass28.customer:attr/materialCircleRadius = 0x7f0402e3
com.mohandhass28.customer:attr/pivotAnchor = 0x7f040344
com.mohandhass28.customer:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f130076
com.mohandhass28.customer:attr/perpendicularPath_percent = 0x7f040343
com.mohandhass28.customer:attr/path_percent = 0x7f04033e
com.mohandhass28.customer:dimen/m3_btn_padding_top = 0x7f0700f7
com.mohandhass28.customer:attr/passwordToggleTint = 0x7f04033b
com.mohandhass28.customer:dimen/compat_notification_large_icon_max_height = 0x7f07005f
com.mohandhass28.customer:color/m3_sys_color_light_surface = 0x7f0601a2
com.mohandhass28.customer:string/state_empty = 0x7f120128
com.mohandhass28.customer:attr/paddingStart = 0x7f040332
com.mohandhass28.customer:attr/materialAlertDialogTitlePanelStyle = 0x7f0402cb
com.mohandhass28.customer:dimen/abc_seekbar_track_progress_height_material = 0x7f070039
com.mohandhass28.customer:attr/hintAnimationEnabled = 0x7f040210
com.mohandhass28.customer:attr/passwordToggleEnabled = 0x7f04033a
com.mohandhass28.customer:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f13046e
com.mohandhass28.customer:attr/cropShowLabel = 0x7f040156
com.mohandhass28.customer:attr/fabAnimationMode = 0x7f0401c2
com.mohandhass28.customer:color/design_dark_default_color_on_primary = 0x7f06004a
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f060180
com.mohandhass28.customer:attr/passwordToggleDrawable = 0x7f040339
com.mohandhass28.customer:attr/listPreferredItemHeightLarge = 0x7f0402af
com.mohandhass28.customer:layout/amu_text_bubble = 0x7f0d001d
com.mohandhass28.customer:attr/panelMenuListTheme = 0x7f040336
com.mohandhass28.customer:layout/design_navigation_item_subheader = 0x7f0d002f
com.mohandhass28.customer:attr/dividerColor = 0x7f04017a
com.mohandhass28.customer:attr/paddingTopSystemWindowInsets = 0x7f040334
com.mohandhass28.customer:attr/collapseIcon = 0x7f0400d7
com.mohandhass28.customer:attr/paddingTopNoTitle = 0x7f040333
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.Light = 0x7f130067
com.mohandhass28.customer:attr/colorOnPrimaryContainer = 0x7f0400f0
com.mohandhass28.customer:attr/paddingLeftSystemWindowInsets = 0x7f040330
com.mohandhass28.customer:attr/paddingBottomSystemWindowInsets = 0x7f04032e
com.mohandhass28.customer:attr/shapeAppearanceMediumComponent = 0x7f040396
com.mohandhass28.customer:attr/zOrderOnTop = 0x7f0404a8
com.mohandhass28.customer:attr/overlay = 0x7f04032b
com.mohandhass28.customer:attr/barLength = 0x7f04005c
com.mohandhass28.customer:attr/iconSize = 0x7f04021d
com.mohandhass28.customer:dimen/material_textinput_min_width = 0x7f070189
com.mohandhass28.customer:attr/tabPadding = 0x7f0403f5
com.mohandhass28.customer:drawable/assets_resimages_images_8 = 0x7f080080
com.mohandhass28.customer:attr/onShow = 0x7f040328
com.mohandhass28.customer:layout/notification_media_action = 0x7f0d0077
com.mohandhass28.customer:attr/dynamicColorThemeOverlay = 0x7f040194
com.mohandhass28.customer:drawable/assets_resimages_images_3 = 0x7f08007b
com.mohandhass28.customer:drawable/assets_resimages_2 = 0x7f080074
com.mohandhass28.customer:color/m3_ref_palette_dynamic_primary40 = 0x7f0600cd
com.mohandhass28.customer:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f07017c
com.mohandhass28.customer:attr/onCross = 0x7f040324
com.mohandhass28.customer:dimen/mtrl_navigation_rail_default_width = 0x7f070216
com.mohandhass28.customer:style/Widget.Material3.CompoundButton.RadioButton = 0x7f1303a3
com.mohandhass28.customer:attr/nestedScrollable = 0x7f040321
com.mohandhass28.customer:style/Widget.AppCompat.PopupWindow = 0x7f13034a
com.mohandhass28.customer:id/fullscreen_header = 0x7f0a0129
com.mohandhass28.customer:attr/nestedScrollViewStyle = 0x7f040320
com.mohandhass28.customer:id/accessibility_custom_action_6 = 0x7f0a0032
com.mohandhass28.customer:attr/nestedScrollFlags = 0x7f04031f
com.mohandhass28.customer:dimen/material_clock_period_toggle_margin_left = 0x7f07016e
com.mohandhass28.customer:id/alertTitle = 0x7f0a0054
com.mohandhass28.customer:attr/navigationViewStyle = 0x7f04031e
com.mohandhass28.customer:id/use_hardware_layer = 0x7f0a026e
com.mohandhass28.customer:attr/multiChoiceItemLayout = 0x7f040318
com.mohandhass28.customer:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f130408
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f13030c
com.mohandhass28.customer:attr/moveWhenScrollAtTop = 0x7f040317
com.mohandhass28.customer:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f07023f
com.mohandhass28.customer:attr/motion_triggerOnCollision = 0x7f040316
com.mohandhass28.customer:attr/motionInterpolator = 0x7f04030f
com.mohandhass28.customer:attr/motionEasingStandard = 0x7f04030e
com.mohandhass28.customer:attr/textAppearanceSearchResultTitle = 0x7f040424
com.mohandhass28.customer:drawable/rzp_logo = 0x7f080164
com.mohandhass28.customer:drawable/assets_resimages_6 = 0x7f080078
com.mohandhass28.customer:attr/iconEndPadding = 0x7f04021a
com.mohandhass28.customer:string/bottomsheet_action_expand_halfway = 0x7f120020
com.mohandhass28.customer:color/m3_ref_palette_dynamic_tertiary60 = 0x7f0600e9
com.mohandhass28.customer:id/sandbox = 0x7f0a01ee
com.mohandhass28.customer:drawable/common_google_signin_btn_icon_dark_normal_background = 0x7f08009b
com.mohandhass28.customer:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1300be
com.mohandhass28.customer:attr/materialButtonToggleGroupStyle = 0x7f0402cf
com.mohandhass28.customer:id/transitionToEnd = 0x7f0a025f
com.mohandhass28.customer:drawable/exo_controls_fastforward = 0x7f0800b1
com.mohandhass28.customer:color/design_default_color_on_error = 0x7f060056
com.mohandhass28.customer:layout/exo_styled_player_control_ffwd_button = 0x7f0d0038
com.mohandhass28.customer:attr/motionEasingAccelerated = 0x7f04030a
com.mohandhass28.customer:color/material_dynamic_secondary90 = 0x7f0601f3
com.mohandhass28.customer:attr/motionDurationShort2 = 0x7f040309
com.mohandhass28.customer:attr/motionDurationShort1 = 0x7f040308
com.mohandhass28.customer:attr/colorTertiaryContainer = 0x7f040109
com.mohandhass28.customer:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f070195
com.mohandhass28.customer:attr/scrubber_enabled_size = 0x7f04038b
com.mohandhass28.customer:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1300e1
com.mohandhass28.customer:dimen/highlight_alpha_material_light = 0x7f0700be
com.mohandhass28.customer:attr/motionDurationMedium2 = 0x7f040307
com.mohandhass28.customer:attr/motionDurationLong2 = 0x7f040305
com.mohandhass28.customer:attr/motionDurationLong1 = 0x7f040304
com.mohandhass28.customer:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f12010d
com.mohandhass28.customer:attr/mock_showDiagonals = 0x7f040301
com.mohandhass28.customer:color/m3_ref_palette_dynamic_secondary30 = 0x7f0600d9
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral20 = 0x7f0600b1
com.mohandhass28.customer:dimen/abc_text_size_title_material = 0x7f07004f
com.mohandhass28.customer:attr/tabMinWidth = 0x7f0403f3
com.mohandhass28.customer:attr/mock_diagonalsColor = 0x7f0402fd
com.mohandhass28.customer:attr/minTouchTargetSize = 0x7f0402fb
com.mohandhass28.customer:attr/fastScrollHorizontalThumbDrawable = 0x7f0401cc
com.mohandhass28.customer:id/accessibility_custom_action_2 = 0x7f0a0022
com.mohandhass28.customer:color/design_default_color_surface = 0x7f06005f
com.mohandhass28.customer:attr/tabRippleColor = 0x7f0403fa
com.mohandhass28.customer:id/accessibility_custom_action_26 = 0x7f0a0029
com.mohandhass28.customer:attr/menu = 0x7f0402f6
com.mohandhass28.customer:id/holo_light = 0x7f0a013f
com.mohandhass28.customer:attr/circularProgressIndicatorStyle = 0x7f0400c8
com.mohandhass28.customer:attr/measureWithLargestChild = 0x7f0402f5
com.mohandhass28.customer:attr/thumbRadius = 0x7f04043d
com.mohandhass28.customer:attr/appBarLayoutStyle = 0x7f040036
com.mohandhass28.customer:attr/maxWidth = 0x7f0402f4
com.mohandhass28.customer:color/m3_dark_default_color_primary_text = 0x7f06008f
com.mohandhass28.customer:attr/maxVelocity = 0x7f0402f3
com.mohandhass28.customer:attr/maxImageSize = 0x7f0402f1
com.mohandhass28.customer:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f130460
com.mohandhass28.customer:dimen/mtrl_btn_stroke_size = 0x7f0701b2
com.mohandhass28.customer:attr/labelStyle = 0x7f040256
com.mohandhass28.customer:attr/materialTimePickerTitleStyle = 0x7f0402eb
com.mohandhass28.customer:attr/materialTimePickerTheme = 0x7f0402ea
com.mohandhass28.customer:color/exo_white_opacity_70 = 0x7f060077
com.mohandhass28.customer:id/chip2 = 0x7f0a008e
com.mohandhass28.customer:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f13021c
com.mohandhass28.customer:layout/notification_template_big_media_narrow = 0x7f0d007b
com.mohandhass28.customer:attr/materialDividerStyle = 0x7f0402e7
com.mohandhass28.customer:style/Base.V14.Theme.MaterialComponents = 0x7f13008a
com.mohandhass28.customer:id/action_divider = 0x7f0a0047
com.mohandhass28.customer:attr/hintTextColor = 0x7f040213
com.mohandhass28.customer:attr/materialCalendarYearNavigationButton = 0x7f0402de
com.mohandhass28.customer:drawable/amu_bubble_mask = 0x7f080055
com.mohandhass28.customer:attr/materialCalendarTheme = 0x7f0402dd
com.mohandhass28.customer:attr/maxLines = 0x7f0402f2
com.mohandhass28.customer:id/center_vertical = 0x7f0a0086
com.mohandhass28.customer:dimen/m3_navigation_rail_default_width = 0x7f070126
com.mohandhass28.customer:attr/materialCalendarMonth = 0x7f0402da
com.mohandhass28.customer:attr/materialCalendarHeaderToggleButton = 0x7f0402d9
com.mohandhass28.customer:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f1302cb
com.mohandhass28.customer:dimen/design_tab_text_size_2line = 0x7f070091
com.mohandhass28.customer:id/amu_text = 0x7f0a0058
com.mohandhass28.customer:attr/materialCalendarHeaderTitle = 0x7f0402d8
com.mohandhass28.customer:styleable/Capability = 0x7f14001b
com.mohandhass28.customer:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f080025
com.mohandhass28.customer:attr/materialCalendarHeaderLayout = 0x7f0402d6
com.mohandhass28.customer:layout/abc_popup_menu_item_layout = 0x7f0d0013
com.mohandhass28.customer:attr/materialCalendarHeaderDivider = 0x7f0402d5
com.mohandhass28.customer:attr/materialCalendarHeaderConfirmButton = 0x7f0402d4
com.mohandhass28.customer:attr/drawerLayoutStyle = 0x7f040190
com.mohandhass28.customer:color/m3_ref_palette_neutral30 = 0x7f060100
com.mohandhass28.customer:id/exo_check = 0x7f0a00df
com.mohandhass28.customer:attr/materialCalendarHeaderCancelButton = 0x7f0402d3
com.mohandhass28.customer:attr/materialCalendarFullscreenTheme = 0x7f0402d2
com.mohandhass28.customer:attr/show_rewind_button = 0x7f0403a6
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1301dc
com.mohandhass28.customer:attr/roundTopRight = 0x7f04037a
com.mohandhass28.customer:color/common_google_signin_btn_text_dark = 0x7f060039
com.mohandhass28.customer:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1302db
com.mohandhass28.customer:layout/abc_activity_chooser_view = 0x7f0d0006
com.mohandhass28.customer:color/m3_timepicker_display_ripple_color = 0x7f0601b5
com.mohandhass28.customer:color/m3_ref_palette_tertiary70 = 0x7f060138
com.mohandhass28.customer:style/Widget.Material3.BottomAppBar = 0x7f130377
com.mohandhass28.customer:layout/mtrl_alert_select_dialog_multichoice = 0x7f0d005b
com.mohandhass28.customer:attr/materialButtonStyle = 0x7f0402ce
com.mohandhass28.customer:color/m3_ref_palette_neutral_variant10 = 0x7f06010a
com.mohandhass28.customer:dimen/mtrl_btn_dialog_btn_min_width = 0x7f0701a1
com.mohandhass28.customer:style/TextAppearance.AppCompat.Display1 = 0x7f1301bf
com.mohandhass28.customer:attr/materialButtonOutlinedStyle = 0x7f0402cd
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f13043f
com.mohandhass28.customer:attr/materialAlertDialogTitleTextStyle = 0x7f0402cc
com.mohandhass28.customer:id/googleMaterial2 = 0x7f0a012f
com.mohandhass28.customer:attr/materialAlertDialogTitleIconStyle = 0x7f0402ca
com.mohandhass28.customer:dimen/mtrl_progress_circular_inset_medium = 0x7f07021f
com.mohandhass28.customer:attr/layout_keyline = 0x7f04029b
com.mohandhass28.customer:attr/buttonBarNegativeButtonStyle = 0x7f040082
com.mohandhass28.customer:color/common_google_signin_btn_text_light_focused = 0x7f060041
com.mohandhass28.customer:attr/saturation = 0x7f040381
com.mohandhass28.customer:attr/panelBackground = 0x7f040335
com.mohandhass28.customer:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f130325
com.mohandhass28.customer:attr/autoSizePresetSizes = 0x7f040040
com.mohandhass28.customer:attr/materialAlertDialogTheme = 0x7f0402c9
com.mohandhass28.customer:attr/colorControlHighlight = 0x7f0400e7
com.mohandhass28.customer:attr/materialAlertDialogButtonSpacerVisibility = 0x7f0402c8
com.mohandhass28.customer:id/glide_custom_view_target_tag = 0x7f0a012d
com.mohandhass28.customer:attr/maskedWalletDetailsButtonTextAppearance = 0x7f0402c2
com.mohandhass28.customer:attr/maskedWalletDetailsBackground = 0x7f0402c0
com.mohandhass28.customer:attr/textAppearanceHeadline2 = 0x7f040411
com.mohandhass28.customer:attr/listLayout = 0x7f0402ab
com.mohandhass28.customer:attr/marginRightSystemWindowInsets = 0x7f0402be
com.mohandhass28.customer:attr/marginLeftSystemWindowInsets = 0x7f0402bd
com.mohandhass28.customer:attr/marginHorizontal = 0x7f0402bc
com.mohandhass28.customer:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1300d4
com.mohandhass28.customer:id/design_menu_item_action_area_stub = 0x7f0a00b4
com.mohandhass28.customer:attr/mapId = 0x7f0402ba
com.mohandhass28.customer:string/exo_track_surround = 0x7f12009a
com.mohandhass28.customer:attr/logoScaleType = 0x7f0402b9
com.mohandhass28.customer:attr/logoDescription = 0x7f0402b8
com.mohandhass28.customer:attr/failureImage = 0x7f0401c9
com.mohandhass28.customer:attr/checkedIcon = 0x7f0400aa
com.mohandhass28.customer:attr/verticalOffset = 0x7f04048a
com.mohandhass28.customer:attr/liteMode = 0x7f0402b5
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral30 = 0x7f0600b2
com.mohandhass28.customer:attr/indicatorColor = 0x7f040228
com.mohandhass28.customer:id/search_go_btn = 0x7f0a01ff
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_primary_container = 0x7f06016f
com.mohandhass28.customer:id/action_bar_container = 0x7f0a0040
com.mohandhass28.customer:attr/layout_constraintEnd_toStartOf = 0x7f040273
com.mohandhass28.customer:attr/actionBarStyle = 0x7f040005
com.mohandhass28.customer:attr/boxCornerRadiusTopEnd = 0x7f040079
com.mohandhass28.customer:xml/standalone_badge_gravity_bottom_start = 0x7f15000c
com.mohandhass28.customer:id/autofill_inline_suggestion_end_icon = 0x7f0a0067
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f1303c1
com.mohandhass28.customer:attr/fontProviderQuery = 0x7f0401f5
com.mohandhass28.customer:dimen/abc_text_size_subhead_material = 0x7f07004d
com.mohandhass28.customer:attr/listPreferredItemPaddingLeft = 0x7f0402b2
com.mohandhass28.customer:color/m3_sys_color_dark_on_secondary = 0x7f060151
com.mohandhass28.customer:layout/splash_screen_view = 0x7f0d008f
com.mohandhass28.customer:drawable/ic_m3_chip_close = 0x7f080113
com.mohandhass28.customer:attr/flow_lastHorizontalStyle = 0x7f0401e4
com.mohandhass28.customer:attr/counterOverflowTextColor = 0x7f040133
com.mohandhass28.customer:attr/chipIconTint = 0x7f0400b9
com.mohandhass28.customer:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f1303eb
com.mohandhass28.customer:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0700dc
com.mohandhass28.customer:string/catalyst_perf_monitor = 0x7f120039
com.mohandhass28.customer:attr/floatingActionButtonSurfaceStyle = 0x7f0401d9
com.mohandhass28.customer:attr/endIconMode = 0x7f0401a1
com.mohandhass28.customer:drawable/abc_list_pressed_holo_light = 0x7f08002f
com.mohandhass28.customer:attr/borderlessButtonStyle = 0x7f04006e
com.mohandhass28.customer:attr/navigationContentDescription = 0x7f040319
com.mohandhass28.customer:id/CropOverlayView = 0x7f0a0004
com.mohandhass28.customer:attr/listChoiceBackgroundIndicator = 0x7f0402a6
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f13003a
com.mohandhass28.customer:color/m3_selection_control_button_tint = 0x7f06013e
com.mohandhass28.customer:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f08012b
com.mohandhass28.customer:color/m3_ref_palette_dynamic_primary30 = 0x7f0600cc
com.mohandhass28.customer:attr/listMenuViewStyle = 0x7f0402ac
com.mohandhass28.customer:color/m3_ref_palette_secondary10 = 0x7f060124
com.mohandhass28.customer:color/bright_foreground_material_dark = 0x7f060026
com.mohandhass28.customer:id/bottom = 0x7f0a0071
com.mohandhass28.customer:dimen/m3_sys_elevation_level1 = 0x7f070136
com.mohandhass28.customer:attr/behavior_overlapTop = 0x7f040069
com.mohandhass28.customer:attr/buttonTint = 0x7f04008d
com.mohandhass28.customer:attr/textAppearanceBody1 = 0x7f040406
com.mohandhass28.customer:string/m3_ref_typeface_brand_medium = 0x7f1200c8
com.mohandhass28.customer:id/end_padder = 0x7f0a00d3
com.mohandhass28.customer:color/design_error = 0x7f060060
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f1303ba
com.mohandhass28.customer:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f13016a
com.mohandhass28.customer:attr/actionTextColorAlpha = 0x7f040022
com.mohandhass28.customer:attr/liftOnScroll = 0x7f0402a0
com.mohandhass28.customer:style/Base.V24.Theme.Material3.Light = 0x7f1300a9
com.mohandhass28.customer:drawable/assets_resimages_5 = 0x7f080077
com.mohandhass28.customer:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f130108
com.mohandhass28.customer:id/tv_sub_item = 0x7f0a0267
com.mohandhass28.customer:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.mohandhass28.customer:attr/boxStrokeColor = 0x7f04007b
com.mohandhass28.customer:attr/imageAspectRatio = 0x7f040222
com.mohandhass28.customer:attr/labelBehavior = 0x7f040255
com.mohandhass28.customer:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f130080
com.mohandhass28.customer:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f0701dc
com.mohandhass28.customer:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f070156
com.mohandhass28.customer:style/Widget.Material3.Button.IconButton = 0x7f130380
com.mohandhass28.customer:color/bright_foreground_disabled_material_light = 0x7f060023
com.mohandhass28.customer:attr/barrierAllowsGoneWidgets = 0x7f04005f
com.mohandhass28.customer:attr/layout_optimizationLevel = 0x7f04029c
com.mohandhass28.customer:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f13004d
com.mohandhass28.customer:attr/buttonBarStyle = 0x7f040085
com.mohandhass28.customer:attr/layout_insetEdge = 0x7f04029a
com.mohandhass28.customer:attr/listPreferredItemHeight = 0x7f0402ae
com.mohandhass28.customer:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.mohandhass28.customer:drawable/abc_seekbar_thumb_material = 0x7f080040
com.mohandhass28.customer:id/fit = 0x7f0a0119
com.mohandhass28.customer:attr/layout_constraintTop_creator = 0x7f040287
com.mohandhass28.customer:string/abc_menu_shift_shortcut_label = 0x7f12000e
com.mohandhass28.customer:attr/layout_constraintLeft_toLeftOf = 0x7f04027f
com.mohandhass28.customer:attr/layout_goneMarginLeft = 0x7f040296
com.mohandhass28.customer:id/view_tree_saved_state_registry_owner = 0x7f0a0275
com.mohandhass28.customer:color/m3_ref_palette_dynamic_secondary90 = 0x7f0600df
com.mohandhass28.customer:attr/textAppearanceSmallPopupMenu = 0x7f040425
com.mohandhass28.customer:attr/colorPrimarySurface = 0x7f0400fe
com.mohandhass28.customer:drawable/exo_ic_subtitle_off = 0x7f0800cf
com.mohandhass28.customer:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f13020b
com.mohandhass28.customer:attr/progressBarAutoRotateInterval = 0x7f040359
com.mohandhass28.customer:dimen/mtrl_calendar_dialog_background_inset = 0x7f0701c4
com.mohandhass28.customer:color/exo_white = 0x7f060076
com.mohandhass28.customer:attr/layout_goneMarginEnd = 0x7f040295
com.mohandhass28.customer:attr/layout_constraintWidth_min = 0x7f04028f
com.mohandhass28.customer:layout/material_radial_view_group = 0x7f0d0050
com.mohandhass28.customer:attr/cardForegroundColor = 0x7f04009d
com.mohandhass28.customer:attr/tooltipStyle = 0x7f040462
com.mohandhass28.customer:attr/colorPrimaryDark = 0x7f0400fc
com.mohandhass28.customer:style/Theme.AppCompat.Light.DarkActionBar = 0x7f130248
com.mohandhass28.customer:id/one = 0x7f0a01b6
com.mohandhass28.customer:dimen/abc_action_button_min_width_overflow_material = 0x7f07000f
com.mohandhass28.customer:attr/layout_constraintWidth_max = 0x7f04028e
com.mohandhass28.customer:attr/tabTextAppearance = 0x7f0403fe
com.mohandhass28.customer:drawable/btn_checkbox_checked_mtrl = 0x7f08008f
com.mohandhass28.customer:attr/splitTrack = 0x7f0403bc
com.mohandhass28.customer:attr/layout_constraintVertical_chainStyle = 0x7f04028b
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_on_surface = 0x7f06017f
com.mohandhass28.customer:id/accessibility_label = 0x7f0a0037
com.mohandhass28.customer:dimen/mtrl_calendar_days_of_week_height = 0x7f0701c3
com.mohandhass28.customer:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0401bf
com.mohandhass28.customer:attr/materialThemeOverlay = 0x7f0402e8
com.mohandhass28.customer:attr/materialCardViewOutlinedStyle = 0x7f0402e1
com.mohandhass28.customer:id/satellite = 0x7f0a01ef
com.mohandhass28.customer:attr/autofillInlineSuggestionEndIconStyle = 0x7f040046
com.mohandhass28.customer:attr/layout_constraintVertical_bias = 0x7f04028a
com.mohandhass28.customer:attr/colorOutline = 0x7f0400f9
com.mohandhass28.customer:dimen/design_appbar_elevation = 0x7f070063
com.mohandhass28.customer:attr/windowFixedWidthMinor = 0x7f04049c
com.mohandhass28.customer:attr/cameraTargetLat = 0x7f040096
com.mohandhass28.customer:attr/chipIconSize = 0x7f0400b8
com.mohandhass28.customer:attr/layout_constraintHorizontal_weight = 0x7f04027d
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f13006f
com.mohandhass28.customer:attr/layout_constraintStart_toStartOf = 0x7f040285
com.mohandhass28.customer:style/Widget.MaterialComponents.PopupMenu = 0x7f13044b
com.mohandhass28.customer:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f08001c
com.mohandhass28.customer:attr/itemMaxLines = 0x7f04023b
com.mohandhass28.customer:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1300c1
com.mohandhass28.customer:attr/layout_constraintHeight_min = 0x7f040279
com.mohandhass28.customer:string/exo_controls_hide = 0x7f12006f
com.mohandhass28.customer:id/chip1 = 0x7f0a008d
com.mohandhass28.customer:attr/layout_constraintHeight_default = 0x7f040277
com.mohandhass28.customer:color/abc_secondary_text_material_light = 0x7f060012
com.mohandhass28.customer:id/buy_with_google = 0x7f0a007e
com.mohandhass28.customer:dimen/default_dimension = 0x7f070062
com.mohandhass28.customer:id/navigation_bar_item_small_label_view = 0x7f0a01aa
com.mohandhass28.customer:attr/uiZoomGestures = 0x7f040482
com.mohandhass28.customer:attr/layout_behavior = 0x7f040264
com.mohandhass28.customer:attr/minHeight = 0x7f0402f8
com.mohandhass28.customer:attr/layout_constraintBottom_toTopOf = 0x7f04026d
com.mohandhass28.customer:id/split_action_bar = 0x7f0a021b
com.mohandhass28.customer:attr/layout_constraintBottom_creator = 0x7f04026b
com.mohandhass28.customer:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0700e5
com.mohandhass28.customer:raw/rzp_config_checkout = 0x7f110012
com.mohandhass28.customer:color/wallet_secondary_text_holo_dark = 0x7f060282
com.mohandhass28.customer:attr/textAppearanceDisplayMedium = 0x7f04040e
com.mohandhass28.customer:color/design_default_color_primary = 0x7f06005a
com.mohandhass28.customer:id/accessibility_collection_item = 0x7f0a0015
com.mohandhass28.customer:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f070226
com.mohandhass28.customer:attr/postSplashScreenTheme = 0x7f040352
com.mohandhass28.customer:attr/layout_constraintTop_toTopOf = 0x7f040289
com.mohandhass28.customer:dimen/material_textinput_max_width = 0x7f070188
com.mohandhass28.customer:attr/layout_constraintBaseline_creator = 0x7f040269
com.mohandhass28.customer:attr/contentPaddingRight = 0x7f04011c
com.mohandhass28.customer:dimen/m3_btn_disabled_translation_z = 0x7f0700e9
com.mohandhass28.customer:style/Widget.MaterialComponents.Button = 0x7f13040c
com.mohandhass28.customer:attr/pressedStateOverlayImage = 0x7f040357
com.mohandhass28.customer:style/TextAppearance.Design.Suffix = 0x7f1301fd
com.mohandhass28.customer:attr/layout_constrainedWidth = 0x7f040268
com.mohandhass28.customer:attr/layout_collapseMode = 0x7f040265
com.mohandhass28.customer:attr/curveFit = 0x7f04015f
com.mohandhass28.customer:color/m3_hint_foreground = 0x7f0600a2
com.mohandhass28.customer:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f070020
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f130030
com.mohandhass28.customer:attr/layoutDescription = 0x7f04025f
com.mohandhass28.customer:style/ExoMediaButton.Next = 0x7f130123
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Body2 = 0x7f130018
com.mohandhass28.customer:color/mtrl_btn_stroke_color_selector = 0x7f060225
com.mohandhass28.customer:attr/tabSelectedTextColor = 0x7f0403fc
com.mohandhass28.customer:attr/layout_goneMarginBottom = 0x7f040294
com.mohandhass28.customer:color/abc_search_url_text_normal = 0x7f06000e
com.mohandhass28.customer:attr/windowActionModeOverlay = 0x7f040498
com.mohandhass28.customer:id/centerInside = 0x7f0a0084
com.mohandhass28.customer:attr/textAppearanceBody2 = 0x7f040407
com.mohandhass28.customer:attr/latLngBoundsSouthWestLatitude = 0x7f04025c
com.mohandhass28.customer:attr/expandedTitleTextColor = 0x7f0401b9
com.mohandhass28.customer:id/dragUp = 0x7f0a00c5
com.mohandhass28.customer:attr/dragThreshold = 0x7f040183
com.mohandhass28.customer:id/SYM = 0x7f0a000e
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0600bc
com.mohandhass28.customer:attr/labelVisibilityMode = 0x7f040257
com.mohandhass28.customer:attr/navigationIcon = 0x7f04031a
com.mohandhass28.customer:attr/lStar = 0x7f040254
com.mohandhass28.customer:attr/contentInsetRight = 0x7f040115
com.mohandhass28.customer:color/m3_ref_palette_neutral0 = 0x7f0600fc
com.mohandhass28.customer:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f130469
com.mohandhass28.customer:drawable/exo_icon_repeat_one = 0x7f0800db
com.mohandhass28.customer:attr/fastScrollHorizontalTrackDrawable = 0x7f0401cd
com.mohandhass28.customer:attr/keylines = 0x7f040253
com.mohandhass28.customer:color/material_dynamic_tertiary90 = 0x7f060200
com.mohandhass28.customer:attr/navigationIconTint = 0x7f04031b
com.mohandhass28.customer:attr/collapsingToolbarLayoutLargeStyle = 0x7f0400dd
com.mohandhass28.customer:id/accessibility_role = 0x7f0a0039
com.mohandhass28.customer:dimen/abc_list_item_height_large_material = 0x7f070030
com.mohandhass28.customer:style/Theme.FullScreenDialog = 0x7f130259
com.mohandhass28.customer:attr/keyPositionType = 0x7f040251
com.mohandhass28.customer:color/m3_ref_palette_secondary0 = 0x7f060123
com.mohandhass28.customer:layout/abc_screen_simple_overlay_action_mode = 0x7f0d0016
com.mohandhass28.customer:color/material_dynamic_secondary60 = 0x7f0601f0
com.mohandhass28.customer:dimen/abc_text_size_large_material = 0x7f070048
com.mohandhass28.customer:attr/keep_content_on_player_reset = 0x7f040250
com.mohandhass28.customer:color/design_dark_default_color_on_background = 0x7f060048
com.mohandhass28.customer:color/mtrl_navigation_item_text_color = 0x7f060244
com.mohandhass28.customer:attr/unplayed_color = 0x7f040483
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f130433
com.mohandhass28.customer:style/Widget.Material3.Chip.Suggestion = 0x7f130398
com.mohandhass28.customer:attr/closeIcon = 0x7f0400ce
com.mohandhass28.customer:attr/sizePercent = 0x7f0403b2
com.mohandhass28.customer:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1300c2
com.mohandhass28.customer:drawable/paused_in_debugger_background = 0x7f08015a
com.mohandhass28.customer:attr/itemVerticalPadding = 0x7f04024f
com.mohandhass28.customer:attr/yearTodayStyle = 0x7f0404a7
com.mohandhass28.customer:attr/itemTextColor = 0x7f04024e
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1301db
com.mohandhass28.customer:attr/tickColorInactive = 0x7f040445
com.mohandhass28.customer:attr/maskedWalletDetailsButtonBackground = 0x7f0402c1
com.mohandhass28.customer:attr/cameraTilt = 0x7f040098
com.mohandhass28.customer:string/menubar_description = 0x7f1200ec
com.mohandhass28.customer:dimen/abc_list_item_height_small_material = 0x7f070032
com.mohandhass28.customer:styleable/Snackbar = 0x7f140082
com.mohandhass28.customer:attr/itemStrokeColor = 0x7f040249
com.mohandhass28.customer:attr/itemShapeInsetEnd = 0x7f040245
com.mohandhass28.customer:dimen/tooltip_vertical_padding = 0x7f070277
com.mohandhass28.customer:attr/itemShapeInsetBottom = 0x7f040244
com.mohandhass28.customer:color/wallet_bright_foreground_disabled_holo_light = 0x7f060276
com.mohandhass28.customer:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_simplelineicons = 0x7f11000f
com.mohandhass28.customer:attr/itemPaddingTop = 0x7f04023f
com.mohandhass28.customer:color/notification_material_background_media_default_color = 0x7f060258
com.mohandhass28.customer:attr/tabMaxWidth = 0x7f0403f2
com.mohandhass28.customer:attr/actionViewClass = 0x7f040023
com.mohandhass28.customer:styleable/MaterialCardView = 0x7f140058
com.mohandhass28.customer:attr/iconTint = 0x7f04021f
com.mohandhass28.customer:attr/itemHorizontalPadding = 0x7f040236
com.mohandhass28.customer:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0701f0
com.mohandhass28.customer:dimen/mtrl_btn_pressed_z = 0x7f0701b0
com.mohandhass28.customer:id/off = 0x7f0a01b3
com.mohandhass28.customer:attr/alphabeticModifiers = 0x7f040030
com.mohandhass28.customer:attr/itemBackground = 0x7f040234
com.mohandhass28.customer:id/triangle = 0x7f0a0266
com.mohandhass28.customer:drawable/assets_resimages_images_10 = 0x7f080079
com.mohandhass28.customer:string/catalyst_hot_reloading = 0x7f120032
com.mohandhass28.customer:dimen/abc_search_view_preferred_width = 0x7f070037
com.mohandhass28.customer:color/mtrl_navigation_bar_colored_item_tint = 0x7f06023e
com.mohandhass28.customer:attr/thumbTextPadding = 0x7f040440
com.mohandhass28.customer:color/m3_card_ripple_color = 0x7f060088
com.mohandhass28.customer:attr/isAutofillInlineSuggestionTheme = 0x7f04022f
com.mohandhass28.customer:style/TextAppearance.Material3.TitleLarge = 0x7f13021d
com.mohandhass28.customer:id/accessibility_custom_action_13 = 0x7f0a001b
com.mohandhass28.customer:attr/appTheme = 0x7f040037
com.mohandhass28.customer:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1300cc
com.mohandhass28.customer:attr/tabTextColor = 0x7f0403ff
com.mohandhass28.customer:id/exo_vr = 0x7f0a0108
com.mohandhass28.customer:attr/colorOnPrimary = 0x7f0400ef
com.mohandhass28.customer:attr/trackCornerRadius = 0x7f04046d
com.mohandhass28.customer:style/ExoStyledControls.Button.Center = 0x7f130136
com.mohandhass28.customer:style/AlertDialog.AppCompat.Light = 0x7f130001
com.mohandhass28.customer:attr/implementationMode = 0x7f040225
com.mohandhass28.customer:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f070246
com.mohandhass28.customer:string/abc_menu_delete_shortcut_label = 0x7f12000a
com.mohandhass28.customer:color/material_dynamic_neutral_variant60 = 0x7f0601d6
com.mohandhass28.customer:attr/cornerFamilyTopLeft = 0x7f040127
com.mohandhass28.customer:attr/indicatorInset = 0x7f04022b
com.mohandhass28.customer:style/ExoStyledControls.TimeBar = 0x7f13013c
com.mohandhass28.customer:attr/color = 0x7f0400e1
com.mohandhass28.customer:layout/select_dialog_multichoice_material = 0x7f0d008c
com.mohandhass28.customer:attr/imageButtonStyle = 0x7f040224
com.mohandhass28.customer:color/androidx_core_ripple_material_light = 0x7f06001b
com.mohandhass28.customer:attr/actionButtonStyle = 0x7f04000b
com.mohandhass28.customer:attr/expandedTitleTextAppearance = 0x7f0401b8
com.mohandhass28.customer:id/deltaRelative = 0x7f0a00b1
com.mohandhass28.customer:attr/iconifiedByDefault = 0x7f040221
com.mohandhass28.customer:id/src_over = 0x7f0a0221
com.mohandhass28.customer:drawable/common_google_signin_btn_icon_dark = 0x7f080098
com.mohandhass28.customer:attr/iconStartPadding = 0x7f04021e
com.mohandhass28.customer:color/design_fab_stroke_top_inner_color = 0x7f060066
com.mohandhass28.customer:attr/actionModeCloseButtonStyle = 0x7f040011
com.mohandhass28.customer:attr/alertDialogTheme = 0x7f04002d
com.mohandhass28.customer:color/button_material_dark = 0x7f06002c
com.mohandhass28.customer:attr/hoveredFocusedTranslationZ = 0x7f040218
com.mohandhass28.customer:dimen/mtrl_slider_widget_height = 0x7f070237
com.mohandhass28.customer:dimen/exo_icon_size = 0x7f07009d
com.mohandhass28.customer:attr/horizontalOffsetWithText = 0x7f040217
com.mohandhass28.customer:attr/cropBorderCornerThickness = 0x7f04013d
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.Light = 0x7f1302f8
com.mohandhass28.customer:attr/homeAsUpIndicator = 0x7f040214
com.mohandhass28.customer:color/m3_ref_palette_dynamic_primary50 = 0x7f0600ce
com.mohandhass28.customer:attr/show_previous_button = 0x7f0403a5
com.mohandhass28.customer:color/abc_background_cache_hint_selector_material_light = 0x7f060001
com.mohandhass28.customer:attr/cropBorderLineColor = 0x7f04013e
com.mohandhass28.customer:attr/counterTextAppearance = 0x7f040134
com.mohandhass28.customer:id/accessibility_custom_action_1 = 0x7f0a0017
com.mohandhass28.customer:attr/motionTarget = 0x7f040314
com.mohandhass28.customer:dimen/test_navigation_bar_elevation = 0x7f07026a
com.mohandhass28.customer:string/pick_image_camera = 0x7f120119
com.mohandhass28.customer:attr/defaultQueryHint = 0x7f04016f
com.mohandhass28.customer:drawable/googleg_standard_color_18 = 0x7f080103
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f06016c
com.mohandhass28.customer:string/abc_capital_off = 0x7f120006
com.mohandhass28.customer:id/edittext_dropdown_noneditable = 0x7f0a00cf
com.mohandhass28.customer:attr/hideOnContentScroll = 0x7f04020c
com.mohandhass28.customer:style/ExoStyledControls.Button.Bottom.Settings = 0x7f130133
com.mohandhass28.customer:dimen/mtrl_high_ripple_focused_alpha = 0x7f070203
com.mohandhass28.customer:drawable/exo_styled_controls_audiotrack = 0x7f0800ea
com.mohandhass28.customer:attr/hideMotionSpec = 0x7f04020b
com.mohandhass28.customer:style/Widget.AppCompat.EditText = 0x7f13032b
com.mohandhass28.customer:string/mtrl_picker_announce_current_selection = 0x7f1200f4
com.mohandhass28.customer:color/mtrl_filled_icon_tint = 0x7f06023b
com.mohandhass28.customer:attr/materialCalendarMonthNavigationButton = 0x7f0402db
com.mohandhass28.customer:attr/hideAnimationBehavior = 0x7f04020a
com.mohandhass28.customer:dimen/mtrl_tooltip_minHeight = 0x7f07024b
com.mohandhass28.customer:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f140034
com.mohandhass28.customer:style/ExoStyledControls.TimeText.Separator = 0x7f130140
com.mohandhass28.customer:attr/headerLayout = 0x7f040204
com.mohandhass28.customer:attr/gapBetweenBars = 0x7f0401ff
com.mohandhass28.customer:attr/editTextStyle = 0x7f040197
com.mohandhass28.customer:style/Base.CardView = 0x7f130010
com.mohandhass28.customer:id/graph = 0x7f0a0133
com.mohandhass28.customer:dimen/mtrl_calendar_day_corner = 0x7f0701bd
com.mohandhass28.customer:dimen/exo_styled_progress_margin_bottom = 0x7f0700b6
com.mohandhass28.customer:attr/expandedTitleMarginEnd = 0x7f0401b5
com.mohandhass28.customer:attr/fragmentMode = 0x7f0401fc
com.mohandhass28.customer:attr/itemMinHeight = 0x7f04023c
com.mohandhass28.customer:attr/colorPrimaryInverse = 0x7f0400fd
com.mohandhass28.customer:dimen/m3_badge_vertical_offset = 0x7f0700d7
com.mohandhass28.customer:attr/fontWeight = 0x7f0401f9
com.mohandhass28.customer:drawable/assets_paymentimg_success = 0x7f080065
com.mohandhass28.customer:attr/minHideDelay = 0x7f0402f9
com.mohandhass28.customer:attr/actionDropDownStyle = 0x7f04000c
com.mohandhass28.customer:attr/fontProviderCerts = 0x7f0401f1
com.mohandhass28.customer:attr/gestureInsetBottomIgnored = 0x7f040200
com.mohandhass28.customer:attr/chipMinTouchTargetSize = 0x7f0400bc
com.mohandhass28.customer:dimen/design_snackbar_elevation = 0x7f070086
com.mohandhass28.customer:string/catalyst_hot_reloading_auto_disable = 0x7f120033
com.mohandhass28.customer:color/material_dynamic_secondary20 = 0x7f0601ec
com.mohandhass28.customer:attr/maskedWalletDetailsLogoImageType = 0x7f0402c4
com.mohandhass28.customer:attr/titlePositionInterpolator = 0x7f040457
com.mohandhass28.customer:id/material_timepicker_container = 0x7f0a0178
com.mohandhass28.customer:attr/scrimVisibleHeightTrigger = 0x7f040386
com.mohandhass28.customer:styleable/NavigationRailView = 0x7f14006c
com.mohandhass28.customer:attr/maxAcceleration = 0x7f0402ec
com.mohandhass28.customer:drawable/avd_show_password = 0x7f08008e
com.mohandhass28.customer:animator/fragment_close_exit = 0x7f020004
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_primary = 0x7f060184
com.mohandhass28.customer:style/Widget.AppCompat.Button.Small = 0x7f130323
com.mohandhass28.customer:style/TextAppearance.Design.HelperText = 0x7f1301f8
com.mohandhass28.customer:style/ExoStyledControls.Button.Center.Next = 0x7f130138
com.mohandhass28.customer:attr/flow_verticalStyle = 0x7f0401ec
com.mohandhass28.customer:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1302cf
com.mohandhass28.customer:attr/textAppearanceBodySmall = 0x7f04040a
com.mohandhass28.customer:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f070241
com.mohandhass28.customer:dimen/mtrl_low_ripple_focused_alpha = 0x7f070208
com.mohandhass28.customer:attr/flow_lastVerticalBias = 0x7f0401e5
com.mohandhass28.customer:id/fingerprint_description = 0x7f0a0115
com.mohandhass28.customer:attr/singleLine = 0x7f0403b0
com.mohandhass28.customer:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f0701d2
com.mohandhass28.customer:attr/boxBackgroundColor = 0x7f040074
com.mohandhass28.customer:attr/flow_horizontalStyle = 0x7f0401e2
com.mohandhass28.customer:dimen/abc_list_item_padding_horizontal_material = 0x7f070033
com.mohandhass28.customer:color/m3_ref_palette_secondary40 = 0x7f060128
com.mohandhass28.customer:id/button_text = 0x7f0a007a
com.mohandhass28.customer:attr/cornerSize = 0x7f04012b
com.mohandhass28.customer:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f0701d5
com.mohandhass28.customer:color/material_dynamic_tertiary99 = 0x7f060202
com.mohandhass28.customer:attr/flow_horizontalAlign = 0x7f0401df
com.mohandhass28.customer:integer/material_motion_duration_long_1 = 0x7f0b0023
com.mohandhass28.customer:attr/flow_firstVerticalStyle = 0x7f0401de
com.mohandhass28.customer:attr/flow_firstHorizontalStyle = 0x7f0401dc
com.mohandhass28.customer:style/Base.Widget.Material3.ActionMode = 0x7f1300f4
com.mohandhass28.customer:attr/contentDescription = 0x7f040111
com.mohandhass28.customer:string/exo_controls_fastforward_description = 0x7f12006c
com.mohandhass28.customer:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f070179
com.mohandhass28.customer:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0700de
com.mohandhass28.customer:attr/paddingEnd = 0x7f04032f
com.mohandhass28.customer:attr/searchViewStyle = 0x7f04038e
com.mohandhass28.customer:color/m3_ref_palette_tertiary40 = 0x7f060135
com.mohandhass28.customer:attr/motionDurationMedium1 = 0x7f040306
com.mohandhass28.customer:attr/fabCradleVerticalOffset = 0x7f0401c5
com.mohandhass28.customer:color/abc_primary_text_disable_only_material_dark = 0x7f060009
com.mohandhass28.customer:attr/layout_scrollEffect = 0x7f04029d
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral95 = 0x7f0600b9
com.mohandhass28.customer:attr/titleMarginEnd = 0x7f040453
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f13029f
com.mohandhass28.customer:string/exo_track_surround_7_point_1 = 0x7f12009c
com.mohandhass28.customer:attr/overlayImage = 0x7f04032c
com.mohandhass28.customer:layout/exo_list_divider = 0x7f0d0035
com.mohandhass28.customer:id/exo_rew = 0x7f0a00fc
com.mohandhass28.customer:attr/contentInsetLeft = 0x7f040114
com.mohandhass28.customer:attr/floatingActionButtonSecondaryStyle = 0x7f0401d7
com.mohandhass28.customer:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0401d4
com.mohandhass28.customer:drawable/exo_icon_shuffle_off = 0x7f0800dd
com.mohandhass28.customer:attr/floatingActionButtonLargeStyle = 0x7f0401d3
com.mohandhass28.customer:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0401d2
com.mohandhass28.customer:dimen/mtrl_fab_translation_z_pressed = 0x7f070201
com.mohandhass28.customer:color/m3_ref_palette_dynamic_secondary20 = 0x7f0600d8
com.mohandhass28.customer:attr/percentHeight = 0x7f04033f
com.mohandhass28.customer:attr/firstBaselineToTopHeight = 0x7f0401d0
com.mohandhass28.customer:attr/scaleType = 0x7f040382
com.mohandhass28.customer:attr/chipMinHeight = 0x7f0400bb
com.mohandhass28.customer:attr/voiceIcon = 0x7f04048f
com.mohandhass28.customer:id/buttonPanel = 0x7f0a0079
com.mohandhass28.customer:attr/colorPrimaryContainer = 0x7f0400fb
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_outline = 0x7f060183
com.mohandhass28.customer:attr/fadeDuration = 0x7f0401c8
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1301da
com.mohandhass28.customer:drawable/abc_ic_ab_back_material = 0x7f08001b
com.mohandhass28.customer:attr/fabSize = 0x7f0401c7
com.mohandhass28.customer:attr/listDividerAlertDialog = 0x7f0402a9
com.mohandhass28.customer:attr/singleSelection = 0x7f0403b1
com.mohandhass28.customer:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0401bc
com.mohandhass28.customer:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0401bb
com.mohandhass28.customer:attr/elevationOverlayAccentColor = 0x7f040199
com.mohandhass28.customer:styleable/PlayerControlView = 0x7f140070
com.mohandhass28.customer:attr/waveShape = 0x7f040494
com.mohandhass28.customer:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f130190
com.mohandhass28.customer:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f130052
com.mohandhass28.customer:color/browser_actions_text_color = 0x7f06002a
com.mohandhass28.customer:drawable/rns_rounder_top_corners_shape = 0x7f08015f
com.mohandhass28.customer:attr/icon = 0x7f040219
com.mohandhass28.customer:attr/latLngBoundsNorthEastLatitude = 0x7f04025a
com.mohandhass28.customer:anim/rns_fade_in = 0x7f01002b
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f13030d
com.mohandhass28.customer:attr/colorError = 0x7f0400e9
com.mohandhass28.customer:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f130105
com.mohandhass28.customer:attr/behavior_skipCollapsed = 0x7f04006c
com.mohandhass28.customer:attr/errorTextColor = 0x7f0401ae
com.mohandhass28.customer:layout/material_timepicker = 0x7f0d0054
com.mohandhass28.customer:attr/customDimension = 0x7f040163
com.mohandhass28.customer:color/design_default_color_error = 0x7f060054
com.mohandhass28.customer:attr/errorTextAppearance = 0x7f0401ad
com.mohandhass28.customer:string/exo_download_paused = 0x7f120088
com.mohandhass28.customer:dimen/notification_media_narrow_margin = 0x7f070256
com.mohandhass28.customer:attr/errorIconTintMode = 0x7f0401ac
com.mohandhass28.customer:layout/exo_player_view = 0x7f0d0037
com.mohandhass28.customer:attr/enforceMaterialTheme = 0x7f0401a4
com.mohandhass28.customer:id/activity_chooser_view_content = 0x7f0a0050
com.mohandhass28.customer:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f080023
com.mohandhass28.customer:color/mtrl_btn_ripple_color = 0x7f060224
com.mohandhass28.customer:attr/barrierMargin = 0x7f040061
com.mohandhass28.customer:anim/rns_no_animation_250 = 0x7f010037
com.mohandhass28.customer:attr/endIconTintMode = 0x7f0401a3
com.mohandhass28.customer:attr/useCompatPadding = 0x7f040484
com.mohandhass28.customer:style/Widget.Material3.Button.OutlinedButton = 0x7f130381
com.mohandhass28.customer:color/mtrl_chip_text_color = 0x7f060232
com.mohandhass28.customer:attr/endIconCheckable = 0x7f04019e
com.mohandhass28.customer:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.mohandhass28.customer:color/m3_dynamic_default_color_primary_text = 0x7f06009b
com.mohandhass28.customer:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f02001f
com.mohandhass28.customer:attr/flow_maxElementsWrap = 0x7f0401e7
com.mohandhass28.customer:attr/indeterminateProgressStyle = 0x7f040227
com.mohandhass28.customer:attr/errorContentDescription = 0x7f0401a8
com.mohandhass28.customer:id/browser_actions_menu_view = 0x7f0a0077
com.mohandhass28.customer:id/async = 0x7f0a0062
com.mohandhass28.customer:styleable/MenuGroup = 0x7f140061
com.mohandhass28.customer:string/exit_fullscreen_mode = 0x7f120068
com.mohandhass28.customer:dimen/exo_small_icon_padding_horizontal = 0x7f0700aa
com.mohandhass28.customer:attr/chipStyle = 0x7f0400c4
com.mohandhass28.customer:color/secondary_text_disabled_material_light = 0x7f060267
com.mohandhass28.customer:id/listMode = 0x7f0a0164
com.mohandhass28.customer:color/biometric_error_color = 0x7f060021
com.mohandhass28.customer:layout/text_view_with_line_height_from_layout = 0x7f0d009d
com.mohandhass28.customer:attr/editTextColor = 0x7f040196
com.mohandhass28.customer:color/m3_button_background_color_selector = 0x7f060080
com.mohandhass28.customer:attr/itemIconTint = 0x7f04023a
com.mohandhass28.customer:attr/duration = 0x7f040193
com.mohandhass28.customer:id/accessibility_custom_action_8 = 0x7f0a0034
com.mohandhass28.customer:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1303cf
com.mohandhass28.customer:attr/region_widthLessThan = 0x7f040369
com.mohandhass28.customer:string/m3_ref_typeface_brand_regular = 0x7f1200c9
com.mohandhass28.customer:attr/fabAlignmentMode = 0x7f0401c1
com.mohandhass28.customer:color/switch_thumb_normal_material_light = 0x7f06026e
com.mohandhass28.customer:attr/coordinatorLayoutStyle = 0x7f040123
com.mohandhass28.customer:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1303d2
com.mohandhass28.customer:id/rn_frame_file = 0x7f0a01de
com.mohandhass28.customer:attr/roundBottomEnd = 0x7f040373
com.mohandhass28.customer:attr/actionBarSize = 0x7f040003
com.mohandhass28.customer:attr/colorPrimaryVariant = 0x7f0400ff
com.mohandhass28.customer:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f130397
com.mohandhass28.customer:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1300d3
com.mohandhass28.customer:anim/catalyst_fade_in = 0x7f010018
com.mohandhass28.customer:attr/actionBarTabBarStyle = 0x7f040006
com.mohandhass28.customer:drawable/exo_controls_vr = 0x7f0800be
com.mohandhass28.customer:attr/drawerLayoutCornerSize = 0x7f04018f
com.mohandhass28.customer:attr/snackbarStyle = 0x7f0403b5
com.mohandhass28.customer:attr/drawableTopCompat = 0x7f04018d
com.mohandhass28.customer:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1300ef
com.mohandhass28.customer:color/abc_search_url_text_selected = 0x7f060010
com.mohandhass28.customer:attr/drawableTint = 0x7f04018b
com.mohandhass28.customer:attr/cropMinCropResultWidthPX = 0x7f04014e
com.mohandhass28.customer:id/design_navigation_view = 0x7f0a00b6
com.mohandhass28.customer:id/wrap = 0x7f0a027f
com.mohandhass28.customer:attr/drawableSize = 0x7f040189
com.mohandhass28.customer:id/autoCompleteToStart = 0x7f0a0066
com.mohandhass28.customer:color/m3_ref_palette_neutral_variant60 = 0x7f060110
com.mohandhass28.customer:dimen/abc_button_inset_horizontal_material = 0x7f070012
com.mohandhass28.customer:attr/textColorSearchUrl = 0x7f04042c
com.mohandhass28.customer:dimen/mtrl_progress_circular_size = 0x7f070222
com.mohandhass28.customer:attr/drawableStartCompat = 0x7f04018a
com.mohandhass28.customer:attr/behavior_halfExpandedRatio = 0x7f040067
com.mohandhass28.customer:attr/bottomNavigationStyle = 0x7f040071
com.mohandhass28.customer:attr/dividerThickness = 0x7f04017f
com.mohandhass28.customer:color/m3_ref_palette_neutral20 = 0x7f0600ff
com.mohandhass28.customer:id/test_checkbox_android_button_tint = 0x7f0a0240
com.mohandhass28.customer:id/ratio = 0x7f0a01d1
com.mohandhass28.customer:attr/backgroundColor = 0x7f04004b
com.mohandhass28.customer:attr/colorSurfaceVariant = 0x7f040106
com.mohandhass28.customer:attr/dividerInsetStart = 0x7f04017d
com.mohandhass28.customer:attr/cropBorderLineThickness = 0x7f04013f
com.mohandhass28.customer:attr/displayOptions = 0x7f040178
com.mohandhass28.customer:dimen/material_text_size_dp = 0x7f070183
com.mohandhass28.customer:color/m3_ref_palette_dynamic_primary90 = 0x7f0600d2
com.mohandhass28.customer:color/mtrl_textinput_disabled_color = 0x7f060252
com.mohandhass28.customer:attr/colorSecondaryContainer = 0x7f040102
com.mohandhass28.customer:color/common_google_signin_btn_text_dark_disabled = 0x7f06003b
com.mohandhass28.customer:drawable/abc_popup_background_mtrl_mult = 0x7f080037
com.mohandhass28.customer:attr/dividerInsetEnd = 0x7f04017c
com.mohandhass28.customer:attr/selectableItemBackground = 0x7f040390
com.mohandhass28.customer:dimen/abc_star_small = 0x7f07003d
com.mohandhass28.customer:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f13014e
com.mohandhass28.customer:color/m3_sys_color_dark_on_secondary_container = 0x7f060152
com.mohandhass28.customer:attr/layout_goneMarginRight = 0x7f040297
com.mohandhass28.customer:dimen/m3_card_elevated_hovered_z = 0x7f070102
com.mohandhass28.customer:attr/checkedTextViewStyle = 0x7f0400b1
com.mohandhass28.customer:color/material_timepicker_modebutton_tint = 0x7f060222
com.mohandhass28.customer:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f13014d
com.mohandhass28.customer:attr/suggestionRowLayout = 0x7f0403de
com.mohandhass28.customer:style/MaterialAlertDialog.Material3.Title.Text = 0x7f13014a
com.mohandhass28.customer:attr/listPreferredItemPaddingEnd = 0x7f0402b1
com.mohandhass28.customer:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f1303b3
com.mohandhass28.customer:style/Widget.AppCompat.RatingBar.Small = 0x7f13034f
com.mohandhass28.customer:layout/mtrl_picker_text_input_date_range = 0x7f0d0074
com.mohandhass28.customer:drawable/assets_img_ordalanetransparentlogo03 = 0x7f080063
com.mohandhass28.customer:attr/centerIfNoTextEnabled = 0x7f0400a2
com.mohandhass28.customer:attr/deltaPolarAngle = 0x7f040172
com.mohandhass28.customer:attr/default_artwork = 0x7f040171
com.mohandhass28.customer:attr/floatingActionButtonPrimaryStyle = 0x7f0401d6
com.mohandhass28.customer:dimen/m3_navigation_item_vertical_padding = 0x7f070123
com.mohandhass28.customer:attr/colorControlActivated = 0x7f0400e6
com.mohandhass28.customer:style/Widget.AppCompat.ListMenuView = 0x7f130343
com.mohandhass28.customer:string/progressbar_description = 0x7f12011c
com.mohandhass28.customer:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0701fc
com.mohandhass28.customer:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f130420
com.mohandhass28.customer:attr/itemActiveIndicatorStyle = 0x7f040233
com.mohandhass28.customer:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f13028b
com.mohandhass28.customer:attr/defaultState = 0x7f040170
com.mohandhass28.customer:attr/buttonCompat = 0x7f040086
com.mohandhass28.customer:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f070141
com.mohandhass28.customer:attr/indicatorDirectionCircular = 0x7f040229
com.mohandhass28.customer:dimen/abc_panel_menu_list_width = 0x7f070034
com.mohandhass28.customer:color/exo_black_opacity_70 = 0x7f060071
com.mohandhass28.customer:attr/autofillInlineSuggestionSubtitle = 0x7f040048
com.mohandhass28.customer:attr/titleMarginBottom = 0x7f040452
com.mohandhass28.customer:attr/customPixelDimension = 0x7f040167
com.mohandhass28.customer:drawable/rn_edit_text_material = 0x7f08015e
com.mohandhass28.customer:drawable/abc_btn_borderless_material = 0x7f080008
com.mohandhass28.customer:attr/customNavigationLayout = 0x7f040166
com.mohandhass28.customer:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f130338
com.mohandhass28.customer:attr/shortcutMatchRequired = 0x7f040399
com.mohandhass28.customer:style/Theme.Material3.Dark.NoActionBar = 0x7f130262
com.mohandhass28.customer:drawable/m3_radiobutton_ripple = 0x7f08011f
com.mohandhass28.customer:attr/autoCompleteTextViewStyle = 0x7f04003d
com.mohandhass28.customer:attr/behavior_draggable = 0x7f040064
com.mohandhass28.customer:drawable/assets_profileimgcopy_bottomsheet_notionpng = 0x7f08006d
com.mohandhass28.customer:attr/progressBarStyle = 0x7f04035d
com.mohandhass28.customer:color/m3_sys_color_light_outline = 0x7f06019d
com.mohandhass28.customer:color/m3_sys_color_dark_on_surface_variant = 0x7f060154
com.mohandhass28.customer:attr/customIntegerValue = 0x7f040165
com.mohandhass28.customer:style/TextAppearance.AppCompat.Large = 0x7f1301c5
com.mohandhass28.customer:dimen/abc_dialog_padding_top_material = 0x7f070025
com.mohandhass28.customer:style/Widget.MaterialComponents.ActionMode = 0x7f1303fb
com.mohandhass28.customer:color/abc_primary_text_material_light = 0x7f06000c
com.mohandhass28.customer:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f130416
com.mohandhass28.customer:color/material_harmonized_color_error_container = 0x7f06020b
com.mohandhass28.customer:style/TextAppearance.AppCompat.Body1 = 0x7f1301bb
com.mohandhass28.customer:color/material_dynamic_neutral20 = 0x7f0601c5
com.mohandhass28.customer:color/exo_styled_error_message_background = 0x7f060075
com.mohandhass28.customer:attr/customColorDrawableValue = 0x7f040161
com.mohandhass28.customer:dimen/exo_icon_padding = 0x7f07009b
com.mohandhass28.customer:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f13017b
com.mohandhass28.customer:color/switch_thumb_disabled_material_dark = 0x7f060269
com.mohandhass28.customer:attr/expandedTitleMarginTop = 0x7f0401b7
com.mohandhass28.customer:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1300ad
com.mohandhass28.customer:attr/contentInsetStart = 0x7f040116
com.mohandhass28.customer:styleable/AppCompatTextView = 0x7f140011
com.mohandhass28.customer:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f070128
com.mohandhass28.customer:id/action_bar = 0x7f0a003e
com.mohandhass28.customer:id/textTop = 0x7f0a024a
com.mohandhass28.customer:color/abc_tint_edittext = 0x7f060015
com.mohandhass28.customer:attr/textAppearanceHeadline5 = 0x7f040414
com.mohandhass28.customer:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f130203
com.mohandhass28.customer:attr/passwordToggleContentDescription = 0x7f040338
com.mohandhass28.customer:attr/closeIconEndPadding = 0x7f0400d0
com.mohandhass28.customer:attr/cropperLabelTextSize = 0x7f04015c
com.mohandhass28.customer:attr/colorErrorContainer = 0x7f0400ea
com.mohandhass28.customer:attr/itemStrokeWidth = 0x7f04024a
com.mohandhass28.customer:layout/mtrl_picker_header_toggle = 0x7f0d0072
com.mohandhass28.customer:attr/collapsedTitleTextColor = 0x7f0400db
com.mohandhass28.customer:drawable/exo_ic_pause_circle_filled = 0x7f0800c8
com.mohandhass28.customer:string/exo_controls_play_description = 0x7f120074
com.mohandhass28.customer:attr/cropperLabelTextColor = 0x7f04015b
com.mohandhass28.customer:attr/horizontalOffset = 0x7f040216
com.mohandhass28.customer:id/clockwise = 0x7f0a0097
com.mohandhass28.customer:dimen/def_drawer_elevation = 0x7f070061
com.mohandhass28.customer:id/navigation_bar_item_labels_group = 0x7f0a01a8
com.mohandhass28.customer:attr/behavior_autoHide = 0x7f040062
com.mohandhass28.customer:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.mohandhass28.customer:attr/cropperLabelText = 0x7f04015a
com.mohandhass28.customer:attr/constraints = 0x7f04010f
com.mohandhass28.customer:dimen/test_navigation_bar_text_size = 0x7f070271
com.mohandhass28.customer:attr/colorContainer = 0x7f0400e5
com.mohandhass28.customer:dimen/mtrl_btn_text_btn_padding_right = 0x7f0701b5
com.mohandhass28.customer:attr/state_collapsed = 0x7f0403c6
com.mohandhass28.customer:attr/switchStyle = 0x7f0403e3
com.mohandhass28.customer:color/m3_ref_palette_primary50 = 0x7f06011c
com.mohandhass28.customer:string/path_password_eye_mask_strike_through = 0x7f120116
com.mohandhass28.customer:attr/layout_constraintGuide_percent = 0x7f040276
com.mohandhass28.customer:attr/cropShape = 0x7f040154
com.mohandhass28.customer:layout/paused_in_debugger_view = 0x7f0d0084
com.mohandhass28.customer:color/m3_radiobutton_ripple_tint = 0x7f0600ac
com.mohandhass28.customer:attr/layout_constraintDimensionRatio = 0x7f040271
com.mohandhass28.customer:dimen/mtrl_slider_track_side_padding = 0x7f070235
com.mohandhass28.customer:attr/cropMinCropResultHeightPX = 0x7f04014d
com.mohandhass28.customer:attr/suffixTextColor = 0x7f0403dd
com.mohandhass28.customer:style/Widget.MaterialComponents.NavigationRailView = 0x7f130445
com.mohandhass28.customer:attr/layout_constraintBaseline_toBaselineOf = 0x7f04026a
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f13042e
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0600c0
com.mohandhass28.customer:attr/shapeAppearanceOverlay = 0x7f040397
com.mohandhass28.customer:attr/cropMaxCropResultHeightPX = 0x7f04014a
com.mohandhass28.customer:attr/cropCenterMoveEnabled = 0x7f040140
com.mohandhass28.customer:drawable/assets_profileimgcopy_bottomsheet_copy = 0x7f08006a
com.mohandhass28.customer:color/mtrl_chip_close_icon_tint = 0x7f060230
com.mohandhass28.customer:layout/fps_view = 0x7f0d0042
com.mohandhass28.customer:color/m3_ref_palette_neutral_variant0 = 0x7f060109
com.mohandhass28.customer:drawable/exo_ic_settings = 0x7f0800cb
com.mohandhass28.customer:attr/backgroundInsetTop = 0x7f040050
com.mohandhass28.customer:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f1303b5
com.mohandhass28.customer:string/clear_text_end_icon_content_description = 0x7f120046
com.mohandhass28.customer:attr/cropBorderCornerLength = 0x7f04013b
com.mohandhass28.customer:attr/itemShapeInsetStart = 0x7f040246
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral90 = 0x7f0600b8
com.mohandhass28.customer:styleable/AppCompatImageView = 0x7f14000e
com.mohandhass28.customer:id/exo_error_message = 0x7f0a00e5
com.mohandhass28.customer:color/m3_ref_palette_error100 = 0x7f0600f1
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f1302fd
com.mohandhass28.customer:attr/bar_height = 0x7f04005e
com.mohandhass28.customer:attr/cropBackgroundColor = 0x7f040139
com.mohandhass28.customer:dimen/exo_styled_bottom_bar_margin_top = 0x7f0700ae
com.mohandhass28.customer:attr/counterTextColor = 0x7f040135
com.mohandhass28.customer:attr/ad_marker_width = 0x7f040029
com.mohandhass28.customer:drawable/common_google_signin_btn_text_dark_normal = 0x7f0800a3
com.mohandhass28.customer:attr/listPreferredItemPaddingStart = 0x7f0402b4
com.mohandhass28.customer:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f13028c
com.mohandhass28.customer:attr/navigationMode = 0x7f04031c
com.mohandhass28.customer:layout/abc_action_menu_layout = 0x7f0d0003
com.mohandhass28.customer:drawable/common_google_signin_btn_text_dark_focused = 0x7f0800a2
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f13006a
com.mohandhass28.customer:attr/motionPath = 0x7f040310
com.mohandhass28.customer:id/test_checkbox_app_button_tint = 0x7f0a0241
com.mohandhass28.customer:attr/checkedChip = 0x7f0400a9
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f1303bb
com.mohandhass28.customer:attr/drawableBottomCompat = 0x7f040185
com.mohandhass28.customer:attr/spanCount = 0x7f0403b7
com.mohandhass28.customer:attr/cornerSizeTopRight = 0x7f04012f
com.mohandhass28.customer:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1300eb
com.mohandhass28.customer:drawable/mtrl_popupmenu_background_overlay = 0x7f080137
com.mohandhass28.customer:integer/m3_sys_motion_path = 0x7f0b0022
com.mohandhass28.customer:anim/catalyst_slide_up = 0x7f01001d
com.mohandhass28.customer:attr/helperText = 0x7f040206
com.mohandhass28.customer:color/m3_ref_palette_error90 = 0x7f0600f9
com.mohandhass28.customer:id/edittext_dropdown_editable = 0x7f0a00ce
com.mohandhass28.customer:attr/colorScheme = 0x7f040100
com.mohandhass28.customer:id/exo_overflow_show = 0x7f0a00f1
com.mohandhass28.customer:attr/prefixText = 0x7f040353
com.mohandhass28.customer:attr/actualImageResource = 0x7f040025
com.mohandhass28.customer:dimen/mtrl_progress_circular_size_extra_small = 0x7f070223
com.mohandhass28.customer:attr/cornerSizeBottomLeft = 0x7f04012c
com.mohandhass28.customer:attr/constraintSetStart = 0x7f04010d
com.mohandhass28.customer:id/dimensions = 0x7f0a00b8
com.mohandhass28.customer:attr/actionModeCloseContentDescription = 0x7f040012
com.mohandhass28.customer:style/Widget.Material3.CardView.Elevated = 0x7f13038c
com.mohandhass28.customer:string/mtrl_picker_cancel = 0x7f1200f5
com.mohandhass28.customer:attr/layoutDuringTransition = 0x7f040260
com.mohandhass28.customer:drawable/abc_list_selector_disabled_holo_dark = 0x7f080032
com.mohandhass28.customer:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f07014d
com.mohandhass28.customer:attr/cornerShape = 0x7f04012a
com.mohandhass28.customer:id/fitStart = 0x7f0a011d
com.mohandhass28.customer:color/material_dynamic_secondary100 = 0x7f0601eb
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f060167
com.mohandhass28.customer:attr/fontProviderAuthority = 0x7f0401f0
com.mohandhass28.customer:color/m3_chip_assist_text_color = 0x7f06008a
com.mohandhass28.customer:xml/file_system_provider_paths = 0x7f150002
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f130063
com.mohandhass28.customer:attr/layout_constrainedHeight = 0x7f040267
com.mohandhass28.customer:style/Widget.AppCompat.ActionBar = 0x7f130313
com.mohandhass28.customer:attr/tabSecondaryStyle = 0x7f0403fb
com.mohandhass28.customer:id/rzp_securedpayments = 0x7f0a01ec
com.mohandhass28.customer:dimen/m3_card_stroke_width = 0x7f070105
com.mohandhass28.customer:attr/contentScrim = 0x7f04011f
com.mohandhass28.customer:interpolator/mtrl_linear = 0x7f0c0009
com.mohandhass28.customer:attr/fastScrollVerticalTrackDrawable = 0x7f0401cf
com.mohandhass28.customer:string/exo_download_failed = 0x7f120086
com.mohandhass28.customer:id/test = 0x7f0a023f
com.mohandhass28.customer:attr/contentPaddingStart = 0x7f04011d
com.mohandhass28.customer:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0701eb
com.mohandhass28.customer:attr/contentPaddingBottom = 0x7f040119
com.mohandhass28.customer:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1302d7
com.mohandhass28.customer:drawable/material_ic_edit_black_24dp = 0x7f080128
com.mohandhass28.customer:style/CalendarDatePickerDialog = 0x7f130117
com.mohandhass28.customer:dimen/mtrl_btn_icon_padding = 0x7f0701a8
com.mohandhass28.customer:dimen/abc_dialog_list_padding_top_no_title = 0x7f070021
com.mohandhass28.customer:style/Widget.MaterialComponents.NavigationView = 0x7f13044a
com.mohandhass28.customer:attr/contentInsetStartWithNavigation = 0x7f040117
com.mohandhass28.customer:style/Widget.MaterialComponents.TimePicker = 0x7f130465
com.mohandhass28.customer:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f1303ef
com.mohandhass28.customer:attr/buyButtonWidth = 0x7f040092
com.mohandhass28.customer:id/fill_horizontal = 0x7f0a0111
com.mohandhass28.customer:attr/tickVisible = 0x7f040449
com.mohandhass28.customer:id/decelerate = 0x7f0a00ad
com.mohandhass28.customer:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f13027a
com.mohandhass28.customer:color/m3_calendar_item_disabled_text = 0x7f060085
com.mohandhass28.customer:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020017
com.mohandhass28.customer:attr/commitIcon = 0x7f04010a
com.mohandhass28.customer:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1302e4
com.mohandhass28.customer:integer/hide_password_duration = 0x7f0b000c
com.mohandhass28.customer:array/delay_showing_prompt_models = 0x7f030003
com.mohandhass28.customer:color/abc_search_url_text = 0x7f06000d
com.mohandhass28.customer:layout/abc_action_mode_bar = 0x7f0d0004
com.mohandhass28.customer:drawable/$avd_show_password__1 = 0x7f080004
com.mohandhass28.customer:string/abc_shareactionprovider_share_with = 0x7f120018
com.mohandhass28.customer:attr/itemShapeAppearanceOverlay = 0x7f040242
com.mohandhass28.customer:attr/layout_dodgeInsetEdges = 0x7f040291
com.mohandhass28.customer:attr/layout_constraintRight_creator = 0x7f040281
com.mohandhass28.customer:id/exo_progress_placeholder = 0x7f0a00fa
com.mohandhass28.customer:dimen/mtrl_progress_circular_inset_small = 0x7f070220
com.mohandhass28.customer:style/ExoStyledControls = 0x7f130129
com.mohandhass28.customer:attr/layout_constraintLeft_toRightOf = 0x7f040280
com.mohandhass28.customer:dimen/material_cursor_inset_bottom = 0x7f070171
com.mohandhass28.customer:attr/colorSurfaceInverse = 0x7f040105
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral100 = 0x7f0600b0
com.mohandhass28.customer:color/mtrl_tabs_legacy_text_color_selector = 0x7f06024e
com.mohandhass28.customer:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f1301a0
com.mohandhass28.customer:id/oval = 0x7f0a01b9
com.mohandhass28.customer:animator/mtrl_chip_state_list_anim = 0x7f020016
com.mohandhass28.customer:attr/backgroundInsetBottom = 0x7f04004d
com.mohandhass28.customer:attr/contentPadding = 0x7f040118
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0600c5
com.mohandhass28.customer:dimen/abc_action_bar_default_height_material = 0x7f070002
com.mohandhass28.customer:id/screen = 0x7f0a01f4
com.mohandhass28.customer:attr/showDividers = 0x7f04039d
com.mohandhass28.customer:attr/itemTextAppearanceActive = 0x7f04024c
com.mohandhass28.customer:attr/layout_constraintBottom_toBottomOf = 0x7f04026c
com.mohandhass28.customer:id/rectangleHorizontalOnly = 0x7f0a01d4
com.mohandhass28.customer:id/material_label = 0x7f0a0173
com.mohandhass28.customer:attr/colorOnSurfaceVariant = 0x7f0400f6
com.mohandhass28.customer:attr/colorOnSurface = 0x7f0400f4
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f130031
com.mohandhass28.customer:attr/cornerFamilyTopRight = 0x7f040128
com.mohandhass28.customer:color/mtrl_choice_chip_text_color = 0x7f060235
com.mohandhass28.customer:color/mtrl_calendar_item_stroke_color = 0x7f06022b
com.mohandhass28.customer:id/add = 0x7f0a0051
com.mohandhass28.customer:attr/backgroundInsetStart = 0x7f04004f
com.mohandhass28.customer:attr/backgroundTint = 0x7f040054
com.mohandhass28.customer:attr/colorOnPrimarySurface = 0x7f0400f1
com.mohandhass28.customer:attr/itemIconPadding = 0x7f040238
com.mohandhass28.customer:attr/layout_constraintHorizontal_bias = 0x7f04027b
com.mohandhass28.customer:attr/colorTertiary = 0x7f040108
com.mohandhass28.customer:attr/layout_constraintGuide_begin = 0x7f040274
com.mohandhass28.customer:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f13044e
com.mohandhass28.customer:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f130106
com.mohandhass28.customer:color/m3_dynamic_dark_default_color_secondary_text = 0x7f060097
com.mohandhass28.customer:id/catalyst_redbox_title = 0x7f0a0081
com.mohandhass28.customer:attr/textAppearanceSubtitle1 = 0x7f040426
com.mohandhass28.customer:id/action_bar_title = 0x7f0a0044
com.mohandhass28.customer:id/action_menu_divider = 0x7f0a0049
com.mohandhass28.customer:id/checked = 0x7f0a008b
com.mohandhass28.customer:attr/materialTimePickerStyle = 0x7f0402e9
com.mohandhass28.customer:style/Widget.AppCompat.Light.ActionButton = 0x7f130336
com.mohandhass28.customer:color/m3_elevated_chip_background_color = 0x7f0600a0
com.mohandhass28.customer:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f1303ec
com.mohandhass28.customer:attr/badgeTextColor = 0x7f040059
com.mohandhass28.customer:drawable/exo_ic_skip_previous = 0x7f0800cd
com.mohandhass28.customer:style/Widget.AppCompat.ListView = 0x7f130345
com.mohandhass28.customer:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f060178
com.mohandhass28.customer:attr/collapsingToolbarLayoutLargeSize = 0x7f0400dc
com.mohandhass28.customer:anim/rns_no_animation_350 = 0x7f010038
com.mohandhass28.customer:attr/collapseContentDescription = 0x7f0400d6
com.mohandhass28.customer:attr/closeIconTint = 0x7f0400d3
com.mohandhass28.customer:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.mohandhass28.customer:attr/iconGravity = 0x7f04021b
com.mohandhass28.customer:attr/buttonStyle = 0x7f04008b
com.mohandhass28.customer:attr/layout_constraintHeight_max = 0x7f040278
com.mohandhass28.customer:attr/actionMenuTextAppearance = 0x7f04000e
com.mohandhass28.customer:anim/rns_no_animation_medium = 0x7f010039
com.mohandhass28.customer:attr/fabCradleRoundedCornerRadius = 0x7f0401c4
com.mohandhass28.customer:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f13042a
com.mohandhass28.customer:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f130290
com.mohandhass28.customer:attr/closeIconStartPadding = 0x7f0400d2
com.mohandhass28.customer:drawable/m3_tabs_background = 0x7f080121
com.mohandhass28.customer:attr/closeIconSize = 0x7f0400d1
com.mohandhass28.customer:anim/rns_ios_from_right_background_close = 0x7f010032
com.mohandhass28.customer:attr/environment = 0x7f0401a7
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Menu = 0x7f130027
com.mohandhass28.customer:attr/actionModeCopyDrawable = 0x7f040014
com.mohandhass28.customer:attr/boxStrokeWidthFocused = 0x7f04007e
com.mohandhass28.customer:color/m3_dynamic_dark_highlighted_text = 0x7f060098
com.mohandhass28.customer:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f02001e
com.mohandhass28.customer:anim/rns_default_exit_out = 0x7f010029
com.mohandhass28.customer:color/primary_text_disabled_material_dark = 0x7f06025f
com.mohandhass28.customer:attr/tickColor = 0x7f040443
com.mohandhass28.customer:drawable/abc_scrubber_track_mtrl_alpha = 0x7f08003f
com.mohandhass28.customer:styleable/StateListDrawable = 0x7f140086
com.mohandhass28.customer:attr/clockFaceBackgroundColor = 0x7f0400ca
com.mohandhass28.customer:styleable/NavigationView = 0x7f14006d
com.mohandhass28.customer:anim/rns_fade_out = 0x7f01002c
com.mohandhass28.customer:drawable/node_modules_exporouter_assets_unmatched = 0x7f08013f
com.mohandhass28.customer:attr/thumbColor = 0x7f04043b
com.mohandhass28.customer:layout/exo_styled_settings_list_item = 0x7f0d003d
com.mohandhass28.customer:attr/chipStandaloneStyle = 0x7f0400c0
com.mohandhass28.customer:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f07015b
com.mohandhass28.customer:integer/design_tab_indicator_anim_duration_ms = 0x7f0b0008
com.mohandhass28.customer:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f020018
com.mohandhass28.customer:attr/closeItemLayout = 0x7f0400d5
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f13043b
com.mohandhass28.customer:attr/lastItemDecorated = 0x7f040259
com.mohandhass28.customer:attr/content = 0x7f040110
com.mohandhass28.customer:style/Widget.Material3.MaterialTimePicker = 0x7f1303d0
com.mohandhass28.customer:id/navigation_bar_item_icon_container = 0x7f0a01a6
com.mohandhass28.customer:attr/show_buffering = 0x7f0403a2
com.mohandhass28.customer:color/m3_ref_palette_dynamic_tertiary10 = 0x7f0600e3
com.mohandhass28.customer:id/fingerprint_icon = 0x7f0a0117
com.mohandhass28.customer:attr/materialDisplayDividerStyle = 0x7f0402e5
com.mohandhass28.customer:style/CardView = 0x7f130119
com.mohandhass28.customer:attr/chipGroupStyle = 0x7f0400b5
com.mohandhass28.customer:drawable/abc_ratingbar_material = 0x7f080039
com.mohandhass28.customer:style/TextAppearance.Design.Placeholder = 0x7f1301fa
com.mohandhass28.customer:attr/collapsedSize = 0x7f0400d8
com.mohandhass28.customer:anim/rns_ios_from_left_background_close = 0x7f01002e
com.mohandhass28.customer:attr/showAnimationBehavior = 0x7f04039a
com.mohandhass28.customer:attr/cropFixAspectRatio = 0x7f040143
com.mohandhass28.customer:styleable/CheckedTextView = 0x7f14001d
com.mohandhass28.customer:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f080052
com.mohandhass28.customer:string/switch_role = 0x7f120132
com.mohandhass28.customer:attr/checkedIconVisible = 0x7f0400b0
com.mohandhass28.customer:anim/design_bottom_sheet_slide_in = 0x7f01001e
com.mohandhass28.customer:id/custom = 0x7f0a00a8
com.mohandhass28.customer:style/TextAppearance.AppCompat.Body2 = 0x7f1301bc
com.mohandhass28.customer:color/m3_sys_color_dark_on_error_container = 0x7f06014e
com.mohandhass28.customer:attr/customBoolean = 0x7f040160
com.mohandhass28.customer:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f1302a4
com.mohandhass28.customer:attr/cameraMinZoomPreference = 0x7f040095
com.mohandhass28.customer:attr/checkedIconGravity = 0x7f0400ac
com.mohandhass28.customer:color/design_default_color_secondary = 0x7f06005d
com.mohandhass28.customer:drawable/node_modules_reactnativeratings_dist_images_star = 0x7f080147
com.mohandhass28.customer:id/exo_basic_controls = 0x7f0a00db
com.mohandhass28.customer:attr/cardElevation = 0x7f04009c
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1303cc
com.mohandhass28.customer:color/m3_ref_palette_dynamic_primary60 = 0x7f0600cf
com.mohandhass28.customer:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f130379
com.mohandhass28.customer:color/wallet_dim_foreground_holo_dark = 0x7f06027a
com.mohandhass28.customer:attr/viewInflaterClass = 0x7f04048d
com.mohandhass28.customer:attr/chainUseRtl = 0x7f0400a3
com.mohandhass28.customer:attr/cardViewStyle = 0x7f0400a1
com.mohandhass28.customer:string/close_drawer = 0x7f120047
com.mohandhass28.customer:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f0701cc
com.mohandhass28.customer:id/end_time_text = 0x7f0a00d4
com.mohandhass28.customer:layout/test_chip_zero_corner_radius = 0x7f0d0092
com.mohandhass28.customer:dimen/mtrl_extended_fab_min_width = 0x7f0701f7
com.mohandhass28.customer:attr/cropBorderCornerOffset = 0x7f04013c
com.mohandhass28.customer:anim/mtrl_bottom_sheet_slide_in = 0x7f010023
com.mohandhass28.customer:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge.Top = 0x7f130178
com.mohandhass28.customer:attr/trackColorInactive = 0x7f04046c
com.mohandhass28.customer:attr/cardPreventCornerOverlap = 0x7f04009f
com.mohandhass28.customer:dimen/design_navigation_padding_bottom = 0x7f070081
com.mohandhass28.customer:attr/constraint_referenced_ids = 0x7f04010e
com.mohandhass28.customer:color/call_notification_decline_color = 0x7f06002f
com.mohandhass28.customer:id/peekHeight = 0x7f0a01c3
com.mohandhass28.customer:attr/buttonIconDimen = 0x7f040088
com.mohandhass28.customer:style/TextAppearance.AppCompat.Button = 0x7f1301bd
com.mohandhass28.customer:color/m3_sys_color_light_background = 0x7f06018c
com.mohandhass28.customer:attr/buttonBarPositiveButtonStyle = 0x7f040084
com.mohandhass28.customer:attr/colorAccent = 0x7f0400e2
com.mohandhass28.customer:color/browser_actions_title_color = 0x7f06002b
com.mohandhass28.customer:layout/rzp_magic_base = 0x7f0d0089
com.mohandhass28.customer:attr/chipCornerRadius = 0x7f0400b3
com.mohandhass28.customer:attr/cardBackgroundColor = 0x7f04009a
com.mohandhass28.customer:attr/materialClockStyle = 0x7f0402e4
com.mohandhass28.customer:attr/floatingActionButtonTertiaryStyle = 0x7f0401da
com.mohandhass28.customer:attr/roundingBorderWidth = 0x7f040380
com.mohandhass28.customer:styleable/ExtendedFloatingActionButton = 0x7f140033
com.mohandhass28.customer:attr/cameraMaxZoomPreference = 0x7f040094
com.mohandhass28.customer:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.mohandhass28.customer:dimen/m3_datepicker_elevation = 0x7f07010d
com.mohandhass28.customer:attr/maskedWalletDetailsLogoTextColor = 0x7f0402c5
com.mohandhass28.customer:color/m3_text_button_background_color_selector = 0x7f0601a8
com.mohandhass28.customer:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f130069
com.mohandhass28.customer:attr/maskedWalletDetailsHeaderTextAppearance = 0x7f0402c3
com.mohandhass28.customer:attr/colorOnTertiaryContainer = 0x7f0400f8
com.mohandhass28.customer:attr/colorOnErrorContainer = 0x7f0400ee
com.mohandhass28.customer:styleable/ActionBarLayout = 0x7f140001
com.mohandhass28.customer:attr/buyButtonAppearance = 0x7f04008f
com.mohandhass28.customer:dimen/mtrl_chip_pressed_translation_z = 0x7f0701e8
com.mohandhass28.customer:attr/buttonTintMode = 0x7f04008e
com.mohandhass28.customer:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000d
com.mohandhass28.customer:attr/triggerId = 0x7f040477
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1303c0
com.mohandhass28.customer:attr/layout_constraintWidth_percent = 0x7f040290
com.mohandhass28.customer:attr/buttonStyleSmall = 0x7f04008c
com.mohandhass28.customer:string/mtrl_picker_a11y_next_month = 0x7f1200f2
com.mohandhass28.customer:color/m3_dynamic_default_color_secondary_text = 0x7f06009c
com.mohandhass28.customer:style/Widget.Material3.Button.TextButton.Dialog = 0x7f130384
com.mohandhass28.customer:anim/abc_tooltip_exit = 0x7f01000b
com.mohandhass28.customer:attr/subtitleTextColor = 0x7f0403d9
com.mohandhass28.customer:attr/retryImage = 0x7f04036d
com.mohandhass28.customer:drawable/m3_appbar_background = 0x7f08011d
com.mohandhass28.customer:attr/cropShowCropOverlay = 0x7f040155
com.mohandhass28.customer:attr/buttonPanelSideLayout = 0x7f040089
com.mohandhass28.customer:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f130048
com.mohandhass28.customer:attr/pathMotionArc = 0x7f04033d
com.mohandhass28.customer:styleable/MaterialDivider = 0x7f14005a
com.mohandhass28.customer:dimen/exo_small_icon_width = 0x7f0700ac
com.mohandhass28.customer:attr/buttonGravity = 0x7f040087
com.mohandhass28.customer:attr/customColorValue = 0x7f040162
com.mohandhass28.customer:attr/backgroundTintMode = 0x7f040055
com.mohandhass28.customer:dimen/abc_dropdownitem_text_padding_left = 0x7f07002a
com.mohandhass28.customer:string/abc_toolbar_collapse_description = 0x7f12001a
com.mohandhass28.customer:color/material_dynamic_neutral_variant95 = 0x7f0601da
com.mohandhass28.customer:attr/keyboardIcon = 0x7f040252
com.mohandhass28.customer:attr/cropSaveBitmapToInstanceState = 0x7f040152
com.mohandhass28.customer:drawable/common_google_signin_btn_icon_light_normal_background = 0x7f0800a0
com.mohandhass28.customer:color/m3_navigation_item_text_color = 0x7f0600a9
com.mohandhass28.customer:anim/catalyst_slide_down = 0x7f01001c
com.mohandhass28.customer:color/m3_textfield_indicator_text_color = 0x7f0601ac
com.mohandhass28.customer:attr/chipSpacing = 0x7f0400bd
com.mohandhass28.customer:dimen/abc_action_button_min_height_material = 0x7f07000d
com.mohandhass28.customer:style/ExoMediaButton.Rewind = 0x7f130127
com.mohandhass28.customer:attr/circleCrop = 0x7f0400c6
com.mohandhass28.customer:attr/circleRadius = 0x7f0400c7
com.mohandhass28.customer:animator/mtrl_extended_fab_hide_motion_spec = 0x7f020019
com.mohandhass28.customer:attr/buttonBarButtonStyle = 0x7f040081
com.mohandhass28.customer:drawable/node_modules_reactnativeratings_dist_images_heart = 0x7f080145
com.mohandhass28.customer:attr/layout_goneMarginStart = 0x7f040298
com.mohandhass28.customer:drawable/ic_mtrl_chip_checked_black = 0x7f080115
com.mohandhass28.customer:string/pick_image_gallery = 0x7f12011b
com.mohandhass28.customer:attr/waveVariesBy = 0x7f040495
com.mohandhass28.customer:color/m3_sys_color_dark_on_error = 0x7f06014d
com.mohandhass28.customer:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.mohandhass28.customer:attr/drawableRightCompat = 0x7f040188
com.mohandhass28.customer:color/m3_ref_palette_dynamic_tertiary30 = 0x7f0600e6
com.mohandhass28.customer:attr/boxStrokeErrorColor = 0x7f04007c
com.mohandhass28.customer:dimen/mtrl_card_elevation = 0x7f0701e6
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_on_background = 0x7f060164
com.mohandhass28.customer:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f130146
com.mohandhass28.customer:attr/checkedButton = 0x7f0400a8
com.mohandhass28.customer:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f130155
com.mohandhass28.customer:color/m3_ref_palette_primary80 = 0x7f06011f
com.mohandhass28.customer:anim/rns_fade_to_bottom = 0x7f01002d
com.mohandhass28.customer:attr/customFloatValue = 0x7f040164
com.mohandhass28.customer:attr/boxStrokeWidth = 0x7f04007d
com.mohandhass28.customer:style/Base.AlertDialog.AppCompat.Light = 0x7f13000c
com.mohandhass28.customer:color/m3_ref_palette_primary100 = 0x7f060118
com.mohandhass28.customer:string/exo_controls_settings_description = 0x7f12007c
com.mohandhass28.customer:attr/titleMargins = 0x7f040456
com.mohandhass28.customer:attr/floatingActionButtonStyle = 0x7f0401d8
com.mohandhass28.customer:id/exo_overlay = 0x7f0a00f2
com.mohandhass28.customer:style/Widget.MaterialComponents.ChipGroup = 0x7f13041e
com.mohandhass28.customer:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.mohandhass28.customer:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1301e4
com.mohandhass28.customer:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f130013
com.mohandhass28.customer:attr/autoSizeStepGranularity = 0x7f040041
com.mohandhass28.customer:attr/boxCollapsedPaddingTop = 0x7f040076
com.mohandhass28.customer:anim/design_snackbar_in = 0x7f010020
com.mohandhass28.customer:attr/bottomSheetDialogTheme = 0x7f040072
com.mohandhass28.customer:drawable/design_fab_background = 0x7f0800ac
com.mohandhass28.customer:color/mtrl_chip_background_color = 0x7f06022f
com.mohandhass28.customer:attr/badgeWidePadding = 0x7f04005a
com.mohandhass28.customer:attr/title = 0x7f04044d
com.mohandhass28.customer:attr/haloColor = 0x7f040202
com.mohandhass28.customer:attr/checkedIconMargin = 0x7f0400ad
com.mohandhass28.customer:attr/cropCornerRadius = 0x7f040142
com.mohandhass28.customer:styleable/MaterialAlertDialogTheme = 0x7f140052
com.mohandhass28.customer:string/state_busy_description = 0x7f120126
com.mohandhass28.customer:id/useLogo = 0x7f0a026d
com.mohandhass28.customer:id/icon_only = 0x7f0a014b
com.mohandhass28.customer:attr/rippleColor = 0x7f040370
com.mohandhass28.customer:attr/behavior_fitToContents = 0x7f040066
com.mohandhass28.customer:dimen/mtrl_low_ripple_default_alpha = 0x7f070207
com.mohandhass28.customer:style/Base.Theme.AppCompat = 0x7f13004a
com.mohandhass28.customer:id/mtrl_view_tag_bottom_padding = 0x7f0a01a3
com.mohandhass28.customer:attr/colorBackgroundFloating = 0x7f0400e3
com.mohandhass28.customer:attr/stackFromEnd = 0x7f0403be
com.mohandhass28.customer:string/abc_action_menu_overflow_description = 0x7f120002
com.mohandhass28.customer:drawable/m3_selection_control_ripple = 0x7f080120
com.mohandhass28.customer:style/ExoStyledControls.Button.Bottom.OverflowHide = 0x7f13012f
com.mohandhass28.customer:attr/materialCalendarDay = 0x7f0402d0
com.mohandhass28.customer:layout/notification_template_part_time = 0x7f0d0083
com.mohandhass28.customer:attr/behavior_expandedOffset = 0x7f040065
com.mohandhass28.customer:style/Theme.Material3.Light.BottomSheetDialog = 0x7f13026e
com.mohandhass28.customer:dimen/m3_btn_dialog_btn_min_width = 0x7f0700e6
com.mohandhass28.customer:layout/mtrl_layout_snackbar_include = 0x7f0d0069
com.mohandhass28.customer:attr/crossfade = 0x7f04015d
com.mohandhass28.customer:color/design_snackbar_background_color = 0x7f060069
com.mohandhass28.customer:dimen/material_emphasis_medium = 0x7f070178
com.mohandhass28.customer:attr/buttonSize = 0x7f04008a
com.mohandhass28.customer:attr/actionModePasteDrawable = 0x7f040017
com.mohandhass28.customer:attr/windowActionBarOverlay = 0x7f040497
com.mohandhass28.customer:attr/dividerPadding = 0x7f04017e
com.mohandhass28.customer:style/Widget.MaterialComponents.Button.Icon = 0x7f13040d
com.mohandhass28.customer:color/design_dark_default_color_secondary = 0x7f060050
com.mohandhass28.customer:style/ExoStyledControls.Button.Center.FfwdWithAmount = 0x7f130137
com.mohandhass28.customer:attr/borderWidth = 0x7f04006d
com.mohandhass28.customer:dimen/abc_edit_text_inset_bottom_material = 0x7f07002c
com.mohandhass28.customer:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1303ff
com.mohandhass28.customer:attr/cardCornerRadius = 0x7f04009b
com.mohandhass28.customer:attr/itemShapeFillColor = 0x7f040243
com.mohandhass28.customer:attr/badgeWithTextRadius = 0x7f04005b
com.mohandhass28.customer:attr/cropMinCropWindowHeight = 0x7f04014f
com.mohandhass28.customer:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f1302c6
com.mohandhass28.customer:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f130266
com.mohandhass28.customer:id/animateToStart = 0x7f0a005f
com.mohandhass28.customer:attr/actionOverflowButtonStyle = 0x7f04001f
com.mohandhass28.customer:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.mohandhass28.customer:attr/chipEndPadding = 0x7f0400b4
com.mohandhass28.customer:dimen/mtrl_slider_halo_radius = 0x7f07022e
com.mohandhass28.customer:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f13043d
com.mohandhass28.customer:animator/m3_chip_state_list_anim = 0x7f020011
com.mohandhass28.customer:attr/colorSecondaryVariant = 0x7f040103
com.mohandhass28.customer:string/default_media_controller_time = 0x7f120063
com.mohandhass28.customer:id/action_container = 0x7f0a0045
com.mohandhass28.customer:attr/ambientEnabled = 0x7f040032
com.mohandhass28.customer:attr/cropMaxZoom = 0x7f04014c
com.mohandhass28.customer:drawable/abc_switch_thumb_material = 0x7f080047
com.mohandhass28.customer:attr/flow_verticalAlign = 0x7f0401e9
com.mohandhass28.customer:attr/chipStartPadding = 0x7f0400c1
com.mohandhass28.customer:attr/animationMode = 0x7f040034
com.mohandhass28.customer:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f1300f1
com.mohandhass28.customer:id/accessibility_custom_action_5 = 0x7f0a0031
com.mohandhass28.customer:attr/chipSpacingVertical = 0x7f0400bf
com.mohandhass28.customer:attr/actionModeSelectAllDrawable = 0x7f040019
com.mohandhass28.customer:dimen/cardview_default_radius = 0x7f070058
com.mohandhass28.customer:attr/textAppearanceSubtitle2 = 0x7f040427
com.mohandhass28.customer:attr/autoSizeMaxTextSize = 0x7f04003e
com.mohandhass28.customer:string/exo_download_paused_for_network = 0x7f120089
com.mohandhass28.customer:attr/attributeName = 0x7f04003c
com.mohandhass28.customer:style/TextAppearance.Material3.HeadlineMedium = 0x7f130217
com.mohandhass28.customer:attr/defaultDuration = 0x7f04016e
com.mohandhass28.customer:dimen/highlight_alpha_material_colored = 0x7f0700bc
com.mohandhass28.customer:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f130236
com.mohandhass28.customer:color/mtrl_navigation_item_icon_tint = 0x7f060243
com.mohandhass28.customer:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f1303a7
com.mohandhass28.customer:attr/textAppearanceHeadlineMedium = 0x7f040417
com.mohandhass28.customer:attr/chipStrokeWidth = 0x7f0400c3
com.mohandhass28.customer:attr/animation_enabled = 0x7f040035
com.mohandhass28.customer:drawable/exo_ic_subtitle_on = 0x7f0800d0
com.mohandhass28.customer:attr/touchAnchorSide = 0x7f040466
com.mohandhass28.customer:dimen/exo_setting_width = 0x7f0700a1
com.mohandhass28.customer:attr/actionBarWidgetTheme = 0x7f04000a
com.mohandhass28.customer:attr/autoTransition = 0x7f040043
com.mohandhass28.customer:attr/cropGuidelinesThickness = 0x7f040148
com.mohandhass28.customer:attr/actionBarItemBackground = 0x7f040001
com.mohandhass28.customer:color/material_on_surface_emphasis_high_type = 0x7f060215
com.mohandhass28.customer:attr/animate_relativeTo = 0x7f040033
com.mohandhass28.customer:attr/fragmentStyle = 0x7f0401fd
com.mohandhass28.customer:layout/fingerprint_dialog_layout = 0x7f0d0041
com.mohandhass28.customer:dimen/abc_text_size_small_material = 0x7f07004c
com.mohandhass28.customer:attr/background = 0x7f04004a
com.mohandhass28.customer:id/autoCompleteToEnd = 0x7f0a0065
com.mohandhass28.customer:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f13029d
com.mohandhass28.customer:id/accessibility_custom_action_21 = 0x7f0a0024
com.mohandhass28.customer:attr/materialCardViewStyle = 0x7f0402e2
com.mohandhass28.customer:dimen/m3_btn_max_width = 0x7f0700f3
com.mohandhass28.customer:dimen/exo_icon_horizontal_margin = 0x7f07009a
com.mohandhass28.customer:string/mtrl_timepicker_confirm = 0x7f120111
com.mohandhass28.customer:color/m3_ref_palette_dynamic_secondary80 = 0x7f0600de
com.mohandhass28.customer:style/Widget.Material3.MaterialCalendar.Year = 0x7f1303ca
com.mohandhass28.customer:dimen/mtrl_btn_corner_radius = 0x7f0701a0
com.mohandhass28.customer:id/material_timepicker_view = 0x7f0a017c
com.mohandhass28.customer:anim/rns_slide_in_from_right = 0x7f01003c
com.mohandhass28.customer:drawable/exo_icon_repeat_off = 0x7f0800da
com.mohandhass28.customer:id/mtrl_card_checked_layer_id = 0x7f0a0196
com.mohandhass28.customer:attr/allowStacking = 0x7f04002e
com.mohandhass28.customer:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f13022e
com.mohandhass28.customer:attr/layout_constraintVertical_weight = 0x7f04028c
com.mohandhass28.customer:attr/ad_marker_color = 0x7f040028
com.mohandhass28.customer:attr/layout_constraintGuide_end = 0x7f040275
com.mohandhass28.customer:styleable/ImageFilterView = 0x7f140040
com.mohandhass28.customer:attr/actualImageUri = 0x7f040027
com.mohandhass28.customer:attr/scrimBackground = 0x7f040385
com.mohandhass28.customer:style/Theme.Material3.Light.Dialog = 0x7f13026f
com.mohandhass28.customer:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f07015a
com.mohandhass28.customer:drawable/node_modules_reactnavigation_elements_lib_commonjs_assets_clearicon = 0x7f08014a
com.mohandhass28.customer:string/state_off_description = 0x7f12012c
com.mohandhass28.customer:drawable/compat_splash_screen_no_icon_background = 0x7f0800ab
com.mohandhass28.customer:anim/catalyst_fade_out = 0x7f010019
com.mohandhass28.customer:color/m3_assist_chip_stroke_color = 0x7f06007f
com.mohandhass28.customer:integer/m3_sys_motion_duration_350 = 0x7f0b0018
com.mohandhass28.customer:integer/m3_btn_anim_duration_ms = 0x7f0b000e
com.mohandhass28.customer:drawable/exo_styled_controls_fastforward = 0x7f0800ec
com.mohandhass28.customer:attr/badgeGravity = 0x7f040056
com.mohandhass28.customer:attr/alertDialogStyle = 0x7f04002c
com.mohandhass28.customer:array/exo_controls_playback_speeds = 0x7f030004
com.mohandhass28.customer:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f130049
com.mohandhass28.customer:animator/fragment_fade_enter = 0x7f020005
com.mohandhass28.customer:attr/percentY = 0x7f040342
com.mohandhass28.customer:string/common_google_play_services_unknown_issue = 0x7f120052
com.mohandhass28.customer:attr/actualImageScaleType = 0x7f040026
com.mohandhass28.customer:attr/badgeRadius = 0x7f040057
com.mohandhass28.customer:attr/alertDialogButtonGroupStyle = 0x7f04002a
com.mohandhass28.customer:dimen/material_emphasis_disabled_background = 0x7f070176
com.mohandhass28.customer:attr/actionModeWebSearchDrawable = 0x7f04001e
com.mohandhass28.customer:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1301d3
com.mohandhass28.customer:id/exo_playback_speed = 0x7f0a00f6
com.mohandhass28.customer:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f13016e
com.mohandhass28.customer:attr/arrowShaftLength = 0x7f04003b
com.mohandhass28.customer:attr/foregroundInsidePadding = 0x7f0401fb
com.mohandhass28.customer:dimen/mtrl_btn_padding_right = 0x7f0701ae
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f1302fb
com.mohandhass28.customer:layout/material_clockface_view = 0x7f0d004f
com.mohandhass28.customer:attr/roundTopEnd = 0x7f040378
com.mohandhass28.customer:style/Widget.AppCompat.ButtonBar = 0x7f130324
com.mohandhass28.customer:layout/design_navigation_item = 0x7f0d002c
com.mohandhass28.customer:anim/rns_ios_from_right_foreground_close = 0x7f010034
com.mohandhass28.customer:dimen/design_snackbar_padding_horizontal = 0x7f07008a
com.mohandhass28.customer:attr/actionModeCloseDrawable = 0x7f040013
com.mohandhass28.customer:animator/m3_card_state_list_anim = 0x7f020010
com.mohandhass28.customer:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f130261
com.mohandhass28.customer:id/search_src_text = 0x7f0a0202
com.mohandhass28.customer:anim/design_bottom_sheet_slide_out = 0x7f01001f
com.mohandhass28.customer:attr/actionModePopupWindowStyle = 0x7f040018
com.mohandhass28.customer:color/m3_primary_text_disable_only = 0x7f0600ab
com.mohandhass28.customer:string/combobox_description = 0x7f120049
com.mohandhass28.customer:color/design_default_color_on_primary = 0x7f060057
com.mohandhass28.customer:drawable/exo_styled_controls_shuffle_off = 0x7f0800fa
com.mohandhass28.customer:dimen/design_bottom_navigation_active_item_max_width = 0x7f070064
com.mohandhass28.customer:id/text2 = 0x7f0a0245
com.mohandhass28.customer:attr/layout_constraintCircleRadius = 0x7f040270
com.mohandhass28.customer:attr/actionMenuTextColor = 0x7f04000f
com.mohandhass28.customer:anim/abc_slide_out_top = 0x7f010009
com.mohandhass28.customer:layout/notification_template_part_chronometer = 0x7f0d0082
com.mohandhass28.customer:attr/onTouchUp = 0x7f040329
com.mohandhass28.customer:attr/maxButtonHeight = 0x7f0402ee
com.mohandhass28.customer:anim/abc_popup_enter = 0x7f010003
com.mohandhass28.customer:anim/mtrl_bottom_sheet_slide_out = 0x7f010024
com.mohandhass28.customer:anim/rns_default_exit_in = 0x7f010028
com.mohandhass28.customer:color/dim_foreground_disabled_material_light = 0x7f06006b
com.mohandhass28.customer:drawable/exo_icon_stop = 0x7f0800df
com.mohandhass28.customer:style/ExoMediaButton.VR = 0x7f130128
com.mohandhass28.customer:drawable/assets_dailyessentialsassert_hotal_3 = 0x7f08005a
com.mohandhass28.customer:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f130204
com.mohandhass28.customer:attr/textAppearanceHeadline3 = 0x7f040412
com.mohandhass28.customer:id/graph_wrap = 0x7f0a0134
com.mohandhass28.customer:attr/actionModeCutDrawable = 0x7f040015
com.mohandhass28.customer:attr/scrubber_color = 0x7f040387
com.mohandhass28.customer:attr/behavior_peekHeight = 0x7f04006a
com.mohandhass28.customer:attr/dividerHorizontal = 0x7f04017b
com.mohandhass28.customer:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f1303ac
com.mohandhass28.customer:attr/startIconTintMode = 0x7f0403c4
com.mohandhass28.customer:id/accessibility_custom_action_3 = 0x7f0a002d
com.mohandhass28.customer:dimen/notification_small_icon_background_padding = 0x7f070259
com.mohandhass28.customer:string/mtrl_chip_close_icon_content_description = 0x7f1200ef
com.mohandhass28.customer:attr/actionBarDivider = 0x7f040000
com.mohandhass28.customer:animator/design_appbar_state_list_animator = 0x7f020000
com.mohandhass28.customer:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1300bf
com.mohandhass28.customer:color/design_fab_shadow_start_color = 0x7f060063
com.mohandhass28.customer:attr/actionBarTabStyle = 0x7f040007
com.mohandhass28.customer:attr/iconPadding = 0x7f04021c
com.mohandhass28.customer:attr/collapsingToolbarLayoutMediumSize = 0x7f0400de
com.mohandhass28.customer:attr/fabCustomSize = 0x7f0401c6
com.mohandhass28.customer:animator/mtrl_fab_hide_motion_spec = 0x7f02001c
com.mohandhass28.customer:layout/design_text_input_end_icon = 0x7f0d0032
com.mohandhass28.customer:id/selection_type = 0x7f0a0208
com.mohandhass28.customer:color/m3_ref_palette_primary60 = 0x7f06011d
com.mohandhass28.customer:style/TextAppearance.AppCompat.Medium = 0x7f1301cb
com.mohandhass28.customer:dimen/mtrl_progress_circular_track_thickness_small = 0x7f070228
com.mohandhass28.customer:color/m3_dynamic_dark_default_color_primary_text = 0x7f060096
com.mohandhass28.customer:attr/colorOnSurfaceInverse = 0x7f0400f5
com.mohandhass28.customer:string/radiogroup_description = 0x7f12011e
com.mohandhass28.customer:dimen/splashscreen_icon_size = 0x7f070262
com.mohandhass28.customer:color/notification_icon_bg_color = 0x7f060257
com.mohandhass28.customer:attr/actionModeSplitBackground = 0x7f04001b
com.mohandhass28.customer:attr/textAppearanceLargePopupMenu = 0x7f04041c
com.mohandhass28.customer:dimen/compat_button_padding_horizontal_material = 0x7f07005c
com.mohandhass28.customer:attr/extraMultilineHeightEnabled = 0x7f0401c0
com.mohandhass28.customer:drawable/notification_bg_low_pressed = 0x7f080151
com.mohandhass28.customer:attr/autoSizeMinTextSize = 0x7f04003f
com.mohandhass28.customer:anim/rns_no_animation_20 = 0x7f010036
com.mohandhass28.customer:attr/trackTintMode = 0x7f040471
com.mohandhass28.customer:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f130421
com.mohandhass28.customer:attr/onHide = 0x7f040325
com.mohandhass28.customer:animator/m3_elevated_chip_state_list_anim = 0x7f020012
com.mohandhass28.customer:attr/indicatorDirectionLinear = 0x7f04022a
com.mohandhass28.customer:attr/materialDividerHeavyStyle = 0x7f0402e6
com.mohandhass28.customer:anim/abc_fade_out = 0x7f010001
com.mohandhass28.customer:dimen/m3_fab_translation_z_pressed = 0x7f070118
com.mohandhass28.customer:string/state_on_description = 0x7f12012e
com.mohandhass28.customer:attr/helperTextTextAppearance = 0x7f040208
com.mohandhass28.customer:id/seek_bar = 0x7f0a0204
com.mohandhass28.customer:animator/m3_card_elevated_state_list_anim = 0x7f02000f
com.mohandhass28.customer:color/material_dynamic_tertiary40 = 0x7f0601fb
com.mohandhass28.customer:attr/colorOnError = 0x7f0400ed
com.mohandhass28.customer:dimen/m3_ripple_default_alpha = 0x7f07012d
com.mohandhass28.customer:attr/chipIcon = 0x7f0400b6
com.mohandhass28.customer:color/m3_sys_color_dynamic_dark_on_primary = 0x7f060165
com.mohandhass28.customer:style/Widget.AppCompat.ActionBar.TabBar = 0x7f130315
com.mohandhass28.customer:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f13028f
com.mohandhass28.customer:string/catalyst_loading_from_url = 0x7f120037
com.mohandhass28.customer:id/inspection_slot_table_set = 0x7f0a0151
com.mohandhass28.customer:attr/statusBarBackground = 0x7f0403cb
com.mohandhass28.customer:animator/m3_btn_state_list_anim = 0x7f02000e
com.mohandhass28.customer:attr/titleMargin = 0x7f040451
com.mohandhass28.customer:animator/fragment_open_exit = 0x7f020008
com.mohandhass28.customer:animator/linear_indeterminate_line1_tail_interpolator = 0x7f02000a
com.mohandhass28.customer:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f08003d
com.mohandhass28.customer:styleable/ActionBar = 0x7f140000
com.mohandhass28.customer:dimen/abc_edit_text_inset_top_material = 0x7f07002e
com.mohandhass28.customer:attr/queryPatterns = 0x7f040360
com.mohandhass28.customer:dimen/mtrl_calendar_action_height = 0x7f0701b9
com.mohandhass28.customer:attr/altSrc = 0x7f040031
com.mohandhass28.customer:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f13008b
com.mohandhass28.customer:animator/fragment_open_enter = 0x7f020007
com.mohandhass28.customer:drawable/exo_icon_next = 0x7f0800d5
com.mohandhass28.customer:id/onTouch = 0x7f0a01b5
com.mohandhass28.customer:attr/maxHeight = 0x7f0402f0
com.mohandhass28.customer:anim/rns_slide_in_from_left = 0x7f01003b
com.mohandhass28.customer:attr/motionDebug = 0x7f040303
com.mohandhass28.customer:anim/rns_slide_out_to_bottom = 0x7f01003d
com.mohandhass28.customer:attr/enableEdgeToEdge = 0x7f04019d
com.mohandhass28.customer:drawable/assets_streetfoodsassert_images_rectangle592762 = 0x7f080088
com.mohandhass28.customer:id/pointer_events = 0x7f0a01c8
com.mohandhass28.customer:attr/chipSurfaceColor = 0x7f0400c5
com.mohandhass28.customer:attr/themeLineHeight = 0x7f040439
com.mohandhass28.customer:drawable/assets_unavailable01 = 0x7f080089
com.mohandhass28.customer:color/wallet_highlighted_text_holo_light = 0x7f06027c
com.mohandhass28.customer:attr/backgroundStacked = 0x7f040053
com.mohandhass28.customer:color/design_dark_default_color_on_secondary = 0x7f06004b
com.mohandhass28.customer:animator/mtrl_fab_show_motion_spec = 0x7f02001d
com.mohandhass28.customer:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f130240
com.mohandhass28.customer:dimen/m3_btn_padding_bottom = 0x7f0700f4
com.mohandhass28.customer:attr/errorEnabled = 0x7f0401a9
com.mohandhass28.customer:style/Widget.Material3.AppBarLayout = 0x7f130371
com.mohandhass28.customer:id/book_now = 0x7f0a0070
com.mohandhass28.customer:drawable/mtrl_navigation_bar_item_background = 0x7f080135
com.mohandhass28.customer:anim/rns_fade_from_bottom = 0x7f01002a
com.mohandhass28.customer:attr/flow_lastHorizontalBias = 0x7f0401e3
com.mohandhass28.customer:dimen/design_fab_translation_z_pressed = 0x7f070079
com.mohandhass28.customer:anim/rns_ios_from_right_foreground_open = 0x7f010035
com.mohandhass28.customer:styleable/ClockFaceView = 0x7f140021
com.mohandhass28.customer:drawable/ic_fullscreen_exit_32dp = 0x7f08010e
com.mohandhass28.customer:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f1302f3
com.mohandhass28.customer:attr/helperTextEnabled = 0x7f040207
com.mohandhass28.customer:attr/mock_label = 0x7f0402fe
com.mohandhass28.customer:styleable/MockView = 0x7f140064
com.mohandhass28.customer:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f130014
com.mohandhass28.customer:layout/abc_screen_toolbar = 0x7f0d0017
com.mohandhass28.customer:dimen/m3_btn_text_btn_icon_padding_right = 0x7f0700fa
com.mohandhass28.customer:attr/dialogPreferredPadding = 0x7f040176
com.mohandhass28.customer:attr/layout_constraintCircle = 0x7f04026e
com.mohandhass28.customer:style/TextAppearance.Material3.DisplaySmall = 0x7f130215
com.mohandhass28.customer:attr/arrowHeadLength = 0x7f04003a
com.mohandhass28.customer:dimen/mtrl_shape_corner_size_medium_component = 0x7f07022c
com.mohandhass28.customer:anim/rns_ios_from_left_foreground_close = 0x7f010030
com.mohandhass28.customer:anim/rns_ios_from_left_background_open = 0x7f01002f
com.mohandhass28.customer:dimen/material_cursor_width = 0x7f070173
com.mohandhass28.customer:attr/colorOnTertiary = 0x7f0400f7
com.mohandhass28.customer:id/action_image = 0x7f0a0048
com.mohandhass28.customer:id/info = 0x7f0a0150
com.mohandhass28.customer:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.mohandhass28.customer:anim/fragment_fast_out_extra_slow_in = 0x7f010022
com.mohandhass28.customer:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f130034
com.mohandhass28.customer:anim/abc_slide_in_bottom = 0x7f010006
com.mohandhass28.customer:anim/design_snackbar_out = 0x7f010021
com.mohandhass28.customer:dimen/mtrl_btn_text_btn_icon_padding = 0x7f0701b3
com.mohandhass28.customer:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0600c2
com.mohandhass28.customer:styleable/AppBarLayout_Layout = 0x7f14000c
com.mohandhass28.customer:dimen/test_mtrl_calendar_day_cornerSize = 0x7f070266
com.mohandhass28.customer:attr/itemTextAppearance = 0x7f04024b
com.mohandhass28.customer:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.mohandhass28.customer:styleable/AppCompatEmojiHelper = 0x7f14000d
com.mohandhass28.customer:attr/theme = 0x7f040438
com.mohandhass28.customer:styleable/ThemeEnforcement = 0x7f140093
com.mohandhass28.customer:color/material_dynamic_neutral_variant99 = 0x7f0601db
com.mohandhass28.customer:attr/actionModeStyle = 0x7f04001c
com.mohandhass28.customer:attr/hintTextAppearance = 0x7f040212
com.mohandhass28.customer:attr/chipBackgroundColor = 0x7f0400b2
com.mohandhass28.customer:drawable/exo_controls_fullscreen_exit = 0x7f0800b3
com.mohandhass28.customer:attr/played_color = 0x7f04034c
com.mohandhass28.customer:attr/thumbStrokeColor = 0x7f04043e
com.mohandhass28.customer:animator/mtrl_btn_state_list_anim = 0x7f020013
com.mohandhass28.customer:attr/cornerFamily = 0x7f040124
com.mohandhass28.customer:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.mohandhass28.customer:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f07015e
com.mohandhass28.customer:raw/node_modules_expo_vectoricons_build_vendor_reactnativevectoricons_fonts_evilicons = 0x7f110003
com.mohandhass28.customer:color/abc_color_highlight_material = 0x7f060004
com.mohandhass28.customer:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.mohandhass28.customer:string/state_expanded_description = 0x7f120129
com.mohandhass28.customer:id/textinput_prefix_text = 0x7f0a0252
com.mohandhass28.customer:attr/hide_during_ads = 0x7f04020e
com.mohandhass28.customer:integer/mtrl_chip_anim_duration = 0x7f0b0032
com.mohandhass28.customer:color/primary_dark_material_dark = 0x7f060259
com.mohandhass28.customer:id/withinBounds = 0x7f0a027e
com.mohandhass28.customer:animator/mtrl_extended_fab_state_list_animator = 0x7f02001b
com.mohandhass28.customer:layout/abc_screen_content_include = 0x7f0d0014
com.mohandhass28.customer:dimen/mtrl_badge_with_text_radius = 0x7f070199
com.mohandhass28.customer:attr/height = 0x7f040205
com.mohandhass28.customer:dimen/mtrl_calendar_header_height_fullscreen = 0x7f0701c9
com.mohandhass28.customer:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.mohandhass28.customer:attr/number = 0x7f040322
com.mohandhass28.customer:string/mtrl_picker_invalid_format_example = 0x7f1200fc
com.mohandhass28.customer:color/m3_ref_palette_dynamic_secondary50 = 0x7f0600db
com.mohandhass28.customer:array/crypto_fingerprint_fallback_prefixes = 0x7f030001
com.mohandhass28.customer:string/exo_controls_repeat_one_description = 0x7f120079
com.mohandhass28.customer:attr/actionModeShareDrawable = 0x7f04001a
com.mohandhass28.customer:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f07019d
com.mohandhass28.customer:integer/material_motion_duration_short_1 = 0x7f0b0027
com.mohandhass28.customer:attr/endIconTint = 0x7f0401a2
com.mohandhass28.customer:dimen/m3_navigation_item_icon_padding = 0x7f07011e
com.mohandhass28.customer:attr/searchHintIcon = 0x7f04038c
com.mohandhass28.customer:id/contentPanel = 0x7f0a00a0
com.mohandhass28.customer:attr/autoSizeTextType = 0x7f040042
