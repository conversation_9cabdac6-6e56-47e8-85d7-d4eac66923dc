import AsyncStorage from "@react-native-async-storage/async-storage";
import BackIcon from "@/assets/OrderDetails/backIcon.svg";
import Constants from "expo-constants";
import JsonPrint from "@/core/print_call/json_print";
import LinearGradientComponent from "../../../component/LinearGradientComponent copy";
import React, { useEffect, useState } from "react";
import axios from "axios";
import useTenStackHook2 from "@/hooks/TenStackHook2/TenStackHook2";
import { router } from "expo-router";
import { ActivityIndicator, Text, TouchableOpacity, View } from "react-native";
import { ScrollView } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import {
  OrderItemModel,
  OrderListResponseModel,
} from "@/data/model/order_model/order_list_model";

const index = () => {
  const { ActiveOrder, IsActiveOrderLoading } = GetActiveOrders();
  return (
    <SafeAreaView>
      <ScrollView>
        <View className="items-center justify-center relative mt-6">
          <TouchableOpacity
            className="absolute left-[3%]"
            onPress={() => {
              router.back();
            }}
          >
            <BackIcon />
          </TouchableOpacity>
          <View>
            <Text className="font-[600] text-[26px] leading-[39px]">
              Active Orders
            </Text>
          </View>
        </View>
        <View className="mt-6 px-4">
          {IsActiveOrderLoading ? (
            <>
              <View className="flex-1 items-center justify-center">
                <ActivityIndicator size={"large"} />
              </View>
            </>
          ) : (
            <>
              {Array.isArray(ActiveOrder?.data) &&
                ActiveOrder?.data.map((data, index) => {
                  return <ActiveOrdersComponent key={index} data={data} />;
                })}
            </>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const ActiveOrdersComponent = ({
  data: { order_id, order_amnt, order_invoices, oid },
}: {
  data: OrderItemModel;
}) => {
  return (
    <View className="flex-row space-x-4 mt-5 w-full border-[#D4D4D4] border-[2px]">
      <View className="w-[133px] h-[127px] items-center justify-center rounded-[7px] relative overflow-hidden">
        <LinearGradientComponent color1={"#00660A"} color2={"#02B914"} />
        <Text className="absolute z-20 text-[20px] leading-[30px] font-[400] text-[#FFFFFF]">
          {Array.isArray(order_invoices) &&
            order_invoices.reduce((prv, cur) => {
              return { ...prv, edt: Number(prv.edt) + Number(cur.edt) };
            })?.edt}
          min
        </Text>
      </View>
      <View className="mt-4">
        <View className="space-y-3">
          <Text className="">Order ID:{order_id}</Text>
          <Text className="">₹ {order_amnt}</Text>
        </View>
        <TouchableOpacity
          onPress={() => {
            router.push({
              pathname: "/home/<USER>/deliverydetails",
              params: { id: oid },
            });
          }}
          className="mt-6 bg-[#A4F4AC] h-[26px] items-center justify-center border-[1px] border-[#00660A] rounded-[4px]"
        >
          <Text className="text-[12px] leading-[18px] font-[600] text-[#00660A]">
            Track your order
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export const GetActiveOrders = () => {
  const { data, isLoading } = useTenStackHook2<
    {
      type: number;
    },
    OrderListResponseModel
  >({
    key: "order_list_new",
    canSave: true,
    endpoint: "customer/order/order_list_new",
    params: { type: 2 },
  });
  useEffect(() => {}, []);
  JsonPrint({
    ActiveOrder: data,
    IsActiveOrderLoading: isLoading,
    message: "ActiveOrder",
  });
  return { ActiveOrder: data, IsActiveOrderLoading: isLoading };
};
export default index;
