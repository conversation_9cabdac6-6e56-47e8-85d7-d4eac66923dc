import LinearGradientComponent from "@/component/LinearGradientComponent copy";
import LoadingScreen from "@/component/LoadingScreen/LoadingScreen";
import React, { useEffect } from "react";
import useDefaultAddressHook from "@/hooks/DefaultAddress/useDefaultAddressHook";
import useShopActionsHook from "@/hooks/shopHooks/useShopActionsHook";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Redirect, Stack, router } from "expo-router";
import { ActivityIndicator, StatusBar, View } from "react-native";
import { Text as RNText } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { Provider, useSelector } from "react-redux";
import { selectAuth } from "@/redux/auth/meno";
import { store } from "@/redux/store";

const RNTextAny = RNText as any;
const oldRender = RNTextAny.render;
RNTextAny.render = function (...args: any[]) {
  const origin = oldRender.call(this, ...args);
  return React.cloneElement(origin, {
    allowFontScaling: false,
    ...origin.props,
  });
};
const AppContent = () => {
  const IsLogin = useSelector(selectAuth);
  const { isLoading, AuthCheck } = useShopActionsHook();
  useEffect(() => {
    const AsynceLoad = async () => {
      await AuthCheck();
    };
    AsynceLoad();
  }, []);

  return (
    <>
      {isLoading ? (
        <>
          <LoadingScreen />
        </>
      ) : (
        <>
          <StatusBar barStyle={"dark-content"} backgroundColor={"#000"} />
          <Stack screenOptions={{ headerShown: false }} />
          {IsLogin ? (
            <>
              {(() => {
                router.dismissAll();
                return <Redirect href={"/home/<USER>/home"} />;
              })()}
            </>
          ) : (
            <Redirect href={"/auth/createaccount"} />
          )}
        </>
      )}
    </>
  );
};

const queryClient = new QueryClient();

const _layout = () => {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Provider store={store}>
        <QueryClientProvider client={queryClient}>
          <AppContent />
        </QueryClientProvider>
      </Provider>
    </GestureHandlerRootView>
  );
};

export default _layout;
