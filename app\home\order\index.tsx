import React, { useEffect } from "react";
import { router, useLocalSearchParams } from "expo-router";
import { Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

const Index = () => {
  const { id } = useLocalSearchParams();

  useEffect(() => {
    const timeouts = setTimeout(() => {
      router.dismiss(1);
      router.replace({
        pathname: "home/order/deliverydetails",
        params: { id: id },
      });
    }, 2000);
    return () => {
      clearTimeout(timeouts);
    };
  }, []);
  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 items-center justify-center">
        <View className="items-center">
          <View>
            <Text className="font-[500] text-[32px] leading-[48px]">
              Woohoo!
            </Text>
          </View>
          <View className="mt-2">
            <Text className="font-[500] text-[18px] leading-[27px] text-[#627164]">
              You’re order has been successfully placed
            </Text>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default Index;
