import AddressComponents from "../../../component/AddressComponents";
import Arrow from "../../../assets/BillAssert/left.svg";
import BackIcon from "../../../assets/OrderDetails/backIcon.svg";
import BagIcon from "../../../assets/BillAssert/Icon/bag.svg";
import CoinIcon from "../../../assets/BillAssert/Icon/coin-stack.svg";
import ContactBook from "../../../assets/icons/Map/contactBook.svg";
import CouponsIcon from "../../../assets/BillAssert/coupons.svg";
import ForwordIcon from "../../../assets/BillAssert/forwordIcon.svg";
import GreenHomeIcon from "../../../assets/BillAssert/greenHomeIcon.svg";
import HomeIcon from "../../../assets/BillAssert/home.svg";
import Icon1 from "../../../assets/1.svg";
import Icon2 from "../../../assets/2.svg";
import Icon3 from "../../../assets/3.svg";
import Icon4 from "../../../assets/4.svg";
import JsonPrint from "@/core/print_call/json_print";
import PhoneIcon from "../../../assets/BillAssert/cell.svg";
import PlusIcon from "../../../assets/BillAssert/pkus.svg";
import RBSheet from "react-native-raw-bottom-sheet";
import React, { forwardRef, useEffect, useRef, useState } from "react";
import ReferIcon from "../../../assets/BillAssert/refer.svg";
import RsIcon from "@/assets/images/CardImages/RsIcon.svg";
import Scooter from "../../../assets/BillAssert/Icon/scooter.svg";
import Smartphone from "../../../assets/BillAssert/Icon/smartphone.svg";
import TextinputWithEdit from "../../../component/TextinputWithEdit";
import Vector from "../../../assets/BillAssert/Vector.svg";
import useCartHook from "@/hooks/useCartHook/useCartHook";
import useDefaultAddressHook from "@/hooks/DefaultAddress/useDefaultAddressHook";
import useSetApiData from "../../../hooks/useSetApiData";
import useTenStackHook from "@/hooks/TenStackHook/TenStackHook";
import useTenStackHook2 from "@/hooks/TenStackHook2/TenStackHook2";
import useTenStackMutate from "@/hooks/TenStackMutateHook/useTenStackMutate";
import useTenStackMutateD from "@/hooks/useTenStackMutateD/useTenStackMutate";
import { Controller, useForm } from "react-hook-form";
import { SafeAreaView } from "react-native-safe-area-context";
import { QuantityField } from "../shop/[id]";
import { defaultAddressModel } from "@/data/model/customer_address_model/default_address_model";
import { defaultAddressEntity } from "@/domain/entity/customer_address_entity/default_address_entity";

import {
  router,
  useFocusEffect,
  useNavigation,
  usePathname,
  useRouter,
} from "expo-router";

/* eslint-disable react/display-name */

import AddRemoveCard, {
  ModifyNormalAddRemoveCard,
  VariantIdandOptionsIdType,
} from "../../../component/AddRemoveCard/AddRemoveCard";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  ActivityIndicator,
  Alert,
} from "react-native";

const Index = () => {
  const AddressrefRBSheet = useRef<typeof RBSheet>(null);
  const EditContactrefRBSheet = useRef<typeof RBSheet>(null);
  const { data: checkoutData, isFetching: checkoutIsLoading } = useTenStackHook<
    any,
    any
  >({
    key: "checkout",
    canSave: true,
    endpoint: "product/checkout_page",
  });
  const {
    cartData: { data: cartlist, isFetching },
  } = useCartHook();
  const [ActiveDeliveryPartners, setActiveDeliveryPartners] = useState(0);
  const [VariantId, setVariant] = useState<VariantIdandOptionsIdType[]>([]);
  const [OptionsId, setOptions] = useState<VariantIdandOptionsIdType[]>([]);
  const pathname = usePathname();

  const { data: defaultAddress, isLoading: defaultAddressLoading } =
    useDefaultAddressHook();

  useEffect(() => {
    const path = pathname.split("/");
    if (cartlist?.cart_data?.length === 0 && path[path.length - 1] === "bill") {
      router.back();
    }
  }, [isFetching]);

  useFocusEffect(() => {
    const path = pathname.split("/");
    if (cartlist?.cart_data?.length === 0 && path[path.length - 1] === "bill") {
      router.back();
    }
  });

  return (
    <SafeAreaView className="flex-1 ">
      <ScrollView>
        <View className="px-4 flex-row items-center justify-start relative mt-6">
          <TouchableOpacity
            className="mr-4"
            onPress={() => {
              router.back();
            }}
          >
            <BackIcon />
          </TouchableOpacity>
          <View className="flex-row justify-between flex-1">
            <View>
              <Text className="font-[600] text-[20px] leading-[30px]">
                Cart
              </Text>
            </View>
          </View>
        </View>
        {cartlist?.cart_data.map((items, index) => {
          if (Boolean(items?.product_details.is_unpackage_prd)) {
            return null;
          }
          return (
            <>
              <ModifyNormalAddRemoveCard
                key={items?.product_details?.id}
                product_id={items.product_details.id}
                id={items.product_details.id}
                name={items?.product_details?.name}
                price={Number(items?.product_details?.price)}
                unit={items?.product_details?.unit}
                VariantId={items.product_variant.map((item) => ({
                  id: Number(item.id),
                  quantity: Number(item.quantity),
                }))}
                OptionsId={items.product_options.map((item) => ({
                  id: Number(item.id),
                  quantity: Number(item.quantity),
                }))}
                image={items?.product_details?.image}
                qty={Number(items?.product_details?.qty)}
                product_quantity={items?.quantity}
                setFunctions={setVariant}
                quantity={items?.quantity}
                item_total={checkoutData?.cart_details?.final_amount as number}
              />
            </>
          );
        })}
        {cartlist?.cart_data.map((items, index) => {
          return (
            <>
              {Boolean(items?.product_details.is_unpackage_prd) ? (
                <UnpackageProductComponent
                  id={items?.product_details?.id}
                  image={items?.product_details?.image}
                  units={items?.product_details?.unit}
                  name={items?.product_details?.name}
                  amount={items?.product_details?.price}
                  quantity={items.product_details?.unpackage_prd_qty}
                  key={items?.product_details?.id}
                />
              ) : (
                <>
                  {Array.isArray(items?.product_variant) &&
                    items?.product_variant?.map((variant, index) => {
                      return (
                        <View key={index}>
                          <AddRemoveCard
                            key={variant?.id}
                            product_id={items.product_details.id}
                            id={variant?.id}
                            name={variant?.variant_name}
                            image={variant?.image}
                            price={variant?.price}
                            unit={variant?.unit}
                            VariantId={items.product_variant.map((item) => ({
                              id: Number(item.id),
                              quantity: Number(item.quantity),
                            }))}
                            OptionsId={items.product_options.map((item) => ({
                              id: Number(item.id),
                              quantity: Number(item.quantity),
                            }))}
                            qty={variant?.qty}
                            product_quantity={items?.quantity}
                            setFunctions={setVariant}
                            quantity={variant?.quantity}
                            item_total={
                              checkoutData?.cart_details?.final_amount as number
                            }
                          />
                        </View>
                      );
                    })}
                  {Array.isArray(items?.product_options) &&
                    items?.product_options?.map(
                      (options: any, index: number) => {
                        return (
                          <View key={index}>
                            <AddRemoveCard
                              key={options?.id}
                              product_id={items?.product_details?.id}
                              id={options?.id}
                              name={options?.name}
                              price={options?.price}
                              image={options?.image}
                              unit={options?.unit}
                              VariantId={items.product_variant.map((item) => ({
                                id: Number(item.id),
                                quantity: Number(item.quantity),
                              }))}
                              OptionsId={items.product_options.map((item) => ({
                                id: Number(item.id),
                                quantity: Number(item.quantity),
                              }))}
                              qty={options?.qty}
                              product_quantity={items?.quantity}
                              setFunctions={setOptions}
                              quantity={options?.quantity}
                              item_total={
                                checkoutData?.cart_details
                                  ?.final_amount as number
                              }
                            />
                          </View>
                        );
                      },
                    )}
                </>
              )}
            </>
          );
        })}

        <BillData />

        <View className="px-4 mt-4">
          <TouchableOpacity
            onPress={() => {
              router.navigate("/home/<USER>/coupons");
            }}
            className="p-4 flex-row items-center justify-between space-x-2 border-[1px] border-[#E9EAE9]"
          >
            <View className="flex-row items-center space-x-2">
              <CouponsIcon />
              <Text>View all payment coupons</Text>
            </View>
            <View>
              <Arrow />
            </View>
          </TouchableOpacity>
          <TouchableOpacity className="border-[1px] border-[#E9EAE9]">
            <TouchableOpacity className="px-4 pt-4 flex-row items-center justify-between space-x-2 ">
              <View className="flex-row items-center space-x-2">
                <ReferIcon />
                <Text>Apply referral code if any</Text>
              </View>
              <View>
                <Arrow />
              </View>
            </TouchableOpacity>
            <View className="flex-row h-[45px] items-center m-4 px-4 border-[1px] border-[#E9EAE9]">
              <TextInput className="flex-1" placeholder="Enter coupon code" />
              <TouchableOpacity>
                <Text className="text-[#00660A]">Check</Text>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        </View>
        <View className="px-4 mt-4">
          <TouchableOpacity
            onPress={() => {
              (AddressrefRBSheet.current as any)?.open();
            }}
            className="p-4 flex-row items-center space-x-2 border-[1px] border-[#E9EAE9]"
          >
            <HomeIcon />
            <View className="space-y-1">
              <Text>Delivery at Home</Text>
              {checkoutIsLoading ? (
                <></>
              ) : (
                <>
                  {checkoutData?.cart_details?.delivery_address?.length ===
                  0 ? (
                    <>
                      <Text>Please Add Delivery Address</Text>
                    </>
                  ) : (
                    <>
                      <Text>
                        {checkoutData?.cart_details?.delivery_address?.address}
                      </Text>
                    </>
                  )}
                </>
              )}
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              (EditContactrefRBSheet?.current as any)?.open();
            }}
            className="p-4 flex-row items-center space-x-2 border-[1px] border-[#E9EAE9]"
          >
            <PhoneIcon />
            <Text>
              {defaultAddressLoading ? (
                <></>
              ) : (
                <>
                  {/* {defaultAddress?.receiver_name} */}
                  {/* {"\n"} */}
                  +91
                  {defaultAddress?.receiver_contact}
                </>
              )}
            </Text>
          </TouchableOpacity>
        </View>
        <Address ref={AddressrefRBSheet} />
        <EditContact ref={EditContactrefRBSheet} />
      </ScrollView>
      <View className="px-4 my-4 flex-row justify-between">
        <TouchableOpacity
          onPress={() => {
            router.push({
              pathname: "/home/<USER>/billtotal",
              params: { TotalAmount: checkoutData?.cart_details?.final_amount },
            });
          }}
          className="space-x-8 bg-[#00660A] flex-row flex-1 items-center justify-around rounded-[4px] min-h-[70px]"
        >
          <View className="" style={{}}>
            <Text className="text-[#fff] font-[400] text-[16px]">Total</Text>
            {checkoutIsLoading ? (
              <></>
            ) : (
              <Text className="text-[#fff] font-[500] text-[18px]">
                ₹ {checkoutData?.cart_details?.final_amount?.toFixed(2)}
              </Text>
            )}
          </View>
          <View className="items-center flex">
            <View className="flex-row space-x-2 items-center" style={{}}>
              <Text className="text-[#fff] font-[400] text-[20px]">
                Place Order
              </Text>
              <Vector />
            </View>
          </View>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export const Address = forwardRef((_props, ref) => {
  const [search, setsearch] = useState("");
  const { data } = useTenStackHook<
    { search_val: string },
    { data: defaultAddressEntity[] }
  >({
    endpoint: "address/customerAddressList",
    canSave: true,
    key: "customerAddressList",
    id: "customerAddressList",
    params: {
      search_val: search,
    },
  });
  const { mutate } = useTenStackMutateD({
    endpoint: "customer/address/setCustomerDefaultAddress",
    invalidateQueriesKey: [
      "customerAddressList",
      "customerDefaultAddress",
      "checkout",
    ],
  });
  return (
    <>
      <RBSheet
        ref={ref}
        customStyles={{
          container: {
            borderRadius: 20,
            flex: 1,
            // minheight: "10%",
          },
          draggableIcon: {
            backgroundColor: "#000",
          },
        }}
        customModalProps={{
          statusBarTranslucent: true,
        }}
        customAvoidingViewProps={{
          enabled: false,
        }}
      >
        <ScrollView>
          <View>
            <View className="p-4">
              <Text className="mt-2 font-[600] text-[16px] leading-[24px]">
                Select an address
              </Text>
            </View>
            <View className="px-4">
              <TouchableOpacity
                onPress={() => {
                  (ref as any).current.close();
                  router.navigate("/home/<USER>/map");
                }}
                className="p-4 flex-row items-center space-x-2 border-[1px] border-[#E9EAE9] justify-between"
              >
                <View className="flex-row items-center space-x-2">
                  <PlusIcon />
                  <Text>Add Address</Text>
                </View>
                <View>
                  <Arrow />
                </View>
              </TouchableOpacity>
            </View>

            <View className="mt-4">
              {Array.isArray(data?.data) && data?.data.length > 0 && (
                <View className="my-8 items-center justify-center">
                  <Text>Saved Locations</Text>
                </View>
              )}

              {Array.isArray(data?.data) &&
                data?.data?.map(
                  (
                    items: {
                      id: unknown;
                      type: unknown;
                      address: unknown;
                      area_sector: unknown;
                      receiver_contact: unknown;
                      is_default: unknown;
                    },
                    index: React.Key | null | undefined,
                  ) => {
                    return (
                      <>
                        <TouchableOpacity
                          className=""
                          onPress={() => {
                            mutate({ id: items?.id as number });
                            (ref as any).current.close();
                          }}
                          key={index}
                        >
                          <AddressComponents
                            id={items.id}
                            name={items.type}
                            address={items.address}
                            address2={items.area_sector}
                            number={items.receiver_contact}
                            forwordIcon={<ForwordIcon />}
                            isDefault={items.is_default}
                            key={index}
                            refreshFunction={() => {}}
                          />
                        </TouchableOpacity>
                      </>
                    );
                  },
                )}
            </View>
          </View>
        </ScrollView>
      </RBSheet>
    </>
  );
});
export const EditContact = forwardRef((props, ref) => {
  const { data: defaultAddress, isLoading: defaultAddressLoading } =
    useDefaultAddressHook();
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm();
  const { mutate, isPending } = useTenStackMutateD({
    endpoint: "customer/address/customerUpdateAddress",
    invalidateQueriesKey: [
      "customerAddressList",
      "checkout",
      "customerDefaultAddress",
      "cartDetails",
    ],
  });
  const UpdateProfileData = async (data: any) => {
    mutate(
      {
        id: defaultAddress?.id,
        type: defaultAddress?.type,
        receiver_name: data.username,
        receiver_contact: data.number,
        address: defaultAddress?.address,
        latitude: defaultAddress?.latitude,
        longitude: defaultAddress?.longitude,
        area_sector: defaultAddress?.area_sector,
        is_default: defaultAddress?.is_default,
      },
      {
        onSuccess: (res) => {
          Alert.alert("Alert", res?.msg);
          (ref as any).current.close();
        },
        onError: (err) => {
          Alert.alert("Alert", err?.msg);
        },
      },
    );
  };
  return (
    <>
      <RBSheet
        ref={ref}
        customStyles={{
          container: {
            borderRadius: 20,
            flex: 1,
          },
          draggableIcon: {
            backgroundColor: "#000",
          },
        }}
        customModalProps={{
          statusBarTranslucent: true,
        }}
        customAvoidingViewProps={{
          enabled: false,
        }}
      >
        <View className="my-6 px-4 flex-1">
          <View className="mt-2">
            <View className="mt-4">
              <Text>Receivers Name</Text>
              <View className="flex-row items-center h-[40px] border-[#ACB9D5] border-[1px] px-2 mt-2 rounded-[4px]">
                <Controller
                  name={"username"}
                  control={control}
                  defaultValue={defaultAddress?.receiver_name}
                  render={({ field: { onChange, onBlur, value } }) => {
                    return (
                      <>
                        <TextInput
                          className="h-full flex-1 pl-1"
                          value={value}
                          onChangeText={(value) => onChange(value)}
                        />
                        <TouchableOpacity>
                          <ContactBook />
                        </TouchableOpacity>
                      </>
                    );
                  }}
                />
              </View>
            </View>
          </View>
          {/* Phone Number TextFile Area */}
          <View>
            <TextinputWithEdit
              text={"Phone Number"}
              placeholder={defaultAddress?.receiver_contact}
              control={control}
              rules={{
                required: {
                  value: true,
                  message: "Phone number is required",
                },
                minLength: {
                  value: 10,
                  message: "Phone number must be 10 digits",
                },
                maxLength: {
                  value: 10,
                  message: "Phone number must be 10 digits",
                },
                pattern: {
                  value: /^[0-9]{10}$/,
                  message: "Please enter a valid 10-digit phone number",
                },
              }}
              name={"number"}
            />
          </View>
          {errors.number && (
            <Text className="text-red-700">{errors.number.message}</Text>
          )}
        </View>
        <View className="px-4 my-3">
          <TouchableOpacity
            onPress={() => {
              handleSubmit(UpdateProfileData)();
            }}
            className="h-[44px] bg-[#00660A] items-center justify-center"
          >
            <Text className="font-[400] text-[16px] leading-[24px] text-[#fff]">
              Submit
            </Text>
          </TouchableOpacity>
        </View>
      </RBSheet>
    </>
  );
});

export const BillData = () => {
  const [showDeliveryDetails, setshowDeliveryDetails] = useState(false);
  const [showTaxDetails, setshowTaxDetails] = useState(false);
  const [showTaxs, setshowTaxs] = useState(false);
  const { data, isFetching: cartDetailsisLoading } = useTenStackHook<any, any>({
    key: "cartDetails",
    canSave: true,
    endpoint: "product/cart_details",
  });
  const { data: checkoutData, isFetching: checkoutIsLoading } = useTenStackHook<
    any,
    any
  >({
    key: "checkout",
    canSave: true,
    endpoint: "product/checkout_page",
  });

  return (
    <>
      {cartDetailsisLoading ? (
        <View className="flex-1 items-center justify-center">
          <ActivityIndicator />
        </View>
      ) : (
        <>
          <View className="my-4 px-4">
            <View>
              <Text className="font-[600] text-[16px] leading-[24]">
                Bill Summary
              </Text>
            </View>
            <View className="mt-2 px-4">
              {/* item Total */}
              <View className="flex-row items-center justify-between my-2">
                <View className="flex-row space-x-2">
                  <BagIcon />
                  <Text>Item Total</Text>
                </View>
                <View>
                  <Text>₹ {data?.cart_details?.item_total?.toFixed(2)}</Text>
                </View>
              </View>

              {/* delivery Details */}
              <>
                <TouchableOpacity
                  onPress={() => {
                    setshowDeliveryDetails(!showDeliveryDetails);
                  }}
                  className="flex-row items-center justify-between my-3"
                >
                  <View className="flex-row space-x-2">
                    <Text>Delivery Charges</Text>
                    {showDeliveryDetails ? (
                      <>
                        <Icon2 width={20} height={20} />
                      </>
                    ) : (
                      <>
                        <Icon3 width={20} height={20} />
                      </>
                    )}
                  </View>
                  <View className="flex-row space-x-2">
                    {data?.cart_details?.delivery_fees_normal_products &&
                    data?.cart_details?.delivery_fees_heavy_products ? (
                      <Text>
                        ₹{" "}
                        {(
                          data?.cart_details?.delivery_fees_normal_products +
                          data?.cart_details?.delivery_fees_heavy_products
                        )?.toFixed(2)}
                      </Text>
                    ) : (
                      <></>
                    )}
                  </View>
                </TouchableOpacity>
                {showDeliveryDetails ? (
                  <>
                    {data?.cart_details?.delivery_fees_normal_products != 0 && (
                      <View className="flex-row items-center justify-between my-2">
                        <View className="flex-row space-x-2">
                          <Scooter />
                          <Text>
                            Delivery fee for{" "}
                            {
                              data?.cart_details
                                ?.delivery_distance_normal_products
                            }{" "}
                            with Normal Vehicle
                          </Text>
                        </View>
                        <View>
                          <Text>
                            ₹{" "}
                            {data?.cart_details?.delivery_fees_normal_products?.toFixed(
                              2,
                            )}
                          </Text>
                        </View>
                      </View>
                    )}
                    {data?.cart_details?.delivery_fees_heavy_products != 0 && (
                      <View className="flex-row items-center justify-between my-2">
                        <View className="flex-row space-x-2">
                          <Icon1 width={20} height={20} />
                          <Text>
                            Delivery fee for{" "}
                            {
                              data?.cart_details
                                ?.delivery_distance_heavy_products
                            }{" "}
                            km with heavy Vehicle
                          </Text>
                        </View>
                        <View>
                          <Text>
                            ₹{" "}
                            {data?.cart_details?.delivery_fees_heavy_products?.toFixed(
                              2,
                            )}
                          </Text>
                        </View>
                      </View>
                    )}
                  </>
                ) : (
                  <></>
                )}
              </>

              {/* Tax and Other */}
              <>
                <TouchableOpacity
                  onPress={() => {
                    setshowTaxDetails(!showTaxDetails);
                  }}
                  className="flex-row items-center justify-between my-3"
                >
                  <View className="flex-row space-x-2">
                    <Text>Tax and Other Charges</Text>
                    {showTaxDetails ? (
                      <>
                        <Icon2 width={20} height={20} />
                      </>
                    ) : (
                      <>
                        <Icon3 width={20} height={20} />
                      </>
                    )}
                  </View>
                  <View className="flex-row space-x-2">
                    {data?.cart_details?.platform_fees &&
                    data?.cart_details?.cash_handling_charges &&
                    data?.cart_details?.gst_total ? (
                      <Text>
                        ₹{" "}
                        {(
                          data?.cart_details?.platform_fees +
                          data?.cart_details?.cash_handling_charges +
                          data?.cart_details?.gst_total
                        )?.toFixed(2)}
                      </Text>
                    ) : (
                      <></>
                    )}
                  </View>
                </TouchableOpacity>
                {showTaxDetails ? (
                  <>
                    <View className="flex-row items-center justify-between my-2">
                      <View className="flex-row space-x-2">
                        <Smartphone />
                        <Text>Platform Charges</Text>
                      </View>
                      <View>
                        <Text>
                          ₹ {data?.cart_details?.platform_fees?.toFixed(2)}
                        </Text>
                      </View>
                    </View>
                    <View className="flex-row items-center justify-between my-2">
                      <View className="flex-row space-x-2">
                        <Icon4 width={20} height={20} />
                        <Text>Payment Gateway Charges</Text>
                      </View>
                      <View>
                        <Text>
                          ₹{" "}
                          {data?.cart_details.payment_gateway_charges?.toFixed(
                            2,
                          )}
                        </Text>
                      </View>
                    </View>
                    <View className="flex-row items-center justify-between my-2">
                      <View className="flex-row space-x-2">
                        <Icon4 width={20} height={20} />
                        <Text>Cash Handling Charges</Text>
                      </View>
                      <View>
                        <Text>
                          ₹{" "}
                          {data?.cart_details.cash_handling_charges?.toFixed(2)}
                        </Text>
                      </View>
                    </View>
                    <>
                      {/* Taxes */}
                      <TouchableOpacity
                        onPress={() => {
                          setshowTaxs(!showTaxs);
                        }}
                        className="flex-row items-center justify-between my-3"
                      >
                        <View className="flex-row space-x-2">
                          <Text>Taxes</Text>
                          {showTaxs ? (
                            <>
                              <Icon2 width={20} height={20} />
                            </>
                          ) : (
                            <>
                              <Icon3 width={20} height={20} />
                            </>
                          )}
                        </View>
                        <View className="flex-row space-x-2"></View>
                      </TouchableOpacity>
                      {showTaxs ? (
                        <>
                          <View className="flex-row items-center justify-between my-2">
                            <View className="flex-row space-x-2">
                              <CoinIcon />
                              <Text>CGST</Text>
                            </View>
                            <View>
                              <Text>
                                ₹ {data?.cart_details?.cgst_total?.toFixed(2)}
                              </Text>
                            </View>
                          </View>
                          <View className="flex-row items-center justify-between my-2">
                            <View className="flex-row space-x-2">
                              <CoinIcon />
                              <Text>SGST</Text>
                            </View>

                            <View>
                              <Text>
                                ₹ {data?.cart_details?.sgst_total?.toFixed(2)}
                              </Text>
                            </View>
                          </View>
                          <View className="flex-row items-center justify-between my-2">
                            <View className="flex-row space-x-2">
                              <CoinIcon />
                              <Text>CESS</Text>
                            </View>

                            <View>
                              <Text>
                                ₹ {data?.cart_details?.cess_total?.toFixed(2)}
                              </Text>
                            </View>
                          </View>
                        </>
                      ) : (
                        <></>
                      )}
                    </>
                  </>
                ) : (
                  <></>
                )}
              </>
              <View className="flex-row items-center justify-between mt-6">
                <View className="flex-row space-x-2">
                  <Text>Grand Total</Text>
                </View>
                <View>
                  <Text>
                    ₹ {checkoutData?.cart_details?.final_amount?.toFixed(2)}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </>
      )}
    </>
  );
};

export const UnpackageProductComponent = (props) => {
  const { control, watch } = useForm();
  const { isLoading: isLoadingCart2, mutate: addunpackageProduct } =
    useTenStackMutateD({
      endpoint: "customer/product/add_update_unpackage_product_cart",
      invalidateQueriesKey: ["cart", "checkout", "cartDetails"],
    });
  return (
    <>
      <View className="flex-row justify-between mt-4 px-5">
        <View className="items-start">
          {props?.image && (
            <Image
              source={{ uri: props?.image }}
              style={{ width: 50, height: 50 }}
            />
          )}
          <View className="items-start">
            <Text className="flex-wrap font-[400] text-[16px]">
              {/* {props?.name} */}
              {props?.name.substring(0, 19)}
              {props?.name.length > 19 && "..."}
            </Text>
            <Text>
              ₹{`${Number(props?.amount).toFixed(2)}/${props?.units}`}
            </Text>
          </View>
        </View>
        <View
          className="flex-col items-end"
          style={{
            alignSelf: "flex-start",
          }}
        >
          <QuantityField
            name1={`number${props?.id}`}
            name2={`unit${props?.id}`}
            control={control}
            defaultprice={`${props?.quantity}`}
            defaultunit={`${props?.units}`}
            onChangefun={() => {
              addunpackageProduct({
                product_id: props?.id,
                unpackage_prd_qty: watch(`number${props?.id}`)
                  ? watch(`number${props?.id}`)
                  : props?.quantity,
                unpackage_prd_unit: watch(`unit${props?.id}`)
                  ? watch(`unit${props?.id}`)
                  : props?.units,
                quantity: 1,
              });
            }}
          />
          <View className="items-end">
            <Text className="mt-2">
              ₹{Number(props?.amount) * Number(props?.quantity)}
            </Text>
          </View>
        </View>
      </View>
    </>
  );
};
export default Index;
