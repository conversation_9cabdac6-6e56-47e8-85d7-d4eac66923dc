import AsyncStorage from "@react-native-async-storage/async-storage";
import BackIcon from "@/assets/OrderDetails/backIcon.svg";
import ButtonComponent from "../../../../component/ButtonComponent";
import CamaraIcon from "@/assets/New folder/New folder/camara.svg";
import Checkbox from "expo-checkbox";
import Constants from "expo-constants";
import JsonPrint from "@/core/print_call/json_print";
import React, { useEffect, useState } from "react";
import axios from "axios";
import resImage from "@/assets/sidemenuassert/Rectangle 592762.png";
import useGetApiDatawithParam from "../../../../hooks/useGetApiDatawithParam";
import useUpdateWithFormData from "../../../../hooks/useUpdateWithFormData";
import { router, useLocalSearchParams } from "expo-router";
import { Controller, useForm } from "react-hook-form";
import { SafeAreaView } from "react-native-safe-area-context";
import { useReportPage } from "../../../../store";
import { ProductCard } from "./orderdetails";

import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Image,
} from "react-native";

const report = () => {
  const { id } = useLocalSearchParams();
  const { ReportImages } = useReportPage((state) => state);
  const {
    control,
    formState: { isValid, errors },
    watch,
    getFieldState,
    handleSubmit,
  } = useForm();
  useEffect(() => {}, []);

  const { data, isLoading, triggerreFresh } = useGetApiDatawithParam({
    endpoint: "order/past_order_details",
    param: { orderId: id },
  });
  let dataLength = 0;

  const [SelectedProduct, setSelectedProduct] = useState([]);

  const SubmitReport = async (formdata) => {
    const token = await AsyncStorage.getItem("login");
    const responce = await axios
      .post(
        `${Constants.expoConfig?.extra?.BASE_URL}order/order_report`,
        formdata,
        {
          headers: {
            "X-Powered-By": "Express",
            "Access-Control-Allow-Origin": "*",
            "content-type": "multipart/form-data",
            Authorization: token,
          },
        },
      )
      .then((val) => {
        JsonPrint(val.data);
        return val.data;
      })
      .catch((error) => {
        console.error(error.response ? error.response.data : error.message);
      });
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView className="flex-1">
        <View className="items-center justify-center relative mt-6">
          <TouchableOpacity
            className="absolute left-[3%]"
            onPress={() => {
              router.back();
            }}
          >
            <BackIcon />
          </TouchableOpacity>
          <Text className="font-[500] text-[19px] leading-[24px]">Report</Text>
        </View>
        <View className="mx-4 mb-6">
          {/* Product List */}
          {Array.isArray(data?.msg?.product) &&
            (() => {
              dataLength = data?.msg?.product.length;
              return true;
            })() &&
            data?.msg?.product?.map((items, index) => {
              return (
                <>
                  <ProductList
                    product_id={items?.id}
                    product_name={items?.name}
                    description={items?.description}
                    price={items?.price}
                    image={items?.image}
                    setSelectedProduct={setSelectedProduct}
                    SelectedProduct={SelectedProduct}
                    control={control}
                    name={`${index}`}
                    dataLength={dataLength}
                    watch={watch}
                    key={index}
                  />
                </>
              );
            })}
          {errors?.[`${dataLength - 1}`]?.message ? (
            <View className="relative">
              <Text className="font-[500] text-[14px] text-[#D13434] leading-[21px]">
                {errors?.[`${dataLength - 1}`]?.message}
              </Text>
            </View>
          ) : (
            <></>
          )}
          {/*  Report Text Field  */}
          <View className="">
            <View className="">
              <Text className="">Description</Text>
            </View>
            <View className="mt-2">
              <Controller
                control={control}
                name={"description"}
                rules={{
                  required: {
                    value: true,
                    message: "Please Enter Description",
                  },
                }}
                render={({ field: { onChange, onBlur, value } }) => {
                  return (
                    <TextInput
                      multiline
                      style={{
                        textAlignVertical: "top",
                      }}
                      scrollEnabled={true}
                      value={value}
                      onChangeText={onChange}
                      className="border-[1px] border-[#ccc] rounded-[5px] h-[150px] px-2 py-2"
                      placeholder="Enter you complaint"
                    />
                  );
                }}
              />
            </View>
            {errors?.description?.message ? (
              <View className="relative">
                <Text className="font-[500] text-[14px] text-[#D13434] leading-[21px]">
                  {errors?.description?.message}
                </Text>
              </View>
            ) : (
              <></>
            )}
          </View>
        </View>
        <View className="flex-row flex flex-wrap gap-5 m-2">
          {ReportImages?.map((images, index) => {
            return (
              <View className="" key={index}>
                <Image source={{ uri: images }} width={70} height={70} />
              </View>
            );
          })}
        </View>
        <View className="mt-8 px-6 mb-6">
          <TouchableOpacity
            onPress={() => {
              router.push("home/sidemenu/pastorder/addphoto");
            }}
            className="flex-row space-x-4 h-[62px] items-center justify-center border-[1px] border-[#00660A] rounded-[100px]"
          >
            <CamaraIcon />
            <Text className="font-[400] text-[20px] font-Pop leading-[30px] text-[#00660A]">
              Add photos and videos
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
      <View className="px-4 flex-row mb-7" style={{ alignSelf: "flex-end" }}>
        <ButtonComponent
          key={1}
          text={"Submit"}
          pressfun={async () => {
            const formdata = new FormData();
            formdata.append("order_id", id);
            formdata.append("product_id", SelectedProduct);
            formdata.append("description", watch("description"));
            formdata.append("file", {
              uri:
                ReportImages.length > 0 &&
                ReportImages[ReportImages.length - 1],
              name:
                ReportImages.length > 0 &&
                ReportImages[ReportImages.length - 1],
              filename:
                ReportImages.length > 0 &&
                ReportImages[ReportImages.length - 1],
              type: "image/jpeg",
            });
            handleSubmit(async () => {
              await SubmitReport(formdata).then(() => {
                router.push("home/sidemenu/pastorder/ReportSuccess");
              });
            })();
          }}
          // disabled={!isValid}
          value={isValid}
          style={{ borderRadius: 8 }}
        />
      </View>
    </SafeAreaView>
  );
};

const ProductList = ({
  control,
  name,
  dataLength,
  watch,
  product_name,
  product_id,
  setSelectedProduct,
  SelectedProduct,
  description,
  price,
  image,
}) => {
  return (
    <Controller
      control={control}
      name={name}
      rules={{}}
      render={({ field: { onChange, onBlur, value = false } }) => {
        return (
          <TouchableOpacity
            onPress={() => {
              onChange(() => {
                if (!value) {
                  setSelectedProduct((state) => {
                    return [...state, product_id];
                  });
                } else if (value) {
                  setSelectedProduct((state) => {
                    return state.filter((id) => {
                      if (id == product_id) {
                        return false;
                      }
                      return true;
                    });
                  });
                }
                return !value;
              });
            }}
            className="flex-row items-center p-6 "
          >
            <View className="flex items-start relative right-5">
              <Checkbox
                value={value}
                onValueChange={() => {
                  onChange(() => {
                    if (!value) {
                      setSelectedProduct((state) => {
                        return [...state, product_id];
                      });
                    } else if (value) {
                      setSelectedProduct((state) => {
                        return state.filter((id) => {
                          if (id == product_id) {
                            return false;
                          }
                          return true;
                        });
                      });
                    }
                    return !value;
                  });
                }}
              />
            </View>
            <ProductCard
              name={product_name}
              price={price}
              image={image}
              description={description}
            />
          </TouchableOpacity>
        );
      }}
    />
  );
};
export default report;
{
  /* <View className="flex-grow">
    <Image source={resImage} />
  </View>
  <View
    className="flex-grow"
    style={{
      alignSelf: "center",
    }}
  >
    <Text>1 X French Vanilla</Text>
    <Text>90 Grams</Text>
  </View> */
}
