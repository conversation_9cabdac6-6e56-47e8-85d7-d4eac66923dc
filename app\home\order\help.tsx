import { View, Text, TouchableOpacity } from "react-native";
import React from "react";
import { SlideComponent } from "../sidemenu/help";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import BackIcon from "../../../assets/OrderDetails/backIcon.svg";

const index = () => {
  return (
    <SafeAreaView className="flex-1 ">
      <View className="items-center justify-center relative mt-6">
        <TouchableOpacity
          className="absolute left-[3%]"
          onPress={() => {
            router.back();
          }}
        >
          <BackIcon />
        </TouchableOpacity>
        <View>
          <Text className="font-[600] text-[20px] leading-[30px]">
            Help and Support
          </Text>
        </View>
      </View>
      <View className="px-4">
        <View className="mt-7">
          <SlideComponent text={"Where is my order?"} />
          <SlideComponent text={"Do you take delivery instructions?"} />
          <SlideComponent
            text={
              "Do you charge any amount or taxes over and above the price of each item?"
            }
          />
          <SlideComponent text={"Can I edit my cart/ Add Items?"} />
          <SlideComponent text={"Can I reschedule my order?"} />
          <SlideComponent text={"Want the invoice/ Pricing break-up?"} />
          <SlideComponent
            text={"Can I change the delivery address of my order?"}
          />
          <SlideComponent text={"Forgot coupon code?"} />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default index;
